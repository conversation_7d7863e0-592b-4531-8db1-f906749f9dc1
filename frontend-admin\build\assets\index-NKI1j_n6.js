import{b6 as s,aW as o}from"./antd-FyCAPQKa.js";import{R as d}from"./react-BUTTOX-3.js";import{P}from"./BaseForm-CUW86psc.js";import{j as l}from"./index-DD0gcXtR.js";var f=["fieldProps","min","proFieldProps","max"],F=function(r,i){var e=r.fieldProps,p=r.min,t=r.proFieldProps,a=r.max,m=s(r,f);return l.jsx(P,o({valueType:"digit",fieldProps:o({min:p,max:a},e),ref:i,filedConfig:{defaultProps:{width:"100%"}},proFieldProps:t},m))},j=d.forwardRef(F);export{j as F};
