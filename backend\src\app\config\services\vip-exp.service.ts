import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VipConfig } from '../entities/vip-config-v2.entity';
import { AppUser } from '../../entities/app-user.entity';

/**
 * VIP EXP经验值计算服务
 * 负责处理用户EXP的计算、累积和等级升级逻辑
 */
@Injectable()
export class VipExpService {
  constructor(
    @InjectRepository(VipConfig)
    private readonly vipConfigRepository: Repository<VipConfig>,
    @InjectRepository(AppUser)
    private readonly appUserRepository: Repository<AppUser>,
  ) {}

  /**
   * 计算用户当前总EXP
   * @param userId 用户ID
   * @returns EXP计算结果详情
   */
  async calculateUserTotalExp(userId: number): Promise<{
    totalExp: number;
    breakdown: {
      realMoneyRecharge: number;
      realMoneyRechargeExp: number;
      realMoneyFlow: number;
      realMoneyFlowExp: number;
      goldPayment: number;
      goldPaymentExp: number;
      goldFlow: number;
      goldFlowExp: number;
      dailyTaskCount: number;
      dailyTaskExp: number;
    };
  }> {
    const user = await this.appUserRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 获取当前用户VIP等级配置
    const vipConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: user.vipLevel, status: 1 },
    });
    if (!vipConfig) {
      throw new NotFoundException('VIP配置不存在');
    }

    // TODO: 这里需要从实际的交易记录表中获取数据
    // 暂时使用模拟数据，实际实现时需要查询相关交易表
    const realMoneyRecharge = 1000; // 从充值记录表获取
    const realMoneyFlow = 5000; // 从游戏流水表获取真金流水
    const goldPayment = 500; // 从金币购买记录获取
    const goldFlow = 50000; // 从游戏流水表获取金币流水
    const dailyTaskCount = 10; // 从任务完成记录获取

    // 计算各部分EXP
    const realMoneyRechargeExp = Math.floor(realMoneyRecharge * vipConfig.realMoneyRechargeExpRatio);
    const realMoneyFlowExp = Math.floor(realMoneyFlow * vipConfig.realMoneyFlowExpRatio);
    const goldPaymentExp = Math.floor(goldPayment * vipConfig.goldPaymentExpRatio);
    const goldFlowExp = Math.floor(goldFlow * vipConfig.goldFlowExpRatio);
    
    // 活跃任务EXP（取配置的中间值）
    const avgTaskExp = (vipConfig.dailyTaskExpMin + vipConfig.dailyTaskExpMax) / 2;
    const dailyTaskExp = Math.floor(dailyTaskCount * avgTaskExp);

    const totalExp = realMoneyRechargeExp + realMoneyFlowExp + goldPaymentExp + goldFlowExp + dailyTaskExp;

    return {
      totalExp,
      breakdown: {
        realMoneyRecharge,
        realMoneyRechargeExp,
        realMoneyFlow,
        realMoneyFlowExp,
        goldPayment,
        goldPaymentExp,
        goldFlow,
        goldFlowExp,
        dailyTaskCount,
        dailyTaskExp,
      },
    };
  }

  /**
   * 添加EXP经验值
   * @param userId 用户ID
   * @param expType EXP类型
   * @param amount 金额或数量
   * @param description 描述
   * @returns 添加的EXP数量
   */
  async addExp(
    userId: number,
    expType: 'real_money_recharge' | 'real_money_flow' | 'gold_payment' | 'gold_flow' | 'daily_task',
    amount: number,
    description?: string
  ): Promise<number> {
    const user = await this.appUserRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 获取当前用户VIP等级配置
    const vipConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: user.vipLevel, status: 1 },
    });
    if (!vipConfig) {
      throw new NotFoundException('VIP配置不存在');
    }

    let expGained = 0;

    // 根据类型计算EXP
    switch (expType) {
      case 'real_money_recharge':
        expGained = Math.floor(amount * vipConfig.realMoneyRechargeExpRatio);
        break;
      case 'real_money_flow':
        expGained = Math.floor(amount * vipConfig.realMoneyFlowExpRatio);
        break;
      case 'gold_payment':
        expGained = Math.floor(amount * vipConfig.goldPaymentExpRatio);
        break;
      case 'gold_flow':
        expGained = Math.floor(amount * vipConfig.goldFlowExpRatio);
        break;
      case 'daily_task':
        // 活跃任务EXP在配置的范围内随机
        const minExp = vipConfig.dailyTaskExpMin;
        const maxExp = vipConfig.dailyTaskExpMax;
        expGained = Math.floor(Math.random() * (maxExp - minExp + 1)) + minExp;
        break;
      default:
        throw new Error('无效的EXP类型');
    }

    // 更新用户EXP
    const newExp = user.vipExp + expGained;
    await this.appUserRepository.update(userId, { vipExp: newExp });

    // TODO: 记录EXP获取日志到专门的表中
    // await this.expLogRepository.save({
    //   userId,
    //   expType,
    //   amount,
    //   expGained,
    //   description,
    //   createTime: new Date(),
    // });

    // 检查是否需要升级VIP等级
    await this.checkAndUpgradeVipLevel(userId);

    return expGained;
  }

  /**
   * 检查并升级用户VIP等级
   * @param userId 用户ID
   * @returns 升级结果
   */
  async checkAndUpgradeVipLevel(userId: number): Promise<{
    upgraded: boolean;
    oldLevel: number;
    newLevel: number;
    currentExp: number;
  }> {
    const user = await this.appUserRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const oldLevel = user.vipLevel;
    const currentExp = user.vipExp;

    // 获取所有启用的VIP配置，按等级降序排列
    const vipConfigs = await this.vipConfigRepository.find({
      where: { status: 1 },
      order: { vipLevel: 'DESC' },
    });

    // 找到用户应该达到的最高等级
    let newLevel = 0;
    for (const config of vipConfigs) {
      if (currentExp >= config.requiredExp) {
        newLevel = config.vipLevel;
        break;
      }
    }

    let upgraded = false;
    if (newLevel > oldLevel) {
      await this.appUserRepository.update(userId, { vipLevel: newLevel });
      upgraded = true;

      // TODO: 发送VIP升级通知
      // await this.notificationService.sendVipUpgradeNotification(userId, oldLevel, newLevel);
    }

    return {
      upgraded,
      oldLevel,
      newLevel,
      currentExp,
    };
  }

  /**
   * 获取用户VIP权益信息
   * @param userId 用户ID
   * @returns VIP权益详情
   */
  async getUserVipBenefits(userId: number): Promise<{
    vipLevel: number;
    levelName: string;
    strategicPosition: string;
    currentExp: number;
    requiredExp: number;
    nextLevelExp: number;
    progressPercent: number;
    benefits: {
      dailyGoldReward: number;
      buybackEnabled: boolean;
      buybackRate: number;
      c2cFeeRate: number;
      rakebackRate: number;
      withdrawalFeeRate: number;
      dailyWithdrawalLimit: number;
      withdrawalPriority: number;
    };
  }> {
    const user = await this.appUserRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const currentConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: user.vipLevel, status: 1 },
    });
    if (!currentConfig) {
      throw new NotFoundException('当前VIP配置不存在');
    }

    // 获取下一级配置
    const nextConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: user.vipLevel + 1, status: 1 },
    });

    const nextLevelExp = nextConfig ? nextConfig.requiredExp : currentConfig.requiredExp;
    const progressPercent = nextConfig 
      ? Math.min(100, (user.vipExp / nextConfig.requiredExp) * 100)
      : 100;

    return {
      vipLevel: user.vipLevel,
      levelName: currentConfig.levelName,
      strategicPosition: currentConfig.strategicPosition || '',
      currentExp: user.vipExp,
      requiredExp: currentConfig.requiredExp,
      nextLevelExp,
      progressPercent: Math.round(progressPercent * 100) / 100,
      benefits: {
        dailyGoldReward: currentConfig.dailyGoldReward,
        buybackEnabled: currentConfig.buybackEnabled,
        buybackRate: currentConfig.buybackRate,
        c2cFeeRate: currentConfig.c2cFeeRate,
        rakebackRate: currentConfig.rakebackRate,
        withdrawalFeeRate: currentConfig.withdrawalFeeRate,
        dailyWithdrawalLimit: currentConfig.dailyWithdrawalLimit,
        withdrawalPriority: currentConfig.withdrawalPriority,
      },
    };
  }

  /**
   * 重新计算所有用户的VIP等级
   * @returns 处理结果统计
   */
  async recalculateAllUserVipLevels(): Promise<{
    processed: number;
    upgraded: number;
    downgraded: number;
  }> {
    const users = await this.appUserRepository.find();
    let processed = 0;
    let upgraded = 0;
    let downgraded = 0;

    for (const user of users) {
      try {
        const result = await this.checkAndUpgradeVipLevel(user.id);
        processed++;
        
        if (result.newLevel > result.oldLevel) {
          upgraded++;
        } else if (result.newLevel < result.oldLevel) {
          downgraded++;
        }
      } catch (error) {
        console.error(`重新计算用户 ${user.id} VIP等级失败:`, error);
      }
    }

    return { processed, upgraded, downgraded };
  }
}
