"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipExpService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const vip_config_v2_entity_1 = require("../entities/vip-config-v2.entity");
const app_user_entity_1 = require("../../entities/app-user.entity");
let VipExpService = class VipExpService {
    vipConfigRepository;
    appUserRepository;
    constructor(vipConfigRepository, appUserRepository) {
        this.vipConfigRepository = vipConfigRepository;
        this.appUserRepository = appUserRepository;
    }
    async calculateUserTotalExp(userId) {
        const user = await this.appUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const vipConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: user.vipLevel, status: 1 },
        });
        if (!vipConfig) {
            throw new common_1.NotFoundException('VIP配置不存在');
        }
        const realMoneyRecharge = 1000;
        const realMoneyFlow = 5000;
        const goldPayment = 500;
        const goldFlow = 50000;
        const dailyTaskCount = 10;
        const realMoneyRechargeExp = Math.floor(realMoneyRecharge * vipConfig.realMoneyRechargeExpRatio);
        const realMoneyFlowExp = Math.floor(realMoneyFlow * vipConfig.realMoneyFlowExpRatio);
        const goldPaymentExp = Math.floor(goldPayment * vipConfig.goldPaymentExpRatio);
        const goldFlowExp = Math.floor(goldFlow * vipConfig.goldFlowExpRatio);
        const avgTaskExp = (vipConfig.dailyTaskExpMin + vipConfig.dailyTaskExpMax) / 2;
        const dailyTaskExp = Math.floor(dailyTaskCount * avgTaskExp);
        const totalExp = realMoneyRechargeExp + realMoneyFlowExp + goldPaymentExp + goldFlowExp + dailyTaskExp;
        return {
            totalExp,
            breakdown: {
                realMoneyRecharge,
                realMoneyRechargeExp,
                realMoneyFlow,
                realMoneyFlowExp,
                goldPayment,
                goldPaymentExp,
                goldFlow,
                goldFlowExp,
                dailyTaskCount,
                dailyTaskExp,
            },
        };
    }
    async addExp(userId, expType, amount, description) {
        const user = await this.appUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const vipConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: user.vipLevel, status: 1 },
        });
        if (!vipConfig) {
            throw new common_1.NotFoundException('VIP配置不存在');
        }
        let expGained = 0;
        switch (expType) {
            case 'real_money_recharge':
                expGained = Math.floor(amount * vipConfig.realMoneyRechargeExpRatio);
                break;
            case 'real_money_flow':
                expGained = Math.floor(amount * vipConfig.realMoneyFlowExpRatio);
                break;
            case 'gold_payment':
                expGained = Math.floor(amount * vipConfig.goldPaymentExpRatio);
                break;
            case 'gold_flow':
                expGained = Math.floor(amount * vipConfig.goldFlowExpRatio);
                break;
            case 'daily_task':
                const minExp = vipConfig.dailyTaskExpMin;
                const maxExp = vipConfig.dailyTaskExpMax;
                expGained = Math.floor(Math.random() * (maxExp - minExp + 1)) + minExp;
                break;
            default:
                throw new Error('无效的EXP类型');
        }
        const newExp = user.vipExp + expGained;
        await this.appUserRepository.update(userId, { vipExp: newExp });
        await this.checkAndUpgradeVipLevel(userId);
        return expGained;
    }
    async checkAndUpgradeVipLevel(userId) {
        const user = await this.appUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const oldLevel = user.vipLevel;
        const currentExp = user.vipExp;
        const vipConfigs = await this.vipConfigRepository.find({
            where: { status: 1 },
            order: { vipLevel: 'DESC' },
        });
        let newLevel = 0;
        for (const config of vipConfigs) {
            if (currentExp >= config.requiredExp) {
                newLevel = config.vipLevel;
                break;
            }
        }
        let upgraded = false;
        if (newLevel > oldLevel) {
            await this.appUserRepository.update(userId, { vipLevel: newLevel });
            upgraded = true;
        }
        return {
            upgraded,
            oldLevel,
            newLevel,
            currentExp,
        };
    }
    async getUserVipBenefits(userId) {
        const user = await this.appUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const currentConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: user.vipLevel, status: 1 },
        });
        if (!currentConfig) {
            throw new common_1.NotFoundException('当前VIP配置不存在');
        }
        const nextConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: user.vipLevel + 1, status: 1 },
        });
        const nextLevelExp = nextConfig ? nextConfig.requiredExp : currentConfig.requiredExp;
        const progressPercent = nextConfig
            ? Math.min(100, (user.vipExp / nextConfig.requiredExp) * 100)
            : 100;
        return {
            vipLevel: user.vipLevel,
            levelName: currentConfig.levelName,
            strategicPosition: currentConfig.strategicPosition || '',
            currentExp: user.vipExp,
            requiredExp: currentConfig.requiredExp,
            nextLevelExp,
            progressPercent: Math.round(progressPercent * 100) / 100,
            benefits: {
                dailyGoldReward: currentConfig.dailyGoldReward,
                buybackEnabled: currentConfig.buybackEnabled,
                buybackRate: currentConfig.buybackRate,
                c2cFeeRate: currentConfig.c2cFeeRate,
                rakebackRate: currentConfig.rakebackRate,
                withdrawalFeeRate: currentConfig.withdrawalFeeRate,
                dailyWithdrawalLimit: currentConfig.dailyWithdrawalLimit,
                withdrawalPriority: currentConfig.withdrawalPriority,
            },
        };
    }
    async recalculateAllUserVipLevels() {
        const users = await this.appUserRepository.find();
        let processed = 0;
        let upgraded = 0;
        let downgraded = 0;
        for (const user of users) {
            try {
                const result = await this.checkAndUpgradeVipLevel(user.id);
                processed++;
                if (result.newLevel > result.oldLevel) {
                    upgraded++;
                }
                else if (result.newLevel < result.oldLevel) {
                    downgraded++;
                }
            }
            catch (error) {
                console.error(`重新计算用户 ${user.id} VIP等级失败:`, error);
            }
        }
        return { processed, upgraded, downgraded };
    }
};
exports.VipExpService = VipExpService;
exports.VipExpService = VipExpService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vip_config_v2_entity_1.VipConfig)),
    __param(1, (0, typeorm_1.InjectRepository)(app_user_entity_1.AppUser)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], VipExpService);
//# sourceMappingURL=vip-exp.service.js.map