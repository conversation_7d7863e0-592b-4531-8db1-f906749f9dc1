# VIP系统V12.0代码清理指南

## 概述
本文档指导如何安全地清理旧版VIP系统代码，为新的经济权益系统让路。

## 清理步骤

### 1. 备份重要数据
在开始清理前，确保已完成数据迁移并备份：
```sql
-- 备份旧VIP配置
CREATE TABLE vip_configs_backup_v11 AS SELECT * FROM vip_configs;

-- 备份用户VIP数据
CREATE TABLE app_users_vip_backup_v11 AS 
SELECT id, vip_level, vip_points, total_recharge_amount, total_cash_consumption, total_gold_consumption 
FROM app_users;
```

### 2. 标记废弃的文件

#### 2.1 后端文件（保留但标记废弃）
以下文件在V12.0中被新版本替代，建议添加废弃标记：

```typescript
// backend/src/app/config/vip-config.service.ts
/**
 * @deprecated 此服务已被 VipConfigV2Service 替代
 * 请使用 backend/src/app/config/services/vip-config-v2.service.ts
 * 计划在 V13.0 版本中移除
 */
```

```typescript
// backend/src/app/config/vip-config.controller.ts  
/**
 * @deprecated 此控制器已被 VipConfigV2Controller 替代
 * 请使用 backend/src/app/config/controllers/vip-config-v2.controller.ts
 * 计划在 V13.0 版本中移除
 */
```

```typescript
// backend/src/app/config/entities/vip-config.entity.ts
/**
 * @deprecated 此实体已被 VipConfigV2Entity 替代
 * 请使用 backend/src/app/config/entities/vip-config-v2.entity.ts
 * 计划在 V13.0 版本中移除
 */
```

#### 2.2 前端文件（保留但标记废弃）
```typescript
// frontend-admin/src/pages/config/vip/index.tsx
/**
 * @deprecated 此页面已被 VIP V2 页面替代
 * 请使用 frontend-admin/src/pages/config/vip-v2/index.tsx
 * 计划在 V13.0 版本中移除
 */
```

### 3. 更新路由配置

#### 3.1 后端路由
在主模块中保持两个版本并存：
```typescript
// backend/src/app/config/config.module.ts
@Module({
  imports: [
    VipConfigModule,     // 旧版本 - 标记废弃
    VipConfigV2Module,   // 新版本 - 主要使用
    // ... 其他模块
  ],
  exports: [
    VipConfigModule,     // 保持向后兼容
    VipConfigV2Module,   // 新版本导出
    // ... 其他模块
  ],
})
export class ConfigModule {}
```

#### 3.2 前端路由
```typescript
// frontend-admin/src/router/config.ts
{
  path: '/config/vip',
  name: 'VipConfig',
  component: () => import('@/pages/config/vip/index.tsx'),
  meta: { 
    title: 'VIP配置 (旧版)', 
    deprecated: true,
    redirectTo: '/config/vip-v2'
  }
},
{
  path: '/config/vip-v2',
  name: 'VipConfigV2', 
  component: () => import('@/pages/config/vip-v2/index.tsx'),
  meta: { title: 'VIP经济权益配置 V12.0' }
}
```

### 4. 数据库表处理

#### 4.1 重命名旧表（可选）
```sql
-- 重命名旧表以保留历史数据
ALTER TABLE vip_configs RENAME TO vip_configs_v11_deprecated;

-- 新表已通过迁移脚本创建
-- vip_configs (新表) 已包含完整的V12.0结构
```

#### 4.2 创建视图保持兼容性
```sql
-- 创建兼容性视图，使旧代码仍能工作
CREATE VIEW vip_configs_legacy AS
SELECT 
    id,
    vip_level,
    level_name,
    required_exp as required_points, -- 映射字段名
    daily_gold_reward,
    status,
    create_time,
    update_time
FROM vip_configs
WHERE status = 1;
```

### 5. API版本管理

#### 5.1 添加版本前缀
```typescript
// 旧版本API保持不变，添加废弃警告
@Controller('config/vip')
@ApiTags('VIP配置管理 (已废弃)')
@ApiDeprecated('请使用 /config/vip-v2 接口')
export class VipConfigController {
  // 保持现有实现，添加废弃日志
}

// 新版本API
@Controller('config/vip-v2') 
@ApiTags('VIP经济权益配置管理 V12.0')
export class VipConfigV2Controller {
  // 新实现
}
```

#### 5.2 响应头添加废弃警告
```typescript
// 在旧版本控制器中添加
@UseInterceptors(DeprecationWarningInterceptor)
export class VipConfigController {
  // 现有方法
}

// 创建废弃警告拦截器
@Injectable()
export class DeprecationWarningInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const response = context.switchToHttp().getResponse();
    response.setHeader('X-API-Deprecated', 'true');
    response.setHeader('X-API-Sunset', '2025-12-31'); // 计划移除日期
    response.setHeader('X-API-Replacement', '/config/vip-v2');
    
    return next.handle();
  }
}
```

### 6. 配置文件更新

#### 6.1 环境变量
```env
# .env
# VIP系统配置
VIP_SYSTEM_VERSION=v12.0
VIP_LEGACY_SUPPORT=true
VIP_LEGACY_SUNSET_DATE=2025-12-31
```

#### 6.2 功能开关
```typescript
// config/features.ts
export const FEATURE_FLAGS = {
  VIP_V2_ENABLED: true,
  VIP_LEGACY_ENABLED: true, // 保持向后兼容
  VIP_MIGRATION_MODE: false, // 迁移完成后设为false
};
```

### 7. 文档更新

#### 7.1 API文档
- 在Swagger中标记旧接口为废弃
- 添加新接口的完整文档
- 提供迁移指南

#### 7.2 README更新
```markdown
## VIP系统版本说明

### V12.0 (当前版本)
- 路径: `/config/vip-v2`
- 特性: 完整的经济权益系统，EXP经验值机制
- 状态: ✅ 活跃开发

### V11.0 (已废弃)
- 路径: `/config/vip`  
- 特性: 基础积分系统
- 状态: ⚠️ 已废弃，计划2025年12月31日移除
- 迁移: 请使用V12.0版本
```

### 8. 监控和日志

#### 8.1 使用情况监控
```typescript
// 添加使用情况统计
@Injectable()
export class VipUsageTracker {
  trackLegacyApiUsage(endpoint: string, userId: number) {
    this.logger.warn(`Legacy VIP API used: ${endpoint} by user ${userId}`);
    // 发送到监控系统
  }
}
```

#### 8.2 迁移进度跟踪
```sql
-- 创建迁移进度表
CREATE TABLE vip_migration_progress (
    id SERIAL PRIMARY KEY,
    migration_type VARCHAR(50),
    total_records INTEGER,
    processed_records INTEGER,
    success_records INTEGER,
    error_records INTEGER,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR(20)
);
```

### 9. 测试策略

#### 9.1 兼容性测试
- 确保旧API仍能正常工作
- 验证数据迁移的完整性
- 测试新旧系统的数据一致性

#### 9.2 回滚计划
```sql
-- 紧急回滚脚本
-- 1. 恢复旧表
DROP TABLE IF EXISTS vip_configs;
ALTER TABLE vip_configs_v11_deprecated RENAME TO vip_configs;

-- 2. 恢复用户数据
UPDATE app_users 
SET vip_level = backup.vip_level,
    vip_points = backup.vip_points
FROM app_users_vip_backup_v11 backup
WHERE app_users.id = backup.id;
```

### 10. 清理时间表

#### 阶段1: 准备期 (当前)
- ✅ 部署V12.0系统
- ✅ 完成数据迁移
- ✅ 标记旧代码为废弃

#### 阶段2: 过渡期 (1-3个月)
- 🔄 监控新系统稳定性
- 🔄 收集用户反馈
- 🔄 修复发现的问题

#### 阶段3: 清理期 (3-6个月后)
- ⏳ 移除旧版本API
- ⏳ 删除废弃代码
- ⏳ 清理数据库表

## 注意事项

1. **渐进式清理**: 不要一次性删除所有旧代码，采用渐进式方法
2. **监控告警**: 设置监控确保新系统稳定运行
3. **用户通知**: 提前通知用户API变更和废弃计划
4. **文档同步**: 保持文档与代码变更同步
5. **测试覆盖**: 确保充分的测试覆盖新旧系统交互

## 联系方式

如有问题，请联系：
- 后端团队负责人
- 系统架构师
- DevOps团队
