import{j as e}from"./index-DD0gcXtR.js";import{a as T}from"./react-BUTTOX-3.js";import{D as Z,C as ee,P as se}from"./react-beautiful-dnd.esm-CIUISnkR.js";import{aq as B,ao as b,ap as d,Q as S,T as le,aR as z,an as x,d as f,u as A,aS as L,E as V,aA as re,n as q,aC as ie,S as X,aE as ae,s as M,M as Y,av as r,al as H,I as w,p as U,q as y,C as $,aF as te,aB as de}from"./antd-FyCAPQKa.js";const{Option:N}=U,{TextArea:ne}=w,{Text:m,Title:oe}=le,{RangePicker:ce}=te,G=[{value:"announcement",label:"系统公告",color:"blue"},{value:"promotion",label:"活动推广",color:"green"},{value:"customer_service",label:"客服咨询",color:"orange"},{value:"checkin_reminder",label:"签到提醒",color:"purple"},{value:"recharge_bonus",label:"充值优惠",color:"red"}],J=[{value:"internal_route",label:"内部路由"},{value:"iframe_page",label:"iframe页面"}],xe=({visible:j,onCancel:n,onOk:h,content:i,mode:t})=>{const[c]=r.useForm(),[O,p]=T.useState(!1);T.useEffect(()=>{var l,u,v,k,R,C,P,D;j&&(t==="edit"&&i?c.setFieldsValue({...i,displayTimeRange:(l=i.displayTime)!=null&&l.startTime&&((u=i.displayTime)!=null&&u.endTime)?[H(i.displayTime.startTime),H(i.displayTime.endTime)]:void 0,backgroundColor:(v=i.style)==null?void 0:v.backgroundColor,textColor:(k=i.style)==null?void 0:k.textColor,width:(R=i.style)==null?void 0:R.width,height:(C=i.style)==null?void 0:C.height,borderRadius:(P=i.style)==null?void 0:P.borderRadius,duration:(D=i.displayTime)==null?void 0:D.duration}):(c.resetFields(),c.setFieldsValue({id:`float_${Date.now()}`,type:"announcement",position:{x:50,y:20},status:1,sortOrder:1})))},[j,t,i,c]);const F=async()=>{try{const l=await c.validateFields(),u={id:l.id,type:l.type,title:l.title,content:l.content,imageUrl:l.imageUrl,jumpType:l.jumpType,jumpTarget:l.jumpTarget,position:l.position,style:{width:l.width,height:l.height,backgroundColor:l.backgroundColor,textColor:l.textColor,borderRadius:l.borderRadius},displayTime:l.displayTimeRange?{startTime:l.displayTimeRange[0].format("YYYY-MM-DD HH:mm:ss"),endTime:l.displayTimeRange[1].format("YYYY-MM-DD HH:mm:ss"),duration:l.duration}:{duration:l.duration},sortOrder:l.sortOrder,status:l.status?1:0};h(u)}catch(l){console.error("表单验证失败:",l)}},I=()=>{c.getFieldsValue(),p(!0)};return e.jsxs(e.Fragment,{children:[e.jsx(Y,{title:t==="create"?"添加浮点内容":"编辑浮点内容",open:j,onCancel:n,onOk:F,width:800,okText:"确定",cancelText:"取消",children:e.jsxs(r,{form:c,layout:"vertical",initialValues:{status:!0,position:{x:50,y:20},sortOrder:1},children:[e.jsxs(b,{gutter:16,children:[e.jsx(d,{span:12,children:e.jsx(r.Item,{name:"id",label:"唯一标识",rules:[{required:!0,message:"请输入唯一标识"}],children:e.jsx(w,{placeholder:"如: float_1",disabled:t==="edit"})})}),e.jsx(d,{span:12,children:e.jsx(r.Item,{name:"type",label:"内容类型",rules:[{required:!0,message:"请选择内容类型"}],children:e.jsx(U,{placeholder:"选择内容类型",children:G.map(l=>e.jsx(N,{value:l.value,children:e.jsx(x,{color:l.color,children:l.label})},l.value))})})})]}),e.jsx(r.Item,{name:"title",label:"标题",rules:[{required:!0,message:"请输入标题"},{max:100,message:"标题长度不能超过100字符"}],children:e.jsx(w,{placeholder:"输入浮点内容标题"})}),e.jsx(r.Item,{name:"content",label:"内容",rules:[{required:!0,message:"请输入内容"},{max:500,message:"内容长度不能超过500字符"}],children:e.jsx(ne,{rows:3,placeholder:"输入浮点内容详情"})}),e.jsxs(b,{gutter:16,children:[e.jsx(d,{span:12,children:e.jsx(r.Item,{name:"imageUrl",label:"图片URL",children:e.jsx(w,{placeholder:"输入图片URL（可选）"})})}),e.jsx(d,{span:12,children:e.jsx(r.Item,{name:"jumpType",label:"跳转类型",children:e.jsx(U,{placeholder:"选择跳转类型（可选）",allowClear:!0,children:J.map(l=>e.jsx(N,{value:l.value,children:l.label},l.value))})})})]}),e.jsx(r.Item,{name:"jumpTarget",label:"跳转目标",children:e.jsx(w,{placeholder:"输入跳转目标路径或URL（可选）"})}),e.jsxs(b,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(r.Item,{name:["position","x"],label:"X坐标(%)",rules:[{required:!0,message:"请输入X坐标"}],children:e.jsx(y,{min:0,max:100,style:{width:"100%"}})})}),e.jsx(d,{span:8,children:e.jsx(r.Item,{name:["position","y"],label:"Y坐标(%)",rules:[{required:!0,message:"请输入Y坐标"}],children:e.jsx(y,{min:0,max:100,style:{width:"100%"}})})}),e.jsx(d,{span:8,children:e.jsx(r.Item,{name:"sortOrder",label:"排序",rules:[{required:!0,message:"请输入排序"}],children:e.jsx(y,{min:1,max:999,style:{width:"100%"}})})})]}),e.jsxs(b,{gutter:16,children:[e.jsx(d,{span:6,children:e.jsx(r.Item,{name:"width",label:"宽度(px)",children:e.jsx(y,{min:50,max:500,style:{width:"100%"}})})}),e.jsx(d,{span:6,children:e.jsx(r.Item,{name:"height",label:"高度(px)",children:e.jsx(y,{min:30,max:300,style:{width:"100%"}})})}),e.jsx(d,{span:6,children:e.jsx(r.Item,{name:"backgroundColor",label:"背景色",children:e.jsx($,{showText:!0})})}),e.jsx(d,{span:6,children:e.jsx(r.Item,{name:"textColor",label:"文字颜色",children:e.jsx($,{showText:!0})})})]}),e.jsxs(b,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(r.Item,{name:"borderRadius",label:"圆角(px)",children:e.jsx(y,{min:0,max:50,style:{width:"100%"}})})}),e.jsx(d,{span:8,children:e.jsx(r.Item,{name:"duration",label:"显示时长(秒)",children:e.jsx(y,{min:1,max:300,style:{width:"100%"}})})}),e.jsx(d,{span:8,children:e.jsx(r.Item,{name:"status",label:"启用状态",valuePropName:"checked",children:e.jsx(X,{checkedChildren:"启用",unCheckedChildren:"禁用"})})})]}),e.jsx(r.Item,{name:"displayTimeRange",label:"显示时间范围",children:e.jsx(ce,{showTime:!0,format:"YYYY-MM-DD HH:mm:ss",placeholder:["开始时间","结束时间"],style:{width:"100%"}})}),e.jsx(r.Item,{children:e.jsxs(S,{children:[e.jsx(f,{icon:e.jsx(de,{}),onClick:I,children:"预览效果"}),e.jsxs(m,{type:"secondary",children:[e.jsx(L,{})," 预览功能可以查看浮点内容的显示效果"]})]})})]})}),e.jsx(Y,{title:"浮点内容预览",open:O,onCancel:()=>p(!1),footer:null,width:600,children:e.jsx("div",{style:{position:"relative",width:"100%",height:300,backgroundColor:"#f0f0f0",border:"1px dashed #d9d9d9"},children:e.jsx(m,{type:"secondary",style:{position:"absolute",top:10,left:10},children:"预览区域 (模拟APP界面)"})})})]})},ue=({value:j=[],onChange:n,disabled:h=!1,maxCount:i=3})=>{const[t,c]=T.useState(j),[O,p]=T.useState(!1),[F,I]=T.useState(),[l,u]=T.useState("create");T.useEffect(()=>{c(j)},[j]);const v=()=>{if(t.length>=i){M.warning(`最多只能添加${i}个浮点内容`);return}I(void 0),u("create"),p(!0)},k=a=>{I(a),u("edit"),p(!0)},R=a=>{Y.confirm({title:"确认删除",content:"确定要删除这个浮点内容吗？",onOk:()=>{const s=t.filter(o=>o.id!==a);c(s),n==null||n(s),M.success("删除成功")}})},C=a=>{let s;l==="create"?s=[...t,a]:s=t.map(o=>o.id===a.id?a:o),c(s),n==null||n(s),p(!1),M.success(l==="create"?"添加成功":"更新成功")},P=a=>{const s=t.map(o=>o.id===a?{...o,status:o.status===1?0:1}:o);c(s),n==null||n(s)},D=a=>{if(!a.destination)return;const s=Array.from(t),[o]=s.splice(a.source.index,1);s.splice(a.destination.index,0,o);const g=s.map((_,E)=>({..._,sortOrder:E+1}));c(g),n==null||n(g)},Q=()=>{Y.confirm({title:"确认清空",content:"确定要清空所有浮点内容吗？此操作不可恢复。",onOk:()=>{c([]),n==null||n([]),M.success("已清空所有浮点内容")}})},K=a=>{const s=G.find(o=>o.value===a);return s?e.jsx(x,{color:s.color,children:s.label}):e.jsx(x,{children:a})};return e.jsxs("div",{children:[e.jsxs(B,{size:"small",style:{marginBottom:16},children:[e.jsxs(b,{justify:"space-between",align:"middle",children:[e.jsx(d,{children:e.jsxs(S,{children:[e.jsxs(oe,{level:5,style:{margin:0},children:[e.jsx(z,{})," 浮点内容配置"]}),e.jsxs(x,{color:t.length<=i?"green":"red",children:[t.length,"/",i," 个内容"]}),t.length>i&&e.jsx(x,{color:"red",children:"超出限制"})]})}),e.jsx(d,{children:e.jsxs(S,{children:[e.jsx(f,{type:"primary",size:"small",icon:e.jsx(A,{}),onClick:v,disabled:h||t.length>=i,children:"添加浮点内容"}),e.jsx(f,{danger:!0,size:"small",onClick:Q,disabled:h||t.length===0,children:"清空全部"})]})})]}),e.jsx("div",{style:{marginTop:12,padding:8,backgroundColor:"#f6ffed",borderRadius:4},children:e.jsxs(m,{type:"secondary",children:[e.jsx(L,{})," 浮点内容将在APP首页以浮层形式显示，支持定时显示、位置自定义等功能"]})})]}),t.length===0?e.jsx(B,{children:e.jsx(V,{image:V.PRESENTED_IMAGE_SIMPLE,description:"暂无浮点内容配置",style:{margin:"40px 0"},children:e.jsx(f,{type:"primary",icon:e.jsx(A,{}),onClick:v,children:"添加第一个浮点内容"})})}):e.jsx(Z,{onDragEnd:D,children:e.jsx(ee,{droppableId:"float-contents",children:a=>e.jsxs("div",{...a.droppableProps,ref:a.innerRef,children:[t.map((s,o)=>e.jsx(se,{draggableId:s.id,index:o,isDragDisabled:h,children:(g,_)=>{var E;return e.jsx("div",{ref:g.innerRef,...g.draggableProps,style:{...g.draggableProps.style,marginBottom:16},children:e.jsx(B,{size:"small",title:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8},children:[e.jsx("div",{...g.dragHandleProps,children:e.jsx(z,{style:{color:"#999",cursor:"grab"}})}),e.jsx("span",{children:s.title}),K(s.type),e.jsx(x,{color:s.status===1?"green":"red",children:s.status===1?"启用":"禁用"})]}),extra:e.jsxs(S,{children:[e.jsx(q,{title:"编辑",children:e.jsx(f,{type:"text",size:"small",icon:e.jsx(ie,{}),onClick:()=>k(s),disabled:h})}),e.jsx(q,{title:s.status===1?"禁用":"启用",children:e.jsx(X,{size:"small",checked:s.status===1,onChange:()=>P(s.id),disabled:h})}),e.jsx(q,{title:"删除",children:e.jsx(f,{type:"text",size:"small",danger:!0,icon:e.jsx(ae,{}),onClick:()=>R(s.id),disabled:h})})]}),style:{backgroundColor:_.isDragging?"#f0f0f0":"#fff"},children:e.jsxs(b,{gutter:16,children:[e.jsxs(d,{span:12,children:[e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(m,{strong:!0,children:"内容："}),e.jsx("div",{style:{marginTop:4},children:e.jsx(m,{children:s.content})})]}),s.imageUrl&&e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(m,{strong:!0,children:"图片："}),e.jsx("div",{style:{marginTop:4},children:e.jsx(re,{src:s.imageUrl,alt:"浮点内容图片",width:60,height:40,style:{objectFit:"cover",borderRadius:4}})})]}),s.jumpType&&s.jumpTarget&&e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(m,{strong:!0,children:"跳转："}),e.jsxs("div",{style:{marginTop:4},children:[e.jsx(x,{color:"blue",children:(E=J.find(W=>W.value===s.jumpType))==null?void 0:E.label}),e.jsx(m,{type:"secondary",children:s.jumpTarget})]})]})]}),e.jsxs(d,{span:12,children:[e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(m,{strong:!0,children:"位置："}),e.jsxs("div",{style:{marginTop:4},children:[e.jsxs(x,{children:["X: ",s.position.x,"%"]}),e.jsxs(x,{children:["Y: ",s.position.y,"%"]}),e.jsxs(x,{children:["排序: ",s.sortOrder]})]})]}),s.style&&e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(m,{strong:!0,children:"样式："}),e.jsxs("div",{style:{marginTop:4},children:[s.style.width&&e.jsxs(x,{children:["宽: ",s.style.width,"px"]}),s.style.height&&e.jsxs(x,{children:["高: ",s.style.height,"px"]}),s.style.backgroundColor&&e.jsx(x,{color:s.style.backgroundColor,children:"背景色"})]})]}),s.displayTime&&e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(m,{strong:!0,children:"显示时间："}),e.jsxs("div",{style:{marginTop:4},children:[s.displayTime.duration&&e.jsxs(x,{color:"orange",children:["时长: ",s.displayTime.duration,"秒"]}),s.displayTime.startTime&&s.displayTime.endTime&&e.jsx("div",{style:{marginTop:2},children:e.jsxs(m,{type:"secondary",style:{fontSize:12},children:[s.displayTime.startTime," ~ ",s.displayTime.endTime]})})]})]})]})]})})})}},s.id)),a.placeholder]})})}),e.jsx(xe,{visible:O,onCancel:()=>p(!1),onOk:C,content:F,mode:l})]})};export{ue as default};
