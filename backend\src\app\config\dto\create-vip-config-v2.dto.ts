import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsBoolean, IsOptional, Min, Max, Length } from 'class-validator';

export class CreateVipConfigDto {
  @ApiProperty({ description: 'VIP等级，从0开始', example: 1 })
  @IsNotEmpty({ message: 'VIP等级不能为空' })
  @IsNumber({}, { message: 'VIP等级必须是数字' })
  @Min(0, { message: 'VIP等级不能小于0' })
  @Max(99, { message: 'VIP等级不能大于99' })
  vipLevel: number;

  @ApiProperty({ description: '等级名称', example: '活跃认证' })
  @IsNotEmpty({ message: '等级名称不能为空' })
  @IsString({ message: '等级名称必须是字符串' })
  @Length(1, 50, { message: '等级名称长度必须在1-50个字符之间' })
  levelName: string;

  @ApiProperty({ description: '核心战略定位描述', example: '活跃认证用户', required: false })
  @IsOptional()
  @IsString({ message: '战略定位必须是字符串' })
  @Length(0, 200, { message: '战略定位长度不能超过200个字符' })
  strategicPosition?: string;

  @ApiProperty({ description: '达到该等级所需的累计EXP经验值', example: 500 })
  @IsNotEmpty({ message: '所需EXP不能为空' })
  @IsNumber({}, { message: '所需EXP必须是数字' })
  @Min(0, { message: '所需EXP不能小于0' })
  requiredExp: number;

  // ==================== EXP获取规则配置 ====================
  @ApiProperty({ description: '真金充值EXP比例 (1 INR = X EXP)', example: 10.0000, required: false })
  @IsOptional()
  @IsNumber({}, { message: '真金充值EXP比例必须是数字' })
  @Min(0, { message: '真金充值EXP比例不能小于0' })
  realMoneyRechargeExpRatio?: number;

  @ApiProperty({ description: '真金流水EXP比例 (1 INR = X EXP)', example: 1.0000, required: false })
  @IsOptional()
  @IsNumber({}, { message: '真金流水EXP比例必须是数字' })
  @Min(0, { message: '真金流水EXP比例不能小于0' })
  realMoneyFlowExpRatio?: number;

  @ApiProperty({ description: '金币付费EXP比例 (1 INR = X EXP)', example: 5.0000, required: false })
  @IsOptional()
  @IsNumber({}, { message: '金币付费EXP比例必须是数字' })
  @Min(0, { message: '金币付费EXP比例不能小于0' })
  goldPaymentExpRatio?: number;

  @ApiProperty({ description: '金币流水EXP比例 (X金币 = 1 EXP)', example: 0.001000, required: false })
  @IsOptional()
  @IsNumber({}, { message: '金币流水EXP比例必须是数字' })
  @Min(0, { message: '金币流水EXP比例不能小于0' })
  goldFlowExpRatio?: number;

  @ApiProperty({ description: '活跃任务最小EXP', example: 10, required: false })
  @IsOptional()
  @IsNumber({}, { message: '活跃任务最小EXP必须是数字' })
  @Min(0, { message: '活跃任务最小EXP不能小于0' })
  dailyTaskExpMin?: number;

  @ApiProperty({ description: '活跃任务最大EXP', example: 50, required: false })
  @IsOptional()
  @IsNumber({}, { message: '活跃任务最大EXP必须是数字' })
  @Min(0, { message: '活跃任务最大EXP不能小于0' })
  dailyTaskExpMax?: number;

  // ==================== 每日奖励系统 ====================
  @ApiProperty({ description: '每日可领取金币数量', example: 50, required: false })
  @IsOptional()
  @IsNumber({}, { message: '每日金币奖励必须是数字' })
  @Min(0, { message: '每日金币奖励不能小于0' })
  dailyGoldReward?: number;

  // ==================== 官方回购系统权益 ====================
  @ApiProperty({ description: '是否开启官方回购资格', example: false, required: false })
  @IsOptional()
  @IsBoolean({ message: '回购资格必须是布尔值' })
  buybackEnabled?: boolean;

  @ApiProperty({ description: '官方回购价格 (金币:1 INR)，如140表示140:1', example: 140, required: false })
  @IsOptional()
  @IsNumber({}, { message: '回购价格必须是数字' })
  @Min(1, { message: '回购价格不能小于1' })
  buybackRate?: number;

  // ==================== C2C交易系统权益 ====================
  @ApiProperty({ description: 'C2C手续费率(%)', example: 15.00, required: false })
  @IsOptional()
  @IsNumber({}, { message: 'C2C手续费率必须是数字' })
  @Min(0, { message: 'C2C手续费率不能小于0' })
  @Max(100, { message: 'C2C手续费率不能大于100' })
  c2cFeeRate?: number;

  // ==================== 真金系统权益 ====================
  @ApiProperty({ description: '周返水比例(%)', example: 0.50, required: false })
  @IsOptional()
  @IsNumber({}, { message: '返水比例必须是数字' })
  @Min(0, { message: '返水比例不能小于0' })
  @Max(100, { message: '返水比例不能大于100' })
  rakebackRate?: number;

  @ApiProperty({ description: '提现手续费率(%)', example: 5.00, required: false })
  @IsOptional()
  @IsNumber({}, { message: '提现手续费率必须是数字' })
  @Min(0, { message: '提现手续费率不能小于0' })
  @Max(100, { message: '提现手续费率不能大于100' })
  withdrawalFeeRate?: number;

  @ApiProperty({ description: '每日提现额度上限(INR)，null表示无上限', example: 2000, required: false })
  @IsOptional()
  @IsNumber({}, { message: '提现额度必须是数字' })
  @Min(1, { message: '提现额度不能小于1' })
  dailyWithdrawalLimit?: number;

  @ApiProperty({ description: '提现优先级 1-普通 2-优先 3-VIP专享', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '提现优先级必须是数字' })
  @Min(1, { message: '提现优先级不能小于1' })
  @Max(3, { message: '提现优先级不能大于3' })
  withdrawalPriority?: number;

  // ==================== 解锁机制配置 ====================
  @ApiProperty({ description: '活跃任务是否需要游戏局数解锁', example: false, required: false })
  @IsOptional()
  @IsBoolean({ message: '任务解锁要求必须是布尔值' })
  taskUnlockRequired?: boolean;

  @ApiProperty({ description: '解锁所需游戏局数', example: 0, required: false })
  @IsOptional()
  @IsNumber({}, { message: '解锁游戏局数必须是数字' })
  @Min(0, { message: '解锁游戏局数不能小于0' })
  taskUnlockGamesRequired?: number;

  // ==================== 状态和管理字段 ====================
  @ApiProperty({ description: '状态：1-启用，0-禁用', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '状态必须是数字' })
  @Min(0, { message: '状态值必须是0或1' })
  @Max(1, { message: '状态值必须是0或1' })
  status?: number;

  @ApiProperty({ description: '备注说明', example: '活跃认证用户配置', required: false })
  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  @Length(0, 1000, { message: '备注长度不能超过1000个字符' })
  remark?: string;
}
