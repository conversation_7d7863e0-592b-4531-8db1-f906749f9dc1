import{j as e}from"./index-DD0gcXtR.js";import{a as o}from"./react-BUTTOX-3.js";import{B as he}from"./index-C1Do55qr.js";import{e as xe,f as pe,g as me,h as ue,i as G,j as je,k as ge}from"./index-B0R7VueJ.js";import{f as fe}from"./index-FIiDB76q.js";import ye from"./BoxModeForm-DwBc3Hr7.js";import{av as m,aq as Ce,ao as j,ap as i,T as Te,ab as Ae,D as f,I as E,p as L,Q as N,d as x,u as we,ak as Ie,az as Se,M as H,an as c,s as n,n as y,aB as be,aC as ke,S as ve,aD as Oe,aE as Pe}from"./antd-FyCAPQKa.js";import"./GameCategoryConfig-BAVKjy8l.js";import"./react-beautiful-dnd.esm-CIUISnkR.js";const{Option:Be}=L,{TextArea:$e}=E,{Title:g,Text:r}=Te,$=[{value:1,label:"启用",color:"green"},{value:0,label:"禁用",color:"red"}],Ee=()=>{var D,F,R,z,M,V;const[C,u]=o.useState(!1),[X,q]=o.useState([]),[Q,U]=o.useState(0),[T,A]=o.useState(1),[w,_]=o.useState(10),[I]=m.useForm(),[p]=m.useForm(),[J,k]=o.useState(!1),[v,K]=o.useState("create"),[O,P]=o.useState(null),[W,S]=o.useState(!1),[a,Y]=o.useState(null),[Z,ee]=o.useState([]),[se,te]=o.useState([]),h=async s=>{try{u(!0);const t=I.getFieldsValue(),d={page:T,pageSize:w,...t,...s},l=await xe(d);l.code===200?(q(l.result.list),U(l.result.total)):n.error(l.message||"获取数据失败")}catch(t){console.error("加载数据失败:",t),n.error("加载数据失败")}finally{u(!1)}},re=async()=>{try{const s=await pe({pageSize:500,status:1});s.code===200&&ee(s.result.list);const t=await fe({pageSize:500,status:"active"});t.code===200&&te(t.result.list)}catch(s){console.error("加载选项数据失败:",s)}};o.useEffect(()=>{h(),re()},[T,w]);const ae=()=>{A(1),h()},le=()=>{I.resetFields(),A(1),h()},B=async(s,t)=>{if(K(s),P(t||null),k(!0),s==="edit"&&t)try{const d=await G(t.id);if(d.code===200){const l=d.result;p.setFieldsValue({configName:l.configName,description:l.description,templateType:l.templateType||"box",status:l.status,sortOrder:l.sortOrder,remark:l.remark,topBannerAdId:l.topBannerAdId,carouselAdId:l.carouselAdId,homeGridAdId:l.homeGridAdId,splashPopupAdId:l.splashPopupAdId,floatAdId:l.floatAdId,floatContents:l.floatContents||[],gameCategories:l.gameCategories||[]})}else n.error("获取配置详情失败")}catch(d){console.error("获取配置详情失败:",d),n.error("获取配置详情失败")}else p.resetFields(),p.setFieldsValue({templateType:"box",status:1,sortOrder:1,gameCategories:[],floatContents:[]})},b=()=>{k(!1),P(null),p.resetFields()},ne=async()=>{try{const s=await p.validateFields();if(u(!0),v==="create"){const t=await me(s);t.code===200?(n.success("创建成功"),b(),h()):n.error(t.message||"创建失败")}else if(O){const t=await ue(O.id,s);t.code===200?(n.success("更新成功"),b(),h()):n.error(t.message||"更新失败")}}catch(s){console.error("提交失败:",s),n.error("操作失败")}finally{u(!1)}},oe=async s=>{try{const t=await je(s.id);t.code===200?(n.success(`${s.status===1?"禁用":"启用"}成功`),h()):n.error(t.message||"操作失败")}catch(t){console.error("切换状态失败:",t),n.error("操作失败")}},ie=async s=>{try{const t=await ge(s.id);t.code===200?(n.success("删除成功"),h()):n.error(t.message||"删除失败")}catch(t){console.error("删除失败:",t),n.error("删除失败")}},de=async s=>{try{u(!0);const t=await G(s.id);t.code===200?(Y(t.result),S(!0)):n.error(t.message||"获取详情失败")}catch(t){console.error("获取详情失败:",t),n.error("获取详情失败")}finally{u(!1)}},ce=[{title:"ID",dataIndex:"id",key:"id",width:80},{title:"配置名称",dataIndex:"configName",key:"configName",ellipsis:!0},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0,render:s=>s||"-"},{title:"模板类型",dataIndex:"templateType",key:"templateType",width:100,render:s=>e.jsx(c,{color:s==="box"?"blue":"default",children:s==="box"?"BOX":"经典"})},{title:"推荐游戏数",dataIndex:"recommendedGameCount",key:"recommendedGameCount",width:120,render:s=>e.jsxs(c,{color:"blue",children:[s,"个"]})},{title:"分类组数",dataIndex:"categoryCount",key:"categoryCount",width:100,render:s=>e.jsxs(c,{color:"green",children:[s,"个"]})},{title:"状态",dataIndex:"status",key:"status",width:100,render:s=>{const t=$.find(d=>d.value===s);return e.jsx(c,{color:t==null?void 0:t.color,children:t==null?void 0:t.label})}},{title:"排序",dataIndex:"sortOrder",key:"sortOrder",width:80},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:180,render:s=>new Date(s).toLocaleString()},{title:"操作",key:"action",width:200,render:(s,t)=>e.jsxs(N,{size:"small",children:[e.jsx(y,{title:"查看详情",children:e.jsx(x,{type:"link",size:"small",icon:e.jsx(be,{}),onClick:()=>de(t)})}),e.jsx(y,{title:"编辑",children:e.jsx(x,{type:"link",size:"small",icon:e.jsx(ke,{}),onClick:()=>B("edit",t)})}),e.jsx(y,{title:t.status===1?"禁用":"启用",children:e.jsx(ve,{size:"small",checked:t.status===1,onChange:()=>oe(t)})}),e.jsx(Oe,{title:"确定要删除这个配置吗？",onConfirm:()=>ie(t),okText:"确定",cancelText:"取消",children:e.jsx(y,{title:"删除",children:e.jsx(x,{type:"link",size:"small",danger:!0,icon:e.jsx(Pe,{})})})})]})}];return e.jsxs(he,{children:[e.jsxs(Ce,{children:[e.jsx("div",{style:{marginBottom:16},children:e.jsx(j,{gutter:16,children:e.jsxs(i,{span:24,children:[e.jsxs(g,{level:4,children:[e.jsx(Ae,{style:{marginRight:8}}),"APP首页配置管理"]}),e.jsx(r,{type:"secondary",children:"管理APP首页展示的广告位、推荐游戏和分类组配置"})]})})}),e.jsx(f,{}),e.jsxs(m,{form:I,layout:"inline",style:{marginBottom:16},children:[e.jsx(m.Item,{name:"configName",label:"配置名称",children:e.jsx(E,{placeholder:"请输入配置名称",allowClear:!0})}),e.jsx(m.Item,{name:"status",label:"状态",children:e.jsx(L,{placeholder:"请选择状态",allowClear:!0,style:{width:120},children:$.map(s=>e.jsx(Be,{value:s.value,children:s.label},s.value))})}),e.jsx(m.Item,{children:e.jsxs(N,{children:[e.jsx(x,{type:"primary",onClick:ae,children:"搜索"}),e.jsx(x,{onClick:le,children:"重置"}),e.jsx(x,{type:"primary",icon:e.jsx(we,{}),onClick:()=>B("create"),children:"新增配置"}),e.jsx(x,{icon:e.jsx(Ie,{}),onClick:()=>h(),children:"刷新"})]})})]}),e.jsx(Se,{columns:ce,dataSource:X,rowKey:"id",loading:C,pagination:{current:T,pageSize:w,total:Q,showSizeChanger:!0,showQuickJumper:!0,showTotal:s=>`共 ${s} 条记录`,onChange:(s,t)=>{A(s),_(t||10)}}})]}),e.jsx(H,{title:v==="create"?"新增APP首页配置 (BOX模式)":"编辑APP首页配置 (BOX模式)",open:J,onOk:ne,onCancel:b,width:1200,confirmLoading:C,destroyOnClose:!0,style:{top:20},children:e.jsx(m,{form:p,layout:"vertical",preserve:!1,initialValues:{templateType:"box",status:1,sortOrder:1,gameCategories:[]},children:e.jsx(ye,{form:p,adOptions:Z,applications:se,disabled:C})})}),e.jsx(H,{title:"APP首页配置详情",open:W,onCancel:()=>S(!1),footer:[e.jsx(x,{onClick:()=>S(!1),children:"关闭"},"close")],width:1e3,children:a&&e.jsxs("div",{children:[e.jsx(g,{level:5,children:"基本信息"}),e.jsxs(j,{gutter:16,children:[e.jsxs(i,{span:6,children:[e.jsx(r,{strong:!0,children:"配置名称："}),e.jsx(r,{children:a.configName})]}),e.jsxs(i,{span:6,children:[e.jsx(r,{strong:!0,children:"模板类型："}),e.jsx(c,{color:a.templateType==="box"?"blue":"default",children:a.templateType==="box"?"BOX模式":"经典模式"})]}),e.jsxs(i,{span:6,children:[e.jsx(r,{strong:!0,children:"状态："}),e.jsx(c,{color:a.status===1?"green":"red",children:a.status===1?"启用":"禁用"})]}),e.jsxs(i,{span:6,children:[e.jsx(r,{strong:!0,children:"排序："}),e.jsx(r,{children:a.sortOrder})]})]}),e.jsxs("div",{style:{marginTop:16},children:[e.jsx(r,{strong:!0,children:"描述："}),e.jsx(r,{children:a.description||"-"})]}),e.jsx(f,{}),e.jsx(g,{level:5,children:"广告配置"}),e.jsxs(j,{gutter:16,children:[e.jsxs(i,{span:12,children:[e.jsx(r,{strong:!0,children:"顶部Banner广告："}),e.jsx(r,{children:((D=a.topBannerAd)==null?void 0:D.title)||"-"})]}),e.jsxs(i,{span:12,children:[e.jsx(r,{strong:!0,children:"轮播广告："}),e.jsx(r,{children:((F=a.carouselAd)==null?void 0:F.title)||"-"})]})]}),a.templateType==="box"&&e.jsx(j,{gutter:16,style:{marginTop:12},children:e.jsxs(i,{span:24,children:[e.jsx(r,{strong:!0,children:"6宫格广告："}),e.jsx(r,{children:((R=a.homeGridAd)==null?void 0:R.title)||"-"}),!a.homeGridAd&&e.jsx(c,{color:"red",style:{marginLeft:8},children:"未配置（BOX模式必需）"})]})}),e.jsxs(j,{gutter:16,style:{marginTop:12},children:[e.jsxs(i,{span:12,children:[e.jsx(r,{strong:!0,children:"开屏弹窗广告："}),e.jsx(r,{children:((z=a.splashPopupAd)==null?void 0:z.title)||"-"})]}),e.jsxs(i,{span:12,children:[e.jsx(r,{strong:!0,children:"浮点广告："}),e.jsx(r,{children:((M=a.floatAd)==null?void 0:M.title)||"-"})]})]}),a.templateType==="box"&&e.jsxs(e.Fragment,{children:[e.jsx(f,{}),e.jsxs(g,{level:5,children:["浮点内容配置 (",((V=a.floatContents)==null?void 0:V.length)||0,"个)"]}),a.floatContents&&a.floatContents.length>0?a.floatContents.map((s,t)=>e.jsxs("div",{style:{marginBottom:12,padding:12,border:"1px solid #d9d9d9",borderRadius:4},children:[e.jsxs(j,{gutter:16,children:[e.jsxs(i,{span:8,children:[e.jsx(r,{strong:!0,children:"标题："}),e.jsx(r,{children:s.title})]}),e.jsxs(i,{span:8,children:[e.jsx(r,{strong:!0,children:"类型："}),e.jsx(c,{children:s.type})]}),e.jsxs(i,{span:8,children:[e.jsx(r,{strong:!0,children:"跳转类型："}),e.jsx(c,{color:s.jumpType==="route"?"blue":"green",children:s.jumpType==="route"?"路由":"内嵌页面"})]})]}),e.jsxs("div",{style:{marginTop:8},children:[e.jsx(r,{strong:!0,children:"内容："}),e.jsx(r,{children:s.content})]})]},t)):e.jsx(r,{type:"secondary",children:"暂无浮点内容配置"})]}),e.jsx(f,{}),e.jsxs(g,{level:5,children:["推荐游戏 (",a.recommendedGames.length,"个)"]}),a.recommendedGames.map((s,t)=>e.jsx("div",{style:{marginBottom:8},children:e.jsxs(r,{children:[t+1,". ",s.game.name]})},s.id)),e.jsx(f,{}),e.jsxs(g,{level:5,children:["游戏分类组 (",a.gameCategories.length,"个)"]}),a.gameCategories.map((s,t)=>e.jsxs("div",{style:{marginBottom:16},children:[e.jsxs(r,{strong:!0,children:[t+1,". ",s.categoryTitle["zh-CN"]," (",s.games.length,"个游戏)"]}),e.jsx("div",{style:{marginLeft:16,marginTop:4},children:s.games.map((d,l)=>e.jsx("div",{children:e.jsxs(r,{type:"secondary",children:[l+1,". ",d.game.name]})},d.id))})]},s.id))]})})]})};export{Ee as default};
