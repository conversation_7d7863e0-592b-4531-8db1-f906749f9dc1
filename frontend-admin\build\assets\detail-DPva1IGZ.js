import{j as r,u as S,ac as W}from"./index-DD0gcXtR.js";import{u as P,f as k,a as D}from"./useQuery-Do4-mmZ5.js";import{a as F}from"./react-BUTTOX-3.js";import{b as L}from"./index-DkOkoykF.js";import{e as M}from"./index-B5ghf3TI.js";import{ah as q,bB as b,av as I}from"./antd-FyCAPQKa.js";import{u as C}from"./useMutation-Dm45oaMR.js";import{D as R}from"./index-DhoWj0AL.js";import{W as x}from"./index-DUwEwhNf.js";import{P as $}from"./index-B9o-j3Nt.js";import"./index-BDysGMcU.js";import"./BaseForm-CUW86psc.js";function A({groupByType:y=!0,filter:s,selectedMenus:m=[],...u}){const{data:d,isLoading:c}=P({queryKey:["permission-select-list",s],queryFn:async()=>(await M()).result,staleTime:3e5}),{data:o,isLoading:p}=P({queryKey:["menu-list-for-permission"],queryFn:async()=>(await L({})).result.list,staleTime:5*60*1e3,enabled:m.length>0}),h=F.useMemo(()=>{var g,f;if(!d)return[];let a=d;if((s==null?void 0:s.status)!==void 0&&(a=a.filter(e=>e.status===s.status)),(g=s==null?void 0:s.types)!=null&&g.length&&(a=a.filter(e=>s.types.includes(e.type))),(f=s==null?void 0:s.excludeIds)!=null&&f.length&&(a=a.filter(e=>!s.excludeIds.includes(e.id))),m.length>0&&o){const n=o.filter(t=>m.includes(t.id)).map(t=>t.path.split("/").filter(Boolean).join(":"));a=a.filter(t=>t.type==="menu"?!0:t.type==="button"?n.some(i=>t.code.startsWith(i+":")||t.code.startsWith(i.replace(":",":"))):t.type==="api"?n.some(i=>t.code.startsWith(i+":")||t.code.startsWith(i.replace(":",":"))):!1)}if(!y)return a.map(e=>({title:`${e.name} (${e.code})`,value:e.id,key:e.id,permission:e}));const l={};a.forEach(e=>{l[e.type]||(l[e.type]=[]),l[e.type].push(e)});const w={menu:"菜单权限",button:"按钮权限",api:"API权限"};return Object.entries(l).map(([e,n])=>({title:w[e]||e,value:e,key:e,selectable:!1,children:n.map(t=>({title:`${t.name} (${t.code})`,value:t.id,key:t.id,permission:t}))}))},[d,s,y,m,o]);return c||m.length>0&&p?r.jsx(q,{size:"small"}):r.jsx(b,{...u,treeData:h,placeholder:"请选择权限",showSearch:!0,multiple:!0,treeCheckable:!0,showCheckedStrategy:b.SHOW_CHILD,treeNodeFilterProp:"title",style:{width:"100%",...u.style}})}function J({title:y,open:s,onCloseChange:m,detailData:u,treeData:d,refreshTable:c}){const{t:o}=S(),[p]=I.useForm(),[h,a]=F.useState([]),l=C({mutationFn:k}),w=C({mutationFn:D}),g=async e=>{var t,i;console.log("原始表单数据:",e);const n={name:e.name,code:e.code,remark:e.remark,permissionIds:e.permissionIds||[]};if(e.id&&(n.id=e.id),console.log("最终提交数据:",n),n.id)console.log("执行更新操作，数据:",n),await w.mutateAsync(n),(t=window.$message)==null||t.success(o("common.updateSuccess"));else{const{id:z,...j}=n;console.log("执行新增操作，数据:",j),await l.mutateAsync(j),(i=window.$message)==null||i.success(o("common.addSuccess"))}return c==null||c(),!0},f=e=>{a(e),p.setFieldValue("permissionIds",[])};return F.useEffect(()=>{if(s){const e={...u,permissionIds:u.permissionIds||[]};p.setFieldsValue(e),a(u.menus||[])}},[s,u]),r.jsxs(R,{title:y,open:s,onOpenChange:e=>{e===!1&&m()},resize:{onResize(){},maxWidth:window.innerWidth*.8,minWidth:500},labelCol:{span:6},wrapperCol:{span:24},layout:"horizontal",form:p,autoFocusFirstInput:!0,drawerProps:{destroyOnClose:!0},onFinish:g,initialValues:{menus:[],permissionIds:[]},children:[r.jsx(x,{name:"id",hidden:!0}),r.jsx(x,{allowClear:!0,rules:[{required:!0}],width:"md",name:"name",label:o("system.role.name"),tooltip:o("form.length",{length:24})}),r.jsx(x,{allowClear:!0,rules:[{required:!0}],width:"md",name:"code",label:o("system.role.id")}),r.jsx($,{allowClear:!0,width:"md",name:"remark",label:o("common.remark")}),r.jsx(I.Item,{name:"menus",label:o("system.role.assignMenu"),children:r.jsx(W,{treeData:d,onChange:f})}),r.jsx(I.Item,{name:"permissionIds",label:o("system.role.assignPermission"),children:r.jsx(A,{placeholder:h.length>0?"请选择角色权限":"请先选择菜单",style:{width:"100%"},disabled:h.length===0,selectedMenus:h})})]})}export{J as Detail};
