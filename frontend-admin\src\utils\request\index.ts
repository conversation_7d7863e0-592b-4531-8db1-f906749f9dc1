import type { Options } from "ky";
import { refreshTokenPath } from "#src/api/user";

import { loginPath } from "#src/router/extra-info";
import { useAuthStore, usePreferencesStore } from "#src/store";
import ky from "ky";

import { AUTH_HEADER, LANG_HEADER } from "./constants";
import { handleErrorResponse } from "./error-response";
import { globalProgress } from "./global-progress";
import { goLogin } from "./go-login";
import { refreshTokenAndRetry } from "./refresh";

// 请求白名单, 请求白名单内的接口不需要携带 token
const requestWhiteList = [loginPath, "system-auth/login"];

// 密码相关API白名单，这些API的401错误不应该触发token刷新
const passwordApiList = ["system-auth/change-password"];

// 请求超时时间
const API_TIMEOUT = Number(import.meta.env.VITE_API_TIMEOUT) || 30000; // 增加到30秒

const defaultConfig: Options = {
	// The input argument cannot start with a slash / when using prefixUrl option.
	prefixUrl: import.meta.env.VITE_API_BASE_URL,
	timeout: API_TIMEOUT,
	retry: {
		// 当请求失败时，最多重试次数
		limit: 3,
		// 对于特定的错误状态码不重试
		statusCodes: [401, 408, 413, 429, 500, 502, 503, 504], // 包含 401，避免密码错误时重试
	},
	hooks: {
		beforeRequest: [
			(request, options) => {
				// 记录请求开始时间
				(request as any)._startTime = Date.now();
				console.log(`[REQUEST] 开始请求: ${request.method} ${request.url}`);

				const ignoreLoading = options.ignoreLoading;
				if (!ignoreLoading) {
					globalProgress.start();
				}
				// 不需要携带 token 的请求
				const isWhiteRequest = requestWhiteList.some(url => request.url.endsWith(url));
				if (!isWhiteRequest) {
					const { token } = useAuthStore.getState();
					request.headers.set(AUTH_HEADER, `Bearer ${token}`);
				}
				// 语言等所有的接口都需要携带
				request.headers.set(LANG_HEADER, usePreferencesStore.getState().language);
			},
		],
		afterResponse: [
			async (request, options, response) => {
				// 计算请求耗时
				const startTime = (request as any)._startTime;
				const endTime = Date.now();
				const duration = startTime ? endTime - startTime : 0;

				console.log(`[REQUEST] 请求完成: ${request.method} ${request.url} - 状态: ${response.status} - 耗时: ${duration}ms`);

				const ignoreLoading = options.ignoreLoading;
				if (!ignoreLoading) {
					globalProgress.done();
				}
				// request error
				if (!response.ok) {
					if (response.status === 401) {
						// 防止刷新 refresh-token 继续接收到的 401 错误，出现死循环
						if ([refreshTokenPath].some(url => request.url.includes(url))) {
							goLogin();
							return response;
						}

						// 检查是否是登录请求
						const isLoginRequest = requestWhiteList.some(url => request.url.includes(url));
						// 检查是否是密码相关API
						const isPasswordApi = passwordApiList.some(url => request.url.includes(url));

						console.log("[REQUEST] 401错误分析:", {
							url: request.url,
							isLoginRequest,
							isPasswordApi,
							requestWhiteList,
							passwordApiList
						});

						if (isLoginRequest) {
							// 登录请求的401错误，提取错误消息但不显示，由登录组件处理
							console.log("[REQUEST] 登录请求401错误，提取错误消息但不显示");
							return handleErrorResponse(response, true); // skipMessage = true
						}

						if (isPasswordApi) {
							// 密码相关API的401错误，提取错误消息但不显示，由调用方处理
							console.log("[REQUEST] 密码API 401错误，提取错误消息但不显示，不触发token刷新");
							// 提取错误消息但不显示，避免重复显示
							return handleErrorResponse(response, true); // skipMessage = true
						}

						// If the token is expired, refresh it and try again.
						const { refreshToken } = useAuthStore.getState();
						// If there is no refresh token, it means that the user has not logged in.
						if (!refreshToken) {
							// 如果页面的路由已经重定向到登录页，则不用跳转直接返回结果
							if (location.pathname === loginPath) {
								return handleErrorResponse(response);
							}
							else {
								goLogin();
								return response;
							}
						}

						return refreshTokenAndRetry(request, options, refreshToken);
					}
					else {
						return handleErrorResponse(response);
					}
				}
				// request success
				return response;
			},
		],
	},
};

export const request = ky.create(defaultConfig);
