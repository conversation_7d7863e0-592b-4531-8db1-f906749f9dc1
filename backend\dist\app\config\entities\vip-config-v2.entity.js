"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipConfig = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
let VipConfig = class VipConfig {
    id;
    vipLevel;
    levelName;
    strategicPosition;
    requiredExp;
    realMoneyRechargeExpRatio;
    realMoneyFlowExpRatio;
    goldPaymentExpRatio;
    goldFlowExpRatio;
    dailyTaskExpMin;
    dailyTaskExpMax;
    dailyGoldReward;
    buybackEnabled;
    buybackRate;
    c2cFeeRate;
    rakebackRate;
    withdrawalFeeRate;
    dailyWithdrawalLimit;
    withdrawalPriority;
    taskUnlockRequired;
    taskUnlockGamesRequired;
    status;
    remark;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
    calculateUserExp(realMoneyRecharge = 0, realMoneyFlow = 0, goldPayment = 0, goldFlow = 0, dailyTaskCount = 0) {
        const rechargeExp = realMoneyRecharge * this.realMoneyRechargeExpRatio;
        const flowExp = realMoneyFlow * this.realMoneyFlowExpRatio;
        const paymentExp = goldPayment * this.goldPaymentExpRatio;
        const goldFlowExp = goldFlow * this.goldFlowExpRatio;
        const avgTaskExp = (this.dailyTaskExpMin + this.dailyTaskExpMax) / 2;
        const taskExp = dailyTaskCount * avgTaskExp;
        return Math.floor(rechargeExp + flowExp + paymentExp + goldFlowExp + taskExp);
    }
    isUserQualified(userExp) {
        return userExp >= this.requiredExp;
    }
    getBuybackRateDisplay() {
        if (!this.buybackEnabled || !this.buybackRate) {
            return '无资格';
        }
        return `${this.buybackRate}:1`;
    }
    getWithdrawalLimitDisplay() {
        if (!this.dailyWithdrawalLimit) {
            return '无上限';
        }
        return `₹${this.dailyWithdrawalLimit.toLocaleString()}`;
    }
    getWithdrawalPriorityDisplay() {
        const priorityMap = {
            1: '普通',
            2: '优先',
            3: 'VIP专享'
        };
        return priorityMap[this.withdrawalPriority] || '普通';
    }
};
exports.VipConfig = VipConfig;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], VipConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vip_level', unique: true, comment: 'VIP等级，从0开始，唯一' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "vipLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'level_name', length: 50, comment: '等级名称显示' }),
    __metadata("design:type", String)
], VipConfig.prototype, "levelName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'strategic_position', length: 200, nullable: true, comment: '核心战略定位描述' }),
    __metadata("design:type", String)
], VipConfig.prototype, "strategicPosition", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'required_exp', type: 'bigint', comment: '达到该等级所需的累计EXP经验值' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "requiredExp", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'real_money_recharge_exp_ratio',
        type: 'decimal',
        precision: 10,
        scale: 4,
        default: 10.0000,
        comment: '真金充值EXP比例 (1 INR = 10 EXP)'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "realMoneyRechargeExpRatio", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'real_money_flow_exp_ratio',
        type: 'decimal',
        precision: 10,
        scale: 4,
        default: 1.0000,
        comment: '真金流水EXP比例 (1 INR = 1 EXP)'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "realMoneyFlowExpRatio", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'gold_payment_exp_ratio',
        type: 'decimal',
        precision: 10,
        scale: 4,
        default: 5.0000,
        comment: '金币付费EXP比例 (1 INR = 5 EXP)'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "goldPaymentExpRatio", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'gold_flow_exp_ratio',
        type: 'decimal',
        precision: 10,
        scale: 6,
        default: 0.001000,
        comment: '金币流水EXP比例 (1000金币 = 1 EXP)'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "goldFlowExpRatio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'daily_task_exp_min', default: 10, comment: '活跃任务最小EXP' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "dailyTaskExpMin", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'daily_task_exp_max', default: 50, comment: '活跃任务最大EXP' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "dailyTaskExpMax", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'daily_gold_reward', type: 'bigint', default: 0, comment: '每日可领取金币数量' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "dailyGoldReward", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'buyback_enabled', default: false, comment: '是否开启官方回购资格' }),
    __metadata("design:type", Boolean)
], VipConfig.prototype, "buybackEnabled", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'buyback_rate',
        nullable: true,
        comment: '官方回购价格 (金币:1 INR)，如140表示140:1'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "buybackRate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'c2c_fee_rate',
        type: 'decimal',
        precision: 5,
        scale: 2,
        default: 15.00,
        comment: 'C2C手续费率(%)'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "c2cFeeRate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'rakeback_rate',
        type: 'decimal',
        precision: 5,
        scale: 2,
        default: 0.00,
        comment: '周返水比例(%)'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "rakebackRate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'withdrawal_fee_rate',
        type: 'decimal',
        precision: 5,
        scale: 2,
        default: 5.00,
        comment: '提现手续费率(%)'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "withdrawalFeeRate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'daily_withdrawal_limit',
        type: 'bigint',
        nullable: true,
        comment: '每日提现额度上限(INR)，NULL表示无上限'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "dailyWithdrawalLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'withdrawal_priority',
        default: 1,
        comment: '提现优先级 1-普通 2-优先 3-VIP专享'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "withdrawalPriority", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_unlock_required', default: false, comment: '活跃任务是否需要游戏局数解锁' }),
    __metadata("design:type", Boolean)
], VipConfig.prototype, "taskUnlockRequired", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_unlock_games_required', default: 0, comment: '解锁所需游戏局数' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "taskUnlockGamesRequired", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '备注说明' }),
    __metadata("design:type", String)
], VipConfig.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], VipConfig.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], VipConfig.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], VipConfig.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], VipConfig.prototype, "updater", void 0);
exports.VipConfig = VipConfig = __decorate([
    (0, typeorm_1.Entity)('vip_configs')
], VipConfig);
//# sourceMappingURL=vip-config-v2.entity.js.map