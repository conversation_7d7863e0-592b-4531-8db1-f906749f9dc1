import{u as I,j as n,H as R,a6 as M}from"./index-DD0gcXtR.js";import{u as S,b as A,c as q,d as E,e as F}from"./useQuery-Do4-mmZ5.js";import{u as L}from"./index-DtsmHvPi.js";import{a as c}from"./react-BUTTOX-3.js";import{B as $}from"./index-C1Do55qr.js";import{B as z}from"./index-DUsnaVFw.js";import{u as H}from"./useMutation-Dm45oaMR.js";import{Detail as O}from"./detail-DPva1IGZ.js";import{getConstantColumns as v}from"./constants-CmI9ADgZ.js";import{aD as K,d as N,bH as P}from"./antd-FyCAPQKa.js";import{a as f}from"./constants-BR8N8nwY.js";import"./Table-Blekjt7O.js";import"./index-DwiaqWIr.js";import"./index-BDysGMcU.js";import"./BaseForm-CUW86psc.js";import"./index-DhoWj0AL.js";import"./index-BhoZk0D4.js";import"./index-DkOkoykF.js";import"./index-B5ghf3TI.js";import"./index-DUwEwhNf.js";import"./index-B9o-j3Nt.js";function de(){const{t:s}=I(),{hasAccessByCodes:d}=L(),{data:w}=S({queryKey:["role-menu"],queryFn:async()=>{var r;const t=await F();return(((r=t==null?void 0:t.result)==null?void 0:r.list)||(t==null?void 0:t.result)||[]).map(o=>({...o,title:o.title||o.name,key:o.id,id:String(o.id)}))},initialData:[]}),g=H({mutationFn:E}),[C,u]=c.useState(!1),[j,y]=c.useState(""),[B,h]=c.useState({}),x=c.useRef(null),D=async(t,e)=>{var r,o,i,l,m;try{const a=await g.mutateAsync(t);await((r=e==null?void 0:e.reload)==null?void 0:r.call(e)),(o=window.$message)==null||o.success(a.message||s("common.deleteSuccess"))}catch(a){const p=((l=(i=a==null?void 0:a.response)==null?void 0:i.data)==null?void 0:l.message)||(a==null?void 0:a.message)||s("common.deleteFailed");(m=window.$message)==null||m.error(p)}},T=[...v(s),{title:s("common.action"),valueType:"option",key:"option",width:120,fixed:"right",render:(t,e,r,o)=>[n.jsx(R,{type:"link",size:"small",disabled:!d(f.roleEdit),onClick:async()=>{var i,l;try{const m=await A({id:e.id}),a=((i=e.permissions)==null?void 0:i.map(p=>p.id))||[];u(!0),y(s("system.role.editRole")),h({...e,menus:m.result,permissionIds:a})}catch{(l=window.$message)==null||l.error("获取角色信息失败")}},children:s("common.edit")},"editable"),n.jsx(K,{title:s("common.confirmDelete"),onConfirm:()=>D(e.id,o),okText:s("common.confirm"),cancelText:s("common.cancel"),children:n.jsx(R,{type:"link",size:"small",disabled:!d(f.roleDelete),children:s("common.delete")})},"delete")]}],b=()=>{u(!1),h({})},k=()=>{var t;(t=x.current)==null||t.reload()};return n.jsxs($,{className:"h-full",children:[n.jsx(z,{columns:T,actionRef:x,request:async t=>{const e=await q(t);return{...e,data:e.result.list,total:e.result.total}},headerTitle:s("common.menu.role"),toolBarRender:()=>[n.jsx(N,{icon:n.jsx(P,{}),type:"primary",disabled:!d(f.roleAdd),onClick:()=>{u(!0),y(s("system.role.addRole"))},children:s("common.add")},"add-role")]}),n.jsx(O,{title:j,open:C,onCloseChange:b,detailData:B,refreshTable:k,treeData:M(w||[])})]})}export{de as default};
