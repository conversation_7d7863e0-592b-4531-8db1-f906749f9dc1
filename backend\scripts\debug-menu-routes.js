// 调试菜单和路由转换的脚本
const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function debugMenuRoutes() {
  try {
    console.log('🔍 开始调试菜单和路由转换...');
    
    // 创建应用实例
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // 获取菜单服务
    const { SystemMenuService } = require('../dist/system/menu/menu.service');
    const { SystemRoutesService } = require('../dist/system/routes/routes.service');
    const menuService = app.get(SystemMenuService);
    const routesService = app.get(SystemRoutesService);
    
    console.log('\n📋 1. 查询所有菜单:');
    const allMenus = await menuService.getMenusByRoles([], true, []);
    console.log('菜单数量:', allMenus.length);
    
    // 打印菜单树结构
    function printMenuTree(menus, indent = '') {
      menus.forEach(menu => {
        console.log(`${indent}${menu.id}: ${menu.name} (${menu.path}) - component: ${menu.component || 'N/A'}`);
        if (menu.children && menu.children.length > 0) {
          printMenuTree(menu.children, indent + '  ');
        }
      });
    }
    
    printMenuTree(allMenus);
    
    console.log('\n🔄 2. 转换为路由:');
    const routes = await routesService.getRoutes([], true, []);
    console.log('路由数量:', routes.length);
    
    // 打印路由结构
    function printRoutes(routes, indent = '') {
      routes.forEach(route => {
        console.log(`${indent}路径: ${route.path}`);
        console.log(`${indent}组件: ${route.component || 'N/A'}`);
        console.log(`${indent}标题: ${route.handle?.title || 'N/A'}`);
        if (route.children && route.children.length > 0) {
          console.log(`${indent}子路由:`);
          printRoutes(route.children, indent + '  ');
        }
        console.log('');
      });
    }
    
    printRoutes(routes);
    
    console.log('\n🔍 3. 查找VIP相关菜单:');
    const flatMenus = [];
    function flattenMenus(menus) {
      menus.forEach(menu => {
        flatMenus.push(menu);
        if (menu.children && menu.children.length > 0) {
          flattenMenus(menu.children);
        }
      });
    }
    flattenMenus(allMenus);
    
    const vipMenus = flatMenus.filter(menu => 
      menu.name.includes('VIP') || 
      menu.path.includes('vip') || 
      menu.component?.includes('vip')
    );
    
    console.log('VIP相关菜单数量:', vipMenus.length);
    vipMenus.forEach(menu => {
      console.log(`  ${menu.id}: ${menu.name}`);
      console.log(`    路径: ${menu.path}`);
      console.log(`    组件: ${menu.component || 'N/A'}`);
      console.log(`    父菜单ID: ${menu.parentId || 'N/A'}`);
      console.log(`    权限代码: ${menu.permissionCode || 'N/A'}`);
      console.log('');
    });
    
    await app.close();
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    console.error(error.stack);
  }
}

// 执行调试
if (require.main === module) {
  debugMenuRoutes()
    .then(() => {
      console.log('✅ 调试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 调试失败:', error);
      process.exit(1);
    });
}

module.exports = { debugMenuRoutes };
