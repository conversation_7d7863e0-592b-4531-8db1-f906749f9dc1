const { Client } = require('pg');
const { getDatabaseConfig } = require('../database/config/database-config');

async function debugAdminVipAccess() {
  try {
    console.log('🔍 调试系统管理员VIP访问权限...');
    
    const config = getDatabaseConfig('local');
    const client = new Client(config);
    await client.connect();

    // 1. 检查admin用户基本信息
    console.log('\n👤 Admin用户基本信息:');
    const adminUser = await client.query(`
      SELECT id, username, email, is_super_admin, status
      FROM sys_users 
      WHERE username = 'admin'
    `);
    console.table(adminUser.rows);

    // 2. 检查admin用户的角色
    console.log('\n🎭 Admin用户角色:');
    const adminRoles = await client.query(`
      SELECT 
        u.username,
        r.id as role_id,
        r.name as role_name,
        r.code as role_code,
        r.status as role_status
      FROM sys_users u
      JOIN sys_user_roles ur ON u.id = ur.user_id
      JOIN sys_roles r ON ur.role_id = r.id
      WHERE u.username = 'admin'
    `);
    console.table(adminRoles.rows);

    // 3. 检查VIP菜单的详细配置
    console.log('\n📋 VIP菜单详细配置:');
    const vipMenu = await client.query(`
      SELECT 
        id, 
        title, 
        path, 
        component, 
        parent_id, 
        permission_code,
        status,
        type
      FROM sys_menus 
      WHERE path = '/config/vip'
    `);
    console.table(vipMenu.rows);

    // 4. 检查admin角色是否有VIP菜单权限
    console.log('\n🔐 Admin角色VIP菜单权限:');
    const adminMenuPermissions = await client.query(`
      SELECT 
        m.id as menu_id,
        m.title as menu_title,
        m.path as menu_path,
        m.permission_code,
        r.code as role_code
      FROM sys_menus m
      JOIN sys_role_menus rm ON m.id = rm.menu_id
      JOIN sys_roles r ON rm.role_id = r.id
      WHERE r.code IN ('admin', 'super_admin') 
        AND m.path = '/config/vip'
    `);
    console.table(adminMenuPermissions.rows);

    // 5. 检查admin角色是否有VIP权限代码
    console.log('\n🔑 Admin角色VIP权限代码:');
    const adminPermissions = await client.query(`
      SELECT 
        p.id as permission_id,
        p.name as permission_name,
        p.code as permission_code,
        r.code as role_code
      FROM sys_permissions p
      JOIN sys_role_permissions rp ON p.id = rp.permission_id
      JOIN sys_roles r ON rp.role_id = r.id
      WHERE r.code IN ('admin', 'super_admin') 
        AND p.code LIKE '%vip%'
    `);
    console.table(adminPermissions.rows);

    // 6. 检查配置管理父菜单权限
    console.log('\n📁 配置管理父菜单权限:');
    const configMenuPermissions = await client.query(`
      SELECT 
        m.id as menu_id,
        m.title as menu_title,
        m.path as menu_path,
        m.permission_code,
        r.code as role_code
      FROM sys_menus m
      JOIN sys_role_menus rm ON m.id = rm.menu_id
      JOIN sys_roles r ON rm.role_id = r.id
      WHERE r.code IN ('admin', 'super_admin') 
        AND m.path = '/config'
    `);
    console.table(configMenuPermissions.rows);

    // 7. 检查所有配置管理子菜单权限
    console.log('\n📂 所有配置管理子菜单权限:');
    const allConfigSubMenus = await client.query(`
      SELECT 
        m.id as menu_id,
        m.title as menu_title,
        m.path as menu_path,
        m.component,
        m.permission_code,
        COUNT(rm.role_id) as role_count
      FROM sys_menus m
      LEFT JOIN sys_role_menus rm ON m.id = rm.menu_id
      LEFT JOIN sys_roles r ON rm.role_id = r.id AND r.code IN ('admin', 'super_admin')
      WHERE m.parent_id = (SELECT id FROM sys_menus WHERE path = '/config')
      GROUP BY m.id, m.title, m.path, m.component, m.permission_code
      ORDER BY m.id
    `);
    console.table(allConfigSubMenus.rows);

    await client.end();
    
  } catch (error) {
    console.error('调试失败:', error.message);
    console.error(error.stack);
  }
}

debugAdminVipAccess();
