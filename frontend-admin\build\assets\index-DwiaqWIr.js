import{aW as a,bh as l,y as X,aS as le,bi as R,n as se,bs as ce,b5 as Q,Q as pe,bt as ue,av as C}from"./antd-FyCAPQKa.js";import{R as m,a as I}from"./react-BUTTOX-3.js";import{u as q,F as de,a as me,P as fe,E as ve}from"./index-BDysGMcU.js";import{j as t}from"./index-DD0gcXtR.js";import{B as xe}from"./BaseForm-CUW86psc.js";var he=function(e){return l({},e.componentCls,{display:"inline-flex",alignItems:"center",maxWidth:"100%","&-icon":{display:"block",marginInlineStart:"4px",cursor:"pointer","&:hover":{color:e.colorPrimary}},"&-title":{display:"inline-flex",flex:"1"},"&-subtitle ":{marginInlineStart:8,color:e.colorTextSecondary,fontWeight:"normal",fontSize:e.fontSize,whiteSpace:"nowrap"},"&-title-ellipsis":{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",wordBreak:"keep-all"}})};function ge(o){return q("LabelIconTip",function(e){var n=a(a({},e),{},{componentCls:".".concat(o)});return[he(n)]})}var ye=m.memo(function(o){var e=o.label,n=o.tooltip,h=o.ellipsis,r=o.subTitle,P=I.useContext(X.ConfigContext),S=P.getPrefixCls,c=S("pro-core-label-tip"),b=ge(c),F=b.wrapSSR,f=b.hashId;if(!n&&!r)return t.jsx(t.Fragment,{children:e});var p=typeof n=="string"||m.isValidElement(n)?{title:n}:n,j=(p==null?void 0:p.icon)||t.jsx(le,{});return F(t.jsxs("div",{className:R(c,f),onMouseDown:function(u){return u.stopPropagation()},onMouseLeave:function(u){return u.stopPropagation()},onMouseMove:function(u){return u.stopPropagation()},children:[t.jsx("div",{className:R("".concat(c,"-title"),f,l({},"".concat(c,"-title-ellipsis"),h)),children:e}),r&&t.jsx("div",{className:"".concat(c,"-subtitle ").concat(f).trim(),children:r}),n&&t.jsx(se,a(a({},p),{},{children:t.jsx("span",{className:"".concat(c,"-icon ").concat(f).trim(),children:j})}))]}))}),Ce=function(e){return l({},e.componentCls,{"&-title":{marginBlockEnd:e.marginXL,fontWeight:"bold"},"&-container":l({flexWrap:"wrap",maxWidth:"100%"},"> div".concat(e.antCls,"-space-item"),{maxWidth:"100%"}),"&-twoLine":l(l(l(l({display:"block",width:"100%"},"".concat(e.componentCls,"-title"),{width:"100%",margin:"8px 0"}),"".concat(e.componentCls,"-container"),{paddingInlineStart:16}),"".concat(e.antCls,"-space-item,").concat(e.antCls,"-form-item"),{width:"100%"}),"".concat(e.antCls,"-form-item"),{"&-control":{display:"flex",alignItems:"center",justifyContent:"flex-end","&-input":{alignItems:"center",justifyContent:"flex-end","&-content":{flex:"none"}}}})})};function Pe(o){return q("ProFormGroup",function(e){var n=a(a({},e),{},{componentCls:".".concat(o)});return[Ce(n)]})}var J=m.forwardRef(function(o,e){var n=m.useContext(de),h=n.groupProps,r=a(a({},h),o),P=r.children,S=r.collapsible,c=r.defaultCollapsed,b=r.style,F=r.labelLayout,f=r.title,p=f===void 0?o.label:f,j=r.tooltip,g=r.align,u=g===void 0?"start":g,E=r.direction,$=r.size,N=$===void 0?32:$,K=r.titleStyle,G=r.titleRender,d=r.spaceProps,_=r.extra,M=r.autoFocus,U=ce(function(){return c||!1},{value:o.collapsed,onChange:o.onCollapse}),T=Q(U,2),W=T[0],Y=T[1],Z=I.useContext(X.ConfigContext),ee=Z.getPrefixCls,z=me(o),te=z.ColWrapper,B=z.RowWrapper,x=ee("pro-form-group"),D=Pe(x),oe=D.wrapSSR,w=D.hashId,O=S&&t.jsx(ue,{style:{marginInlineEnd:8},rotate:W?void 0:90}),V=t.jsx(ye,{label:O?t.jsxs("div",{children:[O,p]}):p,tooltip:j}),k=I.useCallback(function(v){var y=v.children;return t.jsx(pe,a(a({},d),{},{className:R("".concat(x,"-container ").concat(w),d==null?void 0:d.className),size:N,align:u,direction:E,style:a({rowGap:0},d==null?void 0:d.style),children:y}))},[u,x,E,w,N,d]),A=G?G(V,o):V,re=I.useMemo(function(){var v=[],y=m.Children.toArray(P).map(function(i,ie){var L;return m.isValidElement(i)&&i!==null&&i!==void 0&&(L=i.props)!==null&&L!==void 0&&L.hidden?(v.push(i),null):ie===0&&m.isValidElement(i)&&M?m.cloneElement(i,a(a({},i.props),{},{autoFocus:M})):i});return[t.jsx(B,{Wrapper:k,children:y},"children"),v.length>0?t.jsx("div",{style:{display:"none"},children:v}):null]},[P,B,k,M]),H=Q(re,2),ne=H[0],ae=H[1];return oe(t.jsx(te,{children:t.jsxs("div",{className:R(x,w,l({},"".concat(x,"-twoLine"),F==="twoLine")),style:b,ref:e,children:[ae,(p||j||_)&&t.jsx("div",{className:"".concat(x,"-title ").concat(w).trim(),style:K,onClick:function(){Y(!W)},children:_?t.jsxs("div",{style:{display:"flex",width:"100%",alignItems:"center",justifyContent:"space-between"},children:[A,t.jsx("span",{onClick:function(y){return y.stopPropagation()},children:_})]}):A}),t.jsx("div",{style:{display:S&&W?"none":void 0},children:ne})]})}))});J.displayName="ProForm-Group";function s(o){return t.jsx(xe,a({layout:"vertical",contentRender:function(n,h){return t.jsxs(t.Fragment,{children:[n,h]})}},o))}s.Group=J;s.useForm=C.useForm;s.Item=fe;s.useWatch=C.useWatch;s.ErrorList=C.ErrorList;s.Provider=C.Provider;s.useFormInstance=C.useFormInstance;s.EditOrReadOnlyContext=ve;var Re=s.Group;export{ye as L,s as P,Re as a};
