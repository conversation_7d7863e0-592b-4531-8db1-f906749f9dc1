import{u as m,j as e}from"./index-DD0gcXtR.js";import"./react-BUTTOX-3.js";import{B as k}from"./index-C1Do55qr.js";import{t as j,T as x,an as l,ao as c,ap as n,aq as s,ar as o,J as d}from"./antd-FyCAPQKa.js";var y={pkg:{dependencies:{"@ant-design/icons":"^5.6.1","@ant-design/pro-components":"^2.8.6","@dnd-kit/core":"^6.3.1","@dnd-kit/sortable":"^10.0.0","@dnd-kit/utilities":"^3.2.2","@supabase/supabase-js":"^2.50.1","@tanstack/react-query":"^5.69.0","@types/react-beautiful-dnd":"^13.1.8",ahooks:"^3.8.4",antd:"^5.24.4","antd-img-crop":"^4.24.0",dayjs:"^1.11.13",echarts:"^5.6.0","echarts-for-react":"^3.0.3",i18next:"^24.2.3","keepalive-for-react":"^4.0.2",ky:"^1.7.5",motion:"^12.5.0",nprogress:"^0.2.0","pinyin-pro":"^3.26.0",react:"18","react-beautiful-dnd":"^13.1.1","react-countup":"^6.5.3","react-dom":"18","react-error-boundary":"^5.0.0","react-i18next":"^15.4.1","react-jss":"^10.10.0","react-router":"^7.4.0","simplebar-react":"^3.3.0","spin-delay":"^2.0.1","tailwind-merge":"^2.6.0",zustand:"^5.0.3"},devDependencies:{"@antfu/eslint-config":"^4.10.1","@commitlint/cli":"^19.8.0","@commitlint/config-conventional":"^19.8.0","@eslint-react/eslint-plugin":"^1.36.1","@eslint/config-inspector":"^1.0.2","@faker-js/faker":"^9.6.0","@svgr/plugin-jsx":"^8.1.0","@svgr/plugin-svgo":"^8.1.0","@tanstack/react-query-devtools":"^5.69.0","@testing-library/jest-dom":"^6.6.3","@testing-library/react":"^16.2.0","@testing-library/user-event":"^14.6.1","@types/node":"^22.13.10","@types/nprogress":"^0.2.3","@types/react":"^18.3.12","@types/react-dom":"^18.3.1","@vitejs/plugin-react":"^4.3.4",autoprefixer:"^10.4.21",clsx:"^2.1.1","code-inspector-plugin":"^0.20.6",eslint:"^9.22.0","eslint-plugin-react-hooks":"^5.2.0","eslint-plugin-react-refresh":"^0.4.19",globals:"^16.0.0","happy-dom":"^17.4.4","lint-staged":"^15.5.0",postcss:"^8.5.3","simple-git-hooks":"^2.11.1",tailwindcss:"^3.4.17",taze:"^19.0.2",typescript:"^5.8.2",vite:"^6.2.3","vite-bundle-visualizer":"^1.2.1","vite-plugin-checker":"^0.9.1","vite-plugin-fake-server":"^2.2.0","vite-plugin-svgr":"^4.3.0",vitest:"^3.0.9"}}};const{dependencies:p,devDependencies:u}=y.pkg,h=Object.keys(p).map(t=>{const r=p[t];return{key:t,label:t,children:r}}),b=Object.keys(u).map(t=>{const r=u[t];return{key:t,label:t,children:r}});var a={pkg:{version:"0.0.0",license:"MIT",author:"Condor Hero"},lastBuildTime:"2025-06-29 14:02:56"};const{version:v}=a.pkg,{lastBuildTime:f}=a,{Text:T,Link:i}=x;function A(){const{t}=m(),{token:{colorBgLayout:r}}=j.useToken(),g=[{key:1,label:t("about.version"),children:e.jsx(T,{code:!0,children:v})},{key:2,label:t("about.lastBuildTime"),children:e.jsx(l,{color:"#55acee",children:f})},{key:3,label:t("about.license"),children:e.jsx(l,{color:"green",children:a.pkg.license})},{key:4,label:t("about.previewAddress"),children:e.jsx(i,{rel:"noreferrer noopener",copyable:!0,target:"_blank",href:"https://condorheroblog.github.io/react-antd-admin/",children:t("common.view")})},{key:5,label:t("about.documentAddress"),children:e.jsx(i,{rel:"noreferrer noopener",copyable:!0,target:"_blank",href:"https://condorheroblog.github.io/react-antd-admin/docs/",children:t("common.view")})},{key:6,label:"Github",children:e.jsx(i,{rel:"noreferrer noopener",copyable:!0,target:"_blank",href:"https://github.com/condorheroblog/react-antd-admin",children:"InApp2 管理后台"})},{key:7,label:t("about.author"),children:e.jsx(i,{rel:"noreferrer noopener",target:"_blank",href:"https://github.com/condorheroblog/",children:a.pkg.author})}];return e.jsx(k,{children:e.jsxs(c,{gutter:[0,20],style:{backgroundColor:r},children:[e.jsx(n,{span:24,children:e.jsx(c,{children:e.jsx(n,{children:e.jsx(s,{title:t("about.aboutProject"),children:e.jsx(o,{items:[{key:1,children:t("about.descriptions")}]})})})})}),e.jsx(n,{span:24,children:e.jsx(s,{title:t("about.projectMessage"),children:e.jsx(o,{bordered:!0,items:g})})}),e.jsx(n,{span:24,children:e.jsx(d.Ribbon,{text:h.length,color:"green",children:e.jsx(s,{title:t("about.dependencies"),children:e.jsx(o,{bordered:!0,items:h})})})}),e.jsx(n,{span:24,children:e.jsx(d.Ribbon,{text:b.length,color:"blue",children:e.jsx(s,{title:t("about.devDependencies"),children:e.jsx(o,{bordered:!0,items:b})})})})]})})}export{A as default};
