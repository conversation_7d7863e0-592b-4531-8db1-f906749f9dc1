import{u as t,j as s}from"./index-DD0gcXtR.js";import"./react-BUTTOX-3.js";import{B as a}from"./index-C1Do55qr.js";import{PasswordChange as i}from"./password-change-cgAgwFMK.js";import{T as l,ao as c,ap as r,D as o}from"./antd-FyCAPQKa.js";import"./useMutation-Dm45oaMR.js";import"./rules-Br-WHaJV.js";const{Title:n}=l;function v(){const{t:e}=t();return s.jsxs(a,{className:"max-w-4xl ml-10",children:[s.jsx(n,{level:3,className:"mb-6",children:e("common.menu.settings")}),s.jsxs(c,{gutter:[24,24],children:[s.jsx(r,{xs:24,lg:12,children:s.jsx("div",{className:"space-y-6",children:s.jsxs("div",{children:[s.jsx(n,{level:4,className:"mb-4",children:e("personal-center.accountSecurity")}),s.jsx(i,{})]})})}),s.jsx(r,{xs:24,lg:12,children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx(n,{level:4,className:"mb-4",children:e("personal-center.generalSettings")}),s.jsx("div",{className:"text-gray-500 text-center py-8",children:"Coming soon..."})]}),s.jsx(o,{}),s.jsxs("div",{children:[s.jsx(n,{level:4,className:"mb-4",children:e("personal-center.notificationSettings")}),s.jsx("div",{className:"text-gray-500 text-center py-8",children:"Coming soon..."})]})]})})]})]})}export{v as default};
