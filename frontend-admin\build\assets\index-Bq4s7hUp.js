import{u as T,a as b,b as p,j as e}from"./index-DD0gcXtR.js";import{u as f}from"./index-DtsmHvPi.js";import{h as A}from"./react-BUTTOX-3.js";import{B as y}from"./index-C1Do55qr.js";import{au as B,aq as a,d as C,T as c}from"./antd-FyCAPQKa.js";import{a as t,A as o}from"./constants-BR8N8nwY.js";function l({type:s="code",codes:m,children:n,fallback:i}){const{hasAccessByCodes:d,hasAccessByRoles:u}=f();return n?!s||s==="code"?d(m)?n:i:s==="role"&&u(m)?n:i:null}const R={[o.admin]:{password:"123456789admin",username:o.admin},[o.common]:{password:"123456789admin",username:o.common}};function k(){const{t:s}=T(),m=A(),{hasAccessByCodes:n,hasAccessByRoles:i}=f(),{roles:d}=b(),u=p(r=>r.reset),g=p(r=>r.login);function x(r){return d.includes(r)?"primary":"default"}function h(r){if(d.includes(r))return;const j=R[r];u(),j&&g(j).then(()=>{m(0)})}return e.jsxs(y,{className:"flex flex-col gap-4",children:[e.jsx(B,{message:s("access.buttonControl.alertMessage"),description:s("access.buttonControl.alertDescription")}),e.jsx(a,{title:e.jsxs(e.Fragment,{children:[s("access.buttonControl.currentRole"),"  ",e.jsx(c.Text,{mark:!0,code:!0,children:d})]}),children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx(C,{type:x(o.admin),onClick:()=>h(o.admin),children:s("access.buttonControl.switchAdmin")}),e.jsx(C,{type:x(o.common),onClick:()=>h(o.common),children:s("access.buttonControl.switchCommon")})]})}),e.jsx(a,{title:s("access.buttonControl.componentControlPermissionCodes"),children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(l,{codes:t.get,children:e.jsx(c.Text,{code:!0,children:t.get})}),e.jsx(l,{codes:t.update,children:e.jsx(c.Text,{code:!0,children:t.update})}),e.jsx(l,{codes:t.delete,children:e.jsx(c.Text,{code:!0,children:t.delete})}),e.jsx(l,{codes:t.add,children:e.jsx(c.Text,{code:!0,children:t.add})})]})}),e.jsx(a,{title:s("access.buttonControl.componentControlRoles"),children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(l,{type:"role",codes:[o.admin,o.common],children:e.jsxs(c.Text,{code:!0,children:[s("access.adminVisible.title")," & ",s("access.commonVisible.title")]})}),e.jsx(l,{type:"role",codes:o.admin,children:e.jsx(c.Text,{code:!0,children:s("access.adminVisible.title")})}),e.jsx(l,{type:"role",codes:o.common,children:e.jsx(c.Text,{code:!0,children:s("access.commonVisible.title")})})]})}),e.jsx(a,{title:s("access.buttonControl.functionControlPermissionCodes"),children:e.jsxs("div",{className:"flex items-center gap-4",children:[n(t.get)&&e.jsx(c.Text,{code:!0,children:t.get}),n(t.update)&&e.jsx(c.Text,{code:!0,children:t.update}),n(t.delete)&&e.jsx(c.Text,{code:!0,children:t.delete}),n([t.add])&&e.jsx(c.Text,{code:!0,children:t.add})]})}),e.jsx(a,{title:s("access.buttonControl.functionControlRoles"),children:e.jsxs("div",{className:"flex items-center gap-4",children:[i([o.admin,o.common])&&e.jsxs(c.Text,{code:!0,children:[s("access.adminVisible.title")," & ",s("access.commonVisible.title")]}),i([o.admin])&&e.jsx(c.Text,{code:!0,children:s("access.adminVisible.title")}),i(o.common)&&e.jsx(c.Text,{code:!0,children:s("access.commonVisible.title")})]})})]})}export{k as default};
