import{j as e,H as c}from"./index-DD0gcXtR.js";import{a as i}from"./react-BUTTOX-3.js";import{B as p}from"./index-C1Do55qr.js";import{aq as f,I as h,f as u,aw as n,af as j,an as m,bH as y,ax as g}from"./antd-FyCAPQKa.js";function o(r,l="key"){return r.flatMap(t=>{var a;return[t[l],...(a=t.children)!=null&&a.length?o(t.children,l):[]]})}const d=[{title:"parent 1",key:"0-0",children:[{title:"parent 1-0",key:"0-0-0",disabled:!0,children:[{title:"leaf",key:"0-0-0-0",disableCheckbox:!0},{title:"leaf",key:"0-0-0-1"}]},{title:"parent 1-1",key:"0-0-1",children:[{title:e.jsx("span",{style:{color:"#1677ff"},children:"sss"}),key:"0-0-1-0"}]}]}];function B(){const[r,l]=i.useState([]),[t,a]=i.useState([]);return e.jsx(p,{className:"h-full",children:e.jsx(f,{className:"h-full [&_.ant-card-body]:h-full",children:e.jsxs("div",{className:"relative h-full w-full overflow-hidden border-r-[1px] border-r-gray-200 pr-5 lg:w-4/12",children:[e.jsxs("div",{className:"flex gap-3 mb-4",children:[e.jsx(h,{placeholder:"搜索",className:"flex-1",prefix:e.jsx(u,{})}),e.jsxs(n.Group,{onChange:s=>{const x=s.target.value;a(x==="expand"?o(d,"key"):[])},children:[e.jsx(n.Button,{value:"expand",children:"展开"}),e.jsx(n.Button,{value:"collapse",children:"折叠"})]})]}),e.jsx("div",{className:"flex flex-col gap-y-1",children:e.jsx(j,{className:"[&_.ant-tree-treenode-selected_.tree-actions]:flex",blockNode:!0,expandedKeys:t,onExpand:s=>{a(s)},selectedKeys:r,onSelect:s=>{l(s)},titleRender:s=>e.jsxs("div",{className:"group flex justify-between items-center",children:[e.jsx("span",{children:s.title}),e.jsxs("div",{className:"tree-actions hidden group-hover:flex items-center gap-0.5",children:[e.jsx(m,{color:"processing",className:"mr-0 h-fit text-xs",children:"菜单"}),e.jsxs("div",{children:[e.jsx(c,{color:"primary",variant:"text",size:"small",icon:e.jsx(y,{})}),e.jsx(c,{danger:!0,type:"text",size:"small",icon:e.jsx(g,{})})]})]})]}),treeData:d})})]})})})}export{B as default};
