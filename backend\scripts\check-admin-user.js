// 检查管理员用户信息的脚本
const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function checkAdminUser() {
  try {
    console.log('🔍 检查管理员用户信息...');
    
    // 创建应用实例
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // 获取用户服务
    const { SystemUserService } = require('../dist/system/user/user.service');
    const userService = app.get(SystemUserService);
    
    console.log('\n📋 查询所有用户:');
    const users = await userService.findAll({
      page: 1,
      pageSize: 10
    });
    
    console.log('用户查询结果:', users);

    if (users && users.list && Array.isArray(users.list)) {
      console.log('用户数量:', users.total || users.list.length);
      users.list.forEach(user => {
        console.log(`  ID: ${user.id}`);
        console.log(`  用户名: ${user.username}`);
        console.log(`  邮箱: ${user.email || 'N/A'}`);
        console.log(`  状态: ${user.status}`);
        console.log(`  超级管理员: ${user.isSuperAdmin}`);
        console.log(`  创建时间: ${user.createTime}`);
        console.log('');
      });
    } else {
      console.log('❌ 用户数据格式不正确');
    }
    
    // 尝试查找admin用户
    console.log('\n🔍 查找admin用户:');
    try {
      const adminUser = await userService.findByUsername('admin');
      if (adminUser) {
        console.log('✅ 找到admin用户:');
        console.log(`  ID: ${adminUser.id}`);
        console.log(`  用户名: ${adminUser.username}`);
        console.log(`  邮箱: ${adminUser.email || 'N/A'}`);
        console.log(`  状态: ${adminUser.status}`);
        console.log(`  超级管理员: ${adminUser.isSuperAdmin}`);
        console.log(`  密码哈希: ${adminUser.password.substring(0, 20)}...`);
      } else {
        console.log('❌ 未找到admin用户');
      }
    } catch (error) {
      console.log('❌ 查找admin用户失败:', error.message);
    }
    
    await app.close();
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error(error.stack);
  }
}

// 执行检查
if (require.main === module) {
  checkAdminUser()
    .then(() => {
      console.log('✅ 检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 检查失败:', error);
      process.exit(1);
    });
}

module.exports = { checkAdminUser };
