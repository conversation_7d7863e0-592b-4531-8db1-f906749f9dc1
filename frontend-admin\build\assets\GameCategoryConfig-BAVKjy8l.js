import{j as e}from"./index-DD0gcXtR.js";import{a as f}from"./react-BUTTOX-3.js";import{D as B,C as M,P as V}from"./react-beautiful-dnd.esm-CIUISnkR.js";import{aq as k,ao as q,ap as G,Q as R,T as N,am as A,an as z,n as D,d as p,u as P,E as w,aR as F,s as O,M as _,av as b,I as $,X as K,aT as L,aC as U,aE as H,aU as Q,p as X,aV as J}from"./antd-FyCAPQKa.js";const{Option:le}=X,{Text:I,Title:W}=N,{Panel:ie}=J,Y=({visible:n,onCancel:l,onOk:u,applications:c,selectedGameIds:a})=>{const[o,g]=f.useState([]),[h,S]=f.useState([]);f.useEffect(()=>{g(a.map(s=>s.toString()))},[a]);const j=s=>{g(s)},y=(s,t)=>{S([...s,...t])},T=()=>{const s=c.filter(t=>o.includes(t.id.toString()));u(s)},E=s=>{var t;return{key:s.id.toString(),label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8},children:[e.jsx(K,{src:s.iconUrl,size:"small",icon:e.jsx(L,{})}),e.jsxs("div",{children:[e.jsx("div",{children:s.name}),e.jsx(I,{type:"secondary",style:{fontSize:12},children:((t=s.provider)==null?void 0:t.name)||"未知供应商"})]})]}),value:s.id.toString()}},x=c.map(E);return e.jsx(_,{title:"选择游戏",open:n,onCancel:l,onOk:T,width:800,okText:"确定",cancelText:"取消",children:e.jsx(Q,{dataSource:x,titles:["可选游戏","已选游戏"],targetKeys:o,selectedKeys:h,onChange:j,onSelectChange:y,render:s=>s.label,listStyle:{width:350,height:400},showSearch:!0,filterOption:(s,t)=>t.label.props.children[1].props.children[0].props.children.toLowerCase().includes(s.toLowerCase())})})},Z=({category:n,index:l,applications:u,onUpdate:c,onDelete:a,disabled:o})=>{const[g,h]=f.useState(!1),[S,j]=f.useState(!1),[y]=b.useForm(),T=n.games.map(r=>u.find(i=>i.id===r.applicationId)).filter(Boolean),E=()=>{y.setFieldsValue(n.categoryTitle),j(!0)},x=async()=>{try{const r=await y.validateFields();c(l,{...n,categoryTitle:r}),j(!1)}catch(r){console.error("标题验证失败:",r)}},s=r=>{const i=r.map((d,m)=>({applicationId:d.id,sortOrder:m+1}));c(l,{...n,games:i}),h(!1)},t=r=>{if(!r.destination)return;const i=Array.from(n.games),[d]=i.splice(r.source.index,1);i.splice(r.destination.index,0,d);const m=i.map((C,v)=>({...C,sortOrder:v+1}));c(l,{...n,games:m})};return e.jsxs(k,{size:"small",title:e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8},children:[e.jsx(A,{}),e.jsxs("span",{children:["分类组 ",l+1]}),e.jsxs(z,{color:n.games.length>=4?"green":"orange",children:[n.games.length," 个游戏"]})]}),e.jsxs(R,{children:[e.jsx(D,{title:"编辑标题",children:e.jsx(p,{type:"text",size:"small",icon:e.jsx(U,{}),onClick:E,disabled:o})}),e.jsx(D,{title:"删除分类组",children:e.jsx(p,{type:"text",size:"small",danger:!0,icon:e.jsx(H,{}),onClick:()=>a(l),disabled:o})})]})]}),style:{marginBottom:16},children:[e.jsxs("div",{style:{marginBottom:12},children:[e.jsx(I,{strong:!0,children:"分类标题："}),S?e.jsxs(b,{form:y,layout:"inline",style:{marginTop:8},children:[e.jsx(b.Item,{name:"zh",label:"中文",rules:[{required:!0,message:"请输入中文标题"}],children:e.jsx($,{placeholder:"中文标题"})}),e.jsx(b.Item,{name:"en",label:"英文",rules:[{required:!0,message:"请输入英文标题"}],children:e.jsx($,{placeholder:"English Title"})}),e.jsx(b.Item,{children:e.jsxs(R,{children:[e.jsx(p,{type:"primary",size:"small",onClick:x,children:"保存"}),e.jsx(p,{size:"small",onClick:()=>j(!1),children:"取消"})]})})]}):e.jsxs("div",{style:{marginTop:4},children:[e.jsx(z,{color:"blue",children:n.categoryTitle.zh}),e.jsx(z,{color:"green",children:n.categoryTitle.en})]})]}),e.jsxs("div",{children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:8},children:[e.jsx(I,{strong:!0,children:"游戏列表："}),e.jsx(p,{type:"dashed",size:"small",icon:e.jsx(P,{}),onClick:()=>h(!0),disabled:o,children:"选择游戏"})]}),T.length===0?e.jsx(w,{image:w.PRESENTED_IMAGE_SIMPLE,description:"暂无游戏，请点击选择游戏",style:{margin:"20px 0"}}):e.jsx(B,{onDragEnd:t,children:e.jsx(M,{droppableId:`category-${l}`,children:r=>e.jsxs("div",{...r.droppableProps,ref:r.innerRef,children:[T.map((i,d)=>e.jsx(V,{draggableId:`game-${i.id}`,index:d,isDragDisabled:o,children:(m,C)=>{var v;return e.jsxs("div",{ref:m.innerRef,...m.draggableProps,style:{...m.draggableProps.style,marginBottom:8,padding:8,border:"1px solid #d9d9d9",borderRadius:4,backgroundColor:C.isDragging?"#f0f0f0":"#fff",display:"flex",alignItems:"center",gap:8},children:[e.jsx("div",{...m.dragHandleProps,children:e.jsx(F,{style:{color:"#999"}})}),e.jsx(K,{src:i.iconUrl,size:"small",icon:e.jsx(L,{})}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{children:i.name}),e.jsx(I,{type:"secondary",style:{fontSize:12},children:((v=i.provider)==null?void 0:v.name)||"未知供应商"})]}),e.jsxs(I,{type:"secondary",children:["#",d+1]})]})}},i.id)),r.placeholder]})})})]}),e.jsx(Y,{visible:g,onCancel:()=>h(!1),onOk:s,applications:u,selectedGameIds:n.games.map(r=>r.applicationId)})]})},ae=({value:n=[],onChange:l,applications:u=[],disabled:c=!1})=>{const[a,o]=f.useState(n);f.useEffect(()=>{o(n)},[n]);const g=()=>{const s={categoryTitle:{zh:`分类组 ${a.length+1}`,en:`Category ${a.length+1}`},games:[],sortOrder:a.length+1},t=[...a,s];o(t),l==null||l(t)},h=(s,t)=>{const r=[...a];r[s]=t,o(r),l==null||l(r)},S=s=>{const r=a.filter((i,d)=>d!==s).map((i,d)=>({...i,sortOrder:d+1}));o(r),l==null||l(r)},j=s=>{if(!s.destination)return;const t=Array.from(a),[r]=t.splice(s.source.index,1);t.splice(s.destination.index,0,r);const i=t.map((d,m)=>({...d,sortOrder:m+1}));o(i),l==null||l(i)},y=()=>{_.confirm({title:"确认清空",content:"确定要清空所有分类组吗？此操作不可恢复。",onOk:()=>{o([]),l==null||l([]),O.success("已清空所有分类组")}})},T=()=>{const s=[{categoryTitle:{zh:"热门游戏",en:"Popular Games"},games:[],sortOrder:1},{categoryTitle:{zh:"最新游戏",en:"New Games"},games:[],sortOrder:2},{categoryTitle:{zh:"经典游戏",en:"Classic Games"},games:[],sortOrder:3},{categoryTitle:{zh:"推荐游戏",en:"Recommended Games"},games:[],sortOrder:4}];o(s),l==null||l(s),O.success("已添加默认分类组")},x=(()=>{const s=[];return a.length<4&&s.push("至少需要4个分类组"),a.forEach((t,r)=>{(!t.categoryTitle.zh||!t.categoryTitle.en)&&s.push(`第${r+1}个分类组标题不完整`),t.games.length<4&&s.push(`第${r+1}个分类组至少需要4个游戏`)}),{isValid:s.length===0,errors:s}})();return e.jsxs("div",{children:[e.jsxs(k,{size:"small",style:{marginBottom:16},children:[e.jsxs(q,{justify:"space-between",align:"middle",children:[e.jsx(G,{children:e.jsxs(R,{children:[e.jsxs(W,{level:5,style:{margin:0},children:[e.jsx(A,{})," 游戏分类组配置"]}),e.jsxs(z,{color:x.isValid?"green":"red",children:[a.length,"/4+ 个分类组"]}),!x.isValid&&e.jsx(D,{title:x.errors.join("; "),children:e.jsx(z,{color:"red",children:"验证失败"})})]})}),e.jsx(G,{children:e.jsxs(R,{children:[e.jsx(p,{type:"dashed",size:"small",onClick:T,disabled:c,children:"添加默认分类"}),e.jsx(p,{type:"primary",size:"small",icon:e.jsx(P,{}),onClick:g,disabled:c,children:"添加分类组"}),e.jsx(p,{danger:!0,size:"small",onClick:y,disabled:c||a.length===0,children:"清空全部"})]})})]}),!x.isValid&&e.jsxs("div",{style:{marginTop:12,padding:8,backgroundColor:"#fff2f0",borderRadius:4},children:[e.jsx(I,{type:"danger",children:e.jsx("strong",{children:"配置错误："})}),e.jsx("ul",{style:{margin:"4px 0 0 20px",color:"#ff4d4f"},children:x.errors.map((s,t)=>e.jsx("li",{children:s},t))})]})]}),a.length===0?e.jsx(k,{children:e.jsx(w,{image:w.PRESENTED_IMAGE_SIMPLE,description:"暂无分类组配置",style:{margin:"40px 0"},children:e.jsx(p,{type:"primary",icon:e.jsx(P,{}),onClick:g,children:"添加第一个分类组"})})}):e.jsx(B,{onDragEnd:j,children:e.jsx(M,{droppableId:"categories",children:s=>e.jsxs("div",{...s.droppableProps,ref:s.innerRef,children:[a.map((t,r)=>e.jsx(V,{draggableId:`category-${r}`,index:r,isDragDisabled:c,children:(i,d)=>e.jsx("div",{ref:i.innerRef,...i.draggableProps,style:{...i.draggableProps.style,marginBottom:16},children:e.jsxs("div",{style:{display:"flex",alignItems:"flex-start",gap:8},children:[e.jsx("div",{...i.dragHandleProps,style:{marginTop:12,cursor:c?"not-allowed":"grab"},children:e.jsx(F,{style:{color:"#999",fontSize:16}})}),e.jsx("div",{style:{flex:1},children:e.jsx(Z,{category:t,index:r,applications:u,onUpdate:h,onDelete:S,disabled:c})})]})})},`category-${r}`)),s.placeholder]})})})]})};export{ae as default};
