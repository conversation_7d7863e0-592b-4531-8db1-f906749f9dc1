"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const entities_1 = require("./system/entities");
const entities_2 = require("./app/entities");
const vip_config_v2_entity_1 = require("./app/config/entities/vip-config-v2.entity");
const membership_card_config_entity_1 = require("./app/config/entities/membership-card-config.entity");
const gold_recharge_config_entity_1 = require("./app/config/entities/gold-recharge-config.entity");
const balance_recharge_config_entity_1 = require("./app/config/entities/balance-recharge-config.entity");
const balance_recharge_limit_entity_1 = require("./app/config/entities/balance-recharge-limit.entity");
const ad_config_entity_1 = require("./app/config/entities/ad-config.entity");
const entities_3 = require("./app/config/entities");
const system_module_1 = require("./system/system.module");
const app_module_1 = require("./app/app.module");
const api_module_1 = require("./api/api.module");
const database_health_service_1 = require("./common/database-health.service");
const database_health_controller_1 = require("./common/database-health.controller");
const database_warmup_service_1 = require("./common/database-warmup.service");
const database_monitor_service_1 = require("./common/database-monitor.service");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => {
                    return {
                        type: 'postgres',
                        host: configService.get('DB_HOST'),
                        port: +configService.get('DB_PORT'),
                        username: configService.get('DB_USERNAME'),
                        password: configService.get('DB_PASSWORD'),
                        database: configService.get('DB_DATABASE'),
                        entities: [
                            entities_1.SysUser, entities_1.SysRole, entities_1.SysPermission, entities_1.SysMenu, entities_1.SysWorkbook,
                            entities_2.AppUser, entities_2.AppCategory, entities_2.AppProduct, entities_2.AppOrder, entities_2.AppOrderItem, entities_2.AppUserAddress, entities_2.AppShoppingCart, entities_2.AppUserFavorite,
                            entities_2.ApplicationProvider, entities_2.ProviderEnvironment, entities_2.Application,
                            entities_2.MarketingChannel, entities_2.MarketingAd, entities_2.PromotionalPage,
                            entities_2.RiskEvent, entities_2.DeviceLogRealtime, entities_2.DeviceLogHistory,
                            entities_2.CashTransaction, entities_2.GoldTransaction, entities_2.RechargeTransaction,
                            vip_config_v2_entity_1.VipConfigV2,
                            membership_card_config_entity_1.MembershipCardConfig,
                            gold_recharge_config_entity_1.GoldRechargeConfig,
                            balance_recharge_config_entity_1.BalanceRechargeConfig,
                            balance_recharge_limit_entity_1.BalanceRechargeLimit,
                            ad_config_entity_1.AdConfig,
                            entities_3.AppHomeConfig,
                            entities_3.AppHomeRecommendedGame,
                            entities_3.AppHomeGameCategory,
                            entities_3.AppHomeCategoryGame,
                        ],
                        synchronize: false,
                        logging: ['error'],
                        logger: 'advanced-console',
                        ssl: configService.get('ENVIRONMENT') === 'supabase' ? {
                            rejectUnauthorized: false,
                        } : false,
                        poolSize: +configService.get('DB_POOL_SIZE', 15),
                        extra: {
                            connectionTimeoutMillis: +configService.get('DB_CONNECTION_TIMEOUT', 30000),
                            acquireTimeoutMillis: +configService.get('DB_ACQUIRE_TIMEOUT', 30000),
                            createTimeoutMillis: +configService.get('DB_CREATE_TIMEOUT', 30000),
                            query_timeout: +configService.get('DB_QUERY_TIMEOUT', 120000),
                            statement_timeout: +configService.get('DB_QUERY_TIMEOUT', 120000),
                            max: +configService.get('DB_MAX_CONNECTIONS', 20),
                            min: +configService.get('DB_MIN_CONNECTIONS', 5),
                            idleTimeoutMillis: +configService.get('DB_IDLE_TIMEOUT', 300000),
                            keepAlive: configService.get('DB_KEEP_ALIVE', 'true') === 'true',
                            keepAliveInitialDelayMillis: +configService.get('DB_KEEP_ALIVE_DELAY', 30000),
                            application_name: 'inapp2-backend',
                            tcp_keepalives_idle: 600,
                            tcp_keepalives_interval: 30,
                            tcp_keepalives_count: 3,
                            parseInputDatesAsUTC: true,
                            allowExitOnIdle: false,
                        },
                        retryAttempts: +configService.get('DB_RETRY_ATTEMPTS', 3),
                        retryDelay: +configService.get('DB_RETRY_DELAY', 2000),
                    };
                },
                inject: [config_1.ConfigService],
            }),
            system_module_1.SystemModule,
            app_module_1.AppBusinessModule,
            api_module_1.ApiModule,
            core_1.RouterModule.register([
                {
                    path: 'system',
                    module: system_module_1.SystemModule,
                },
                {
                    path: 'app',
                    module: app_module_1.AppBusinessModule,
                },
                {
                    path: 'api',
                    module: api_module_1.ApiModule,
                },
            ]),
        ],
        controllers: [app_controller_1.AppController, database_health_controller_1.DatabaseHealthController],
        providers: [app_service_1.AppService, database_health_service_1.DatabaseHealthService, database_warmup_service_1.DatabaseWarmupService, database_monitor_service_1.DatabaseMonitorService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map