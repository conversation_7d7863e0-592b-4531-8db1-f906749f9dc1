const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testRoutesService() {
  try {
    console.log('🔍 测试路由服务...');
    
    // 创建应用实例
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // 获取路由服务
    const { SystemRoutesService } = require('../dist/system/routes/routes.service');
    const routesService = app.get(SystemRoutesService);
    
    // 测试获取admin用户路由
    console.log('\n📋 获取admin用户路由:');
    const userRoles = ['admin', 'super_admin'];
    const isSuperAdmin = true;
    
    const routes = await routesService.getRoutes(userRoles, isSuperAdmin);
    
    console.log(`路由总数: ${routes.length}`);
    
    // 查找VIP相关的路由
    const vipRoutes = routes.filter(route => 
      route.path?.includes('vip') || 
      route.handle?.title?.includes('VIP') ||
      route.handle?.title?.includes('vip')
    );
    
    console.log(`\nVIP相关路由数量: ${vipRoutes.length}`);
    vipRoutes.forEach(route => {
      console.log(`  - 路径: ${route.path}`);
      console.log(`    标题: ${route.handle?.title}`);
      console.log(`    权限代码: ${route.handle?.permissionCode}`);
      console.log(`    角色: ${JSON.stringify(route.handle?.roles)}`);
      console.log('');
    });
    
    // 查找配置管理相关的路由
    const configRoutes = routes.filter(route => 
      route.path?.includes('config') || 
      route.handle?.title?.includes('配置')
    );
    
    console.log(`配置管理相关路由数量: ${configRoutes.length}`);
    configRoutes.forEach(route => {
      console.log(`  - 路径: ${route.path}`);
      console.log(`    标题: ${route.handle?.title}`);
      console.log(`    权限代码: ${route.handle?.permissionCode}`);
      console.log(`    子路由数量: ${route.children?.length || 0}`);
      if (route.children?.length) {
        route.children.forEach(child => {
          console.log(`      - 子路径: ${child.path}`);
          console.log(`        子标题: ${child.handle?.title}`);
          console.log(`        子权限代码: ${child.handle?.permissionCode}`);
        });
      }
      console.log('');
    });
    
    await app.close();
    
  } catch (error) {
    console.error('测试失败:', error.message);
    console.error(error.stack);
  }
}

testRoutesService();
