"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdConfigQueryDto = exports.UpdateAdConfigDto = exports.CreateAdConfigDto = exports.GameCategoryResponseDto = exports.CategoryGameResponseDto = exports.RecommendedGameResponseDto = exports.GameInfoResponseDto = exports.AdInfoResponseDto = exports.AppHomeConfigPageResponseDto = exports.AppHomeConfigListResponseDto = exports.AppHomeConfigDetailResponseDto = exports.QueryAppHomeConfigDto = exports.UpdateAppHomeConfigDto = exports.GameCategoryDto = exports.CategoryGameDto = exports.RecommendedGameDto = exports.CreateAppHomeConfigDto = void 0;
var create_app_home_config_dto_1 = require("./create-app-home-config.dto");
Object.defineProperty(exports, "CreateAppHomeConfigDto", { enumerable: true, get: function () { return create_app_home_config_dto_1.CreateAppHomeConfigDto; } });
Object.defineProperty(exports, "RecommendedGameDto", { enumerable: true, get: function () { return create_app_home_config_dto_1.RecommendedGameDto; } });
Object.defineProperty(exports, "CategoryGameDto", { enumerable: true, get: function () { return create_app_home_config_dto_1.CategoryGameDto; } });
Object.defineProperty(exports, "GameCategoryDto", { enumerable: true, get: function () { return create_app_home_config_dto_1.GameCategoryDto; } });
var update_app_home_config_dto_1 = require("./update-app-home-config.dto");
Object.defineProperty(exports, "UpdateAppHomeConfigDto", { enumerable: true, get: function () { return update_app_home_config_dto_1.UpdateAppHomeConfigDto; } });
var query_app_home_config_dto_1 = require("./query-app-home-config.dto");
Object.defineProperty(exports, "QueryAppHomeConfigDto", { enumerable: true, get: function () { return query_app_home_config_dto_1.QueryAppHomeConfigDto; } });
var app_home_config_response_dto_1 = require("./app-home-config-response.dto");
Object.defineProperty(exports, "AppHomeConfigDetailResponseDto", { enumerable: true, get: function () { return app_home_config_response_dto_1.AppHomeConfigDetailResponseDto; } });
Object.defineProperty(exports, "AppHomeConfigListResponseDto", { enumerable: true, get: function () { return app_home_config_response_dto_1.AppHomeConfigListResponseDto; } });
Object.defineProperty(exports, "AppHomeConfigPageResponseDto", { enumerable: true, get: function () { return app_home_config_response_dto_1.AppHomeConfigPageResponseDto; } });
Object.defineProperty(exports, "AdInfoResponseDto", { enumerable: true, get: function () { return app_home_config_response_dto_1.AdInfoResponseDto; } });
Object.defineProperty(exports, "GameInfoResponseDto", { enumerable: true, get: function () { return app_home_config_response_dto_1.GameInfoResponseDto; } });
Object.defineProperty(exports, "RecommendedGameResponseDto", { enumerable: true, get: function () { return app_home_config_response_dto_1.RecommendedGameResponseDto; } });
Object.defineProperty(exports, "CategoryGameResponseDto", { enumerable: true, get: function () { return app_home_config_response_dto_1.CategoryGameResponseDto; } });
Object.defineProperty(exports, "GameCategoryResponseDto", { enumerable: true, get: function () { return app_home_config_response_dto_1.GameCategoryResponseDto; } });
var create_ad_config_dto_1 = require("./create-ad-config.dto");
Object.defineProperty(exports, "CreateAdConfigDto", { enumerable: true, get: function () { return create_ad_config_dto_1.CreateAdConfigDto; } });
var update_ad_config_dto_1 = require("./update-ad-config.dto");
Object.defineProperty(exports, "UpdateAdConfigDto", { enumerable: true, get: function () { return update_ad_config_dto_1.UpdateAdConfigDto; } });
var ad_config_query_dto_1 = require("./ad-config-query.dto");
Object.defineProperty(exports, "AdConfigQueryDto", { enumerable: true, get: function () { return ad_config_query_dto_1.AdConfigQueryDto; } });
//# sourceMappingURL=index.js.map