import{r as t}from"./index-DD0gcXtR.js";function n(o){return t.get("workbook/list",{searchParams:o}).json()}function k(o){return t.post("workbook",{json:o}).json()}function s(o,r){return t.patch(`workbook/${o}`,{json:r}).json()}function a(o){return t.delete(`workbook/${o}`).json()}function c(o){return t.get(`workbook/by-type/${o}`).json()}function b(o,r){return t.patch(`workbook/${o}`,{json:{status:r}}).json()}export{k as a,s as b,n as c,b as d,a as e,c as f};
