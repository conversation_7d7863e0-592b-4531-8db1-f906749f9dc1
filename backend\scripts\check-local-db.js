// 检查本地数据库的脚本
const { Client } = require('pg');

async function checkLocalDB() {
  const client = new Client({
    host: '**************',
    port: 5435,
    database: 'inapp2',
    user: 'user_jJSpPW',
    password: 'password_DmrhYX'
  });

  try {
    console.log('🔍 检查本地数据库...');
    await client.connect();
    console.log('✅ 本地数据库连接成功');

    // 1. 检查admin用户信息
    console.log('\n👤 检查本地admin用户信息:');
    const adminUser = await client.query(`
      SELECT id, username, is_super_admin, status 
      FROM sys_users 
      WHERE username = 'admin';
    `);
    
    if (adminUser.rows.length === 0) {
      console.log('❌ 本地数据库未找到admin用户');
      return;
    }
    
    const user = adminUser.rows[0];
    console.log(`用户ID: ${user.id}`);
    console.log(`用户名: ${user.username}`);
    console.log(`超级管理员: ${user.is_super_admin}`);
    console.log(`状态: ${user.status}`);

    // 2. 检查所有角色
    console.log('\n📋 检查本地所有角色:');
    const allRoles = await client.query(`
      SELECT id, name, code, status 
      FROM sys_roles 
      ORDER BY id;
    `);
    
    console.log(`本地系统中的角色 (${allRoles.rows.length} 个):`);
    allRoles.rows.forEach(role => {
      console.log(`  ID: ${role.id}, 名称: ${role.name}, 代码: ${role.code}, 状态: ${role.status}`);
    });

    // 3. 检查用户角色关联表
    console.log('\n🔗 检查本地用户角色关联表:');
    const userRoles = await client.query(`
      SELECT ur.user_id, ur.role_id, u.username, r.name as role_name, r.code as role_code
      FROM sys_user_roles ur
      LEFT JOIN sys_users u ON ur.user_id = u.id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE ur.user_id = $1;
    `, [user.id]);
    
    console.log(`本地admin用户的角色关联 (${userRoles.rows.length} 条记录):`);
    userRoles.rows.forEach(ur => {
      console.log(`  用户ID: ${ur.user_id}, 角色ID: ${ur.role_id}, 角色名称: ${ur.role_name}, 角色代码: ${ur.role_code}`);
    });

    // 4. 检查所有用户角色关联
    console.log('\n📊 检查本地所有用户角色关联:');
    const allUserRoles = await client.query(`
      SELECT ur.user_id, ur.role_id, u.username, r.name as role_name, r.code as role_code
      FROM sys_user_roles ur
      LEFT JOIN sys_users u ON ur.user_id = u.id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      ORDER BY ur.user_id, ur.role_id;
    `);
    
    console.log(`本地所有用户角色关联 (${allUserRoles.rows.length} 条记录):`);
    allUserRoles.rows.forEach(ur => {
      console.log(`  用户: ${ur.username} (ID: ${ur.user_id}) -> 角色: ${ur.role_name} (ID: ${ur.role_id}, 代码: ${ur.role_code})`);
    });

    // 5. 测试完整的LEFT JOIN查询
    console.log('\n🧪 测试本地完整的LEFT JOIN查询:');
    const fullQuery = await client.query(`
      SELECT 
        u.id, u.username, u.is_super_admin,
        r.id as role_id, r.name as role_name, r.code as role_code
      FROM sys_users u
      LEFT JOIN sys_user_roles ur ON u.id = ur.user_id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE u.username = $1
      ORDER BY r.id;
    `, ['admin']);
    
    console.log(`本地完整LEFT JOIN查询结果 (${fullQuery.rows.length} 条记录):`);
    fullQuery.rows.forEach(row => {
      console.log(`  用户: ${row.username} (ID: ${row.id}, 超级管理员: ${row.is_super_admin})`);
      if (row.role_id) {
        console.log(`    角色: ${row.role_name} (ID: ${row.role_id}, 代码: ${row.role_code})`);
      } else {
        console.log(`    ❌ 无角色关联`);
      }
    });

    await client.end();
    console.log('\n🎉 本地数据库检查完成！');

  } catch (error) {
    console.error('❌ 本地数据库检查失败:', error.message);
    console.error(error.stack);
    await client.end();
    throw error;
  }
}

// 执行检查
if (require.main === module) {
  checkLocalDB()
    .then(() => {
      console.log('✅ 本地数据库检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 本地数据库检查失败:', error);
      process.exit(1);
    });
}

module.exports = { checkLocalDB };
