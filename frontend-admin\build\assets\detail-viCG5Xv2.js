import{j as e}from"./index-DD0gcXtR.js";import{l as E,h as O,a as t}from"./react-BUTTOX-3.js";import{a as P,u as _}from"./index-CmJsfaTQ.js";import{S as w,I as R,C as L,B as q,a as z}from"./types-DmGJpzbp.js";import A from"./EnvironmentConfig-XxoVsJUD.js";import{av as l,ah as B,aq as i,d as u,Q as M,aj as F,a6 as f,I as a,p as n,q as b,ay as G,s as h}from"./antd-FyCAPQKa.js";const{TabPane:I}=f,{TextArea:U}=a,{Option:c}=n;function $(){const{id:r}=E(),o=O(),[x]=l.useForm(),[g,j]=t.useState(!1),[y,p]=t.useState(!1),[d,v]=t.useState(null),[N,S]=t.useState("business"),T=async()=>{if(r){j(!0);try{const s=await P(Number(r));s.code===200&&(v(s.result),x.setFieldsValue(s.result))}catch{h.error("获取供应商信息失败")}finally{j(!1)}}};t.useEffect(()=>{T()},[r]);const C=async s=>{if(r){p(!0);try{const m=await _(Number(r),s);m.code===200&&(h.success("保存成功"),v(m.result))}catch{h.error("保存失败")}finally{p(!1)}}};return g?e.jsx("div",{className:"flex justify-center items-center h-96",children:e.jsx(B,{size:"large"})}):d?e.jsx("div",{className:"p-6",children:e.jsxs(i,{children:[e.jsx("div",{className:"mb-4",children:e.jsxs(M,{children:[e.jsx(u,{icon:e.jsx(F,{}),onClick:()=>o("/app/supplier"),children:"返回列表"}),e.jsxs("h2",{className:"text-xl font-semibold mb-0",children:["配置: ",d.name]})]})}),e.jsxs(f,{activeKey:N,onChange:S,children:[e.jsx(I,{tab:"商务信息",children:e.jsxs(l,{form:x,layout:"vertical",onFinish:C,initialValues:d,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(i,{title:"基本信息",size:"small",children:[e.jsx(l.Item,{label:"供应商名称",name:"name",rules:[{required:!0,message:"请输入供应商名称"}],children:e.jsx(a,{placeholder:"请输入供应商名称"})}),e.jsx(l.Item,{label:"供应商代码",name:"providerCode",rules:[{required:!0,message:"请输入供应商代码"}],children:e.jsx(a,{placeholder:"请输入供应商代码"})}),e.jsx(l.Item,{label:"合作状态",name:"status",rules:[{required:!0,message:"请选择合作状态"}],children:e.jsx(n,{placeholder:"请选择合作状态",children:w.map(s=>e.jsx(c,{value:s.value,children:s.label},s.value))})}),e.jsx(l.Item,{label:"技术集成方案",name:"integrationType",rules:[{required:!0,message:"请选择技术集成方案"}],children:e.jsx(n,{placeholder:"请选择技术集成方案",children:R.map(s=>e.jsx(c,{value:s.value,children:s.label},s.value))})})]}),e.jsxs(i,{title:"商务条款",size:"small",children:[e.jsx(l.Item,{label:"合作模式",name:"commercialModelType",children:e.jsx(n,{placeholder:"请选择合作模式",allowClear:!0,children:L.map(s=>e.jsx(c,{value:s.value,children:s.label},s.value))})}),e.jsx(l.Item,{label:"费率/金额 (%)",name:"rateValue",children:e.jsx(b,{placeholder:"请输入费率",min:0,max:100,precision:4,style:{width:"100%"}})}),e.jsx(l.Item,{label:"结算周期",name:"billingCycle",children:e.jsx(n,{placeholder:"请选择结算周期",allowClear:!0,children:q.map(s=>e.jsx(c,{value:s.value,children:s.label},s.value))})}),e.jsx(l.Item,{label:"结算币种",name:"billingCurrency",children:e.jsx(n,{placeholder:"请选择结算币种",allowClear:!0,children:z.map(s=>e.jsx(c,{value:s.value,children:s.label},s.value))})}),e.jsx(l.Item,{label:"最低保证金额",name:"minimumGuarantee",children:e.jsx(b,{placeholder:"请输入最低保证金额",min:0,precision:2,style:{width:"100%"}})})]}),e.jsxs(i,{title:"联系人信息",size:"small",children:[e.jsx(l.Item,{label:"商务经理",name:"amName",children:e.jsx(a,{placeholder:"请输入商务经理姓名"})}),e.jsx(l.Item,{label:"商务邮箱",name:"amEmail",rules:[{type:"email",message:"请输入正确的邮箱格式"}],children:e.jsx(a,{placeholder:"请输入商务经理邮箱"})}),e.jsx(l.Item,{label:"技术支持邮箱",name:"techSupportEmail",rules:[{type:"email",message:"请输入正确的邮箱格式"}],children:e.jsx(a,{placeholder:"请输入技术支持邮箱"})}),e.jsx(l.Item,{label:"财务联系邮箱",name:"financeEmail",rules:[{type:"email",message:"请输入正确的邮箱格式"}],children:e.jsx(a,{placeholder:"请输入财务联系邮箱"})})]}),e.jsx(i,{title:"备注",size:"small",children:e.jsx(l.Item,{label:"内部备注",name:"notes",children:e.jsx(U,{rows:4,placeholder:"请输入内部备注"})})})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx(u,{type:"primary",htmlType:"submit",loading:y,icon:e.jsx(G,{}),size:"large",children:"保存商务信息"})})]})},"business"),e.jsx(I,{tab:"环境配置",children:e.jsx(A,{providerId:Number(r)})},"environment")]})]})}):e.jsx("div",{className:"p-6",children:e.jsx(i,{children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{children:"供应商不存在"}),e.jsx(u,{onClick:()=>o("/app/supplier"),children:"返回列表"})]})})})}export{$ as default};
