const { Client } = require('pg');
const { getDatabaseConfig } = require('../database/config/database-config');

async function checkTableNames() {
  try {
    console.log('🔍 检查数据库表名...');
    
    const config = getDatabaseConfig('local');
    const client = new Client(config);
    await client.connect();

    // 检查所有相关表名
    console.log('\n📋 所有系统相关表:');
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND (table_name LIKE '%role%' 
             OR table_name LIKE '%menu%' 
             OR table_name LIKE '%permission%'
             OR table_name LIKE '%user%')
      ORDER BY table_name
    `);
    console.table(tables.rows);

    await client.end();
    
  } catch (error) {
    console.error('检查失败:', error.message);
    console.error(error.stack);
  }
}

checkTableNames();
