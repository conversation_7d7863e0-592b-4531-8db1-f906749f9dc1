const { Client } = require('pg');

// 数据库连接配置 - 使用Supabase
const client = new Client({
  host: 'db.ytrftwscazjboxbwnrxp.supabase.co',
  port: 5432,
  database: 'postgres',
  user: 'postgres',
  password: 'inapp2backend2024!',
  ssl: {
    rejectUnauthorized: false
  }
});

async function assignVipV2Permissions() {
  try {
    console.log('🚀 开始为超级管理员分配VIP V2权限...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 检查所有角色
    console.log('📋 检查现有角色...');
    const rolesResult = await client.query(`
      SELECT id, name, code FROM sys_roles ORDER BY id;
    `);

    console.log('现有角色:');
    rolesResult.rows.forEach(role => {
      console.log(`- ID: ${role.id}, 名称: ${role.name}, 代码: ${role.code}`);
    });

    // 2. 查找超级管理员角色（尝试多种可能的名称）
    const superAdminRole = await client.query(`
      SELECT id, name, code FROM sys_roles 
      WHERE code = 'super_admin' OR name = 'super_admin' OR name = '超级管理员' OR code = 'admin'
      LIMIT 1;
    `);

    let roleId;
    if (superAdminRole.rows.length > 0) {
      roleId = superAdminRole.rows[0].id;
      console.log(`✅ 找到超级管理员角色: ${superAdminRole.rows[0].name} (ID: ${roleId})`);
    } else {
      // 创建超级管理员角色
      console.log('📝 创建超级管理员角色...');
      const createRoleResult = await client.query(`
        INSERT INTO sys_roles (name, code, status)
        VALUES ('超级管理员', 'super_admin', 1)
        RETURNING id;
      `);
      roleId = createRoleResult.rows[0].id;
      console.log(`✅ 超级管理员角色创建成功，ID: ${roleId}`);
    }

    // 3. 获取所有VIP V2相关权限
    console.log('🔐 获取VIP V2权限...');
    const vipV2Permissions = await client.query(`
      SELECT id, code, name FROM sys_permissions 
      WHERE code LIKE 'config:vip-v2:%' OR code = 'config' OR code = 'config:management'
      ORDER BY code;
    `);

    console.log('VIP V2相关权限:');
    vipV2Permissions.rows.forEach(perm => {
      console.log(`- ${perm.code}: ${perm.name}`);
    });

    // 4. 为超级管理员分配权限
    console.log('👑 分配权限给超级管理员...');
    let assignedCount = 0;
    
    for (const perm of vipV2Permissions.rows) {
      try {
        await client.query(`
          INSERT INTO sys_role_permissions (role_id, permission_id)
          VALUES ($1, $2)
          ON CONFLICT (role_id, permission_id) DO NOTHING;
        `, [roleId, perm.id]);
        assignedCount++;
        console.log(`  ✓ 分配权限: ${perm.code}`);
      } catch (error) {
        console.log(`  ✗ 分配权限失败: ${perm.code} - ${error.message}`);
      }
    }

    console.log(`✅ 成功分配 ${assignedCount} 个权限给超级管理员`);

    // 5. 验证权限分配
    console.log('\n📊 验证权限分配结果...');
    const assignedPermissions = await client.query(`
      SELECT p.code, p.name, p.type
      FROM sys_role_permissions rp
      JOIN sys_permissions p ON rp.permission_id = p.id
      WHERE rp.role_id = $1 AND (p.code LIKE 'config:vip-v2:%' OR p.code = 'config')
      ORDER BY p.code;
    `, [roleId]);

    console.log('已分配的VIP V2权限:');
    assignedPermissions.rows.forEach(perm => {
      console.log(`- ${perm.code}: ${perm.name} (${perm.type})`);
    });

    // 6. 检查超级管理员用户
    console.log('\n👤 检查超级管理员用户...');
    const superAdminUsers = await client.query(`
      SELECT u.id, u.username, u.is_super_admin
      FROM sys_users u
      WHERE u.is_super_admin = true OR u.username = 'admin'
      ORDER BY u.id;
    `);

    console.log('超级管理员用户:');
    superAdminUsers.rows.forEach(user => {
      console.log(`- ID: ${user.id}, 用户名: ${user.username}, 超级管理员: ${user.is_super_admin}`);
    });

    // 7. 为超级管理员用户分配角色
    if (superAdminUsers.rows.length > 0) {
      console.log('🔗 为超级管理员用户分配角色...');
      for (const user of superAdminUsers.rows) {
        try {
          await client.query(`
            INSERT INTO sys_user_roles (user_id, role_id)
            VALUES ($1, $2)
            ON CONFLICT (user_id, role_id) DO NOTHING;
          `, [user.id, roleId]);
          console.log(`  ✓ 用户 ${user.username} 已分配超级管理员角色`);
        } catch (error) {
          console.log(`  ✗ 用户 ${user.username} 角色分配失败: ${error.message}`);
        }
      }
    }

    console.log('\n🎉 VIP V2权限分配完成！');

  } catch (error) {
    console.error('❌ 权限分配过程中发生错误:', error);
    throw error;
  } finally {
    await client.end();
    console.log('📝 数据库连接已关闭');
  }
}

// 执行权限分配
if (require.main === module) {
  assignVipV2Permissions()
    .then(() => {
      console.log('✅ 权限分配完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 权限分配失败:', error);
      process.exit(1);
    });
}

module.exports = { assignVipV2Permissions };
