import{j as e}from"./index-DD0gcXtR.js";import{a as o}from"./react-BUTTOX-3.js";import{ah as A,E as h,aq as f,aJ as T,af as U,X as Y,ac as E,T as C,an as x,aM as F,Q as G,al as b,aN as H}from"./antd-FyCAPQKa.js";import{u as _}from"./index-BpqjFj86.js";const a={"invitation-tree-container":"_invitation-tree-container_f8u3v_2","invitation-tree":"_invitation-tree_f8u3v_2","user-node":"_user-node_f8u3v_44","user-avatar":"_user-avatar_f8u3v_52","user-info":"_user-info_f8u3v_56","user-name":"_user-name_f8u3v_61","user-details":"_user-details_f8u3v_68","stats-card":"_stats-card_f8u3v_77","stats-grid":"_stats-grid_f8u3v_87","stat-item":"_stat-item_f8u3v_94","stat-number":"_stat-number_f8u3v_101","stat-label":"_stat-label_f8u3v_107","invitation-path":"_invitation-path_f8u3v_113","current-user-node":"_current-user-node_f8u3v_169","inviter-node":"_inviter-node_f8u3v_183","invitee-node":"_invitee-node_f8u3v_190"},{Text:R,Title:O}=C,V=({userId:d,loading:L})=>{const[j,p]=o.useState([]),[k,y]=o.useState(!1),[l,S]=o.useState(null),[I,g]=o.useState([]),[q,N]=o.useState([]),M={0:{color:"green",text:"正常"},1:{color:"red",text:"已封禁"},2:{color:"gray",text:"已注销"}},w=async()=>{if(d){y(!0);try{console.log("🔍 获取邀请关系数据，用户ID:",d);const s=await _.getInvitationRelationship(d);console.log("📊 邀请关系数据:",s),S(s);const t=await K(d,s);p(t);const i=$(t);g(i)}catch(s){console.error("❌ 获取邀请关系失败:",s)}finally{y(!1)}}},$=s=>{const t=[],i=n=>{n.forEach(r=>{t.push(r.key),r.children&&i(r.children)})};return i(s),t},K=async(s,t)=>{const i=[];if(t.inviter){const n={key:`inviter-${t.inviter.id}`,title:v(t.inviter,0,!0),user:t.inviter,level:0,children:[]},r=await D(s);if(r){const u={key:`current-${s}`,title:v(r,1,!1,!0),user:r,level:1,isRoot:!0,children:m(t.invitees,2)};n.children=[u]}i.push(n)}else{const n=await D(s);if(n){const r={key:`root-${s}`,title:v(n,0,!1,!0),user:n,level:0,isRoot:!0,children:m(t.invitees,1)};i.push(r)}}return i},D=async s=>{try{const t=await _.getUserDetail(s);return{id:t.id,uid:t.uid,username:t.username,nickname:t.nickname||t.username,avatar:t.avatar||"",status:t.status,createTime:t.createTime,lastLoginTime:t.lastLoginTime||""}}catch(t){return console.error("获取用户详情失败:",t),null}},m=(s,t)=>s.map(i=>({key:`invitee-${i.id}`,title:v(i,t),user:i,level:t,children:[],isLeaf:!1})),z=async s=>{try{const t=await _.getInvitationRelationship(s.user.id);return m(t.invitees,s.level+1)}catch(t){return console.error("加载子节点失败:",t),[]}},P=async s=>{const t=s.key;if(!(s.children&&s.children.length>0)){N(i=>[...i,t]);try{const i=await z(s);p(n=>{const r=u=>u.map(c=>c.key===t?{...c,children:i}:c.children?{...c,children:r(c.children)}:c);return r(n)})}catch(i){console.error("加载子节点失败:",i)}finally{N(i=>i.filter(n=>n!==t))}}},v=(s,t,i=!1,n=!1)=>{const r=M[s.status]||{color:"default",text:"未知"},u=n?a["current-user-node"]:i?a["inviter-node"]:a["invitee-node"];return e.jsxs("div",{className:`${a["user-node"]} ${u}`,children:[e.jsx("div",{className:a["user-avatar"],children:e.jsx(Y,{size:t===0?"large":"default",src:s.avatar,icon:e.jsx(E,{})})}),e.jsxs("div",{className:a["user-info"],children:[e.jsxs("div",{className:a["user-name"],children:[e.jsx(R,{strong:t===0||n,children:s.nickname||s.username}),e.jsxs(R,{type:"secondary",style:{fontSize:"12px"},children:["UID: ",s.uid]}),i&&e.jsx(x,{color:"gold",icon:e.jsx(F,{}),children:"邀请人"}),n&&e.jsx(x,{color:"blue",icon:e.jsx(E,{}),children:"当前用户"}),e.jsx(x,{color:r.color,children:r.text})]}),e.jsx("div",{className:a["user-details"],children:e.jsxs(G,{size:"small",split:e.jsx("span",{children:"•"}),children:[e.jsxs("span",{children:[e.jsx(H,{style:{marginRight:4}}),"注册: ",b(s.createTime).format("YYYY-MM-DD")]}),s.lastLoginTime&&e.jsxs("span",{children:["最后登录: ",b(s.lastLoginTime).format("MM-DD HH:mm")]})]})})]})]})};return o.useEffect(()=>{w()},[d]),k||L?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(A,{size:"large"})}):l?e.jsxs("div",{className:"space-y-4",children:[e.jsx(f,{size:"small",className:a["stats-card"],children:e.jsxs("div",{className:a["stats-grid"],children:[e.jsxs("div",{className:a["stat-item"],children:[e.jsx("div",{className:a["stat-number"],children:l.invitationLevel}),e.jsx("div",{className:a["stat-label"],children:"邀请层级"})]}),e.jsxs("div",{className:a["stat-item"],children:[e.jsx("div",{className:a["stat-number"],children:l.directInvitees}),e.jsx("div",{className:a["stat-label"],children:"直接邀请"})]}),e.jsxs("div",{className:a["stat-item"],children:[e.jsx("div",{className:a["stat-number"],children:l.indirectInvitees}),e.jsx("div",{className:a["stat-label"],children:"间接邀请"})]}),e.jsxs("div",{className:a["stat-item"],children:[e.jsx("div",{className:a["stat-number"],children:l.totalInvitees}),e.jsx("div",{className:a["stat-label"],children:"总邀请数"})]})]})}),l.invitationPath&&e.jsx(f,{size:"small",title:e.jsxs(e.Fragment,{children:[e.jsx(T,{})," 邀请路径"]}),children:e.jsx("div",{className:a["invitation-path"],children:l.invitationPath})}),e.jsx(f,{title:e.jsxs(e.Fragment,{children:[e.jsx(T,{})," 邀请关系图"]}),children:j.length>0?e.jsx("div",{className:a["invitation-tree-container"],children:e.jsx(U,{treeData:j,expandedKeys:I,onExpand:g,loadData:P,showLine:{showLeafIcon:!1},className:a["invitation-tree"],blockNode:!0})}):e.jsx(h,{description:"暂无邀请关系",image:h.PRESENTED_IMAGE_SIMPLE})})]}):e.jsx(h,{description:"暂无邀请关系数据",image:h.PRESENTED_IMAGE_SIMPLE})};export{V as default};
