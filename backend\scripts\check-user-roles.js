// 检查用户角色关联的脚本
const { Client } = require('pg');

async function checkUserRoles() {
  const client = new Client({
    host: 'db.ytrftwscazjboxbwnrxp.supabase.co',
    port: 5432,
    database: 'postgres',
    user: 'postgres',
    password: 'inapp2backend2024!',
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('🔍 检查用户角色关联...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 检查admin用户信息
    console.log('\n👤 检查admin用户信息:');
    const adminUser = await client.query(`
      SELECT id, username, is_super_admin, status 
      FROM sys_users 
      WHERE username = 'admin';
    `);
    
    if (adminUser.rows.length === 0) {
      console.log('❌ 未找到admin用户');
      return;
    }
    
    const user = adminUser.rows[0];
    console.log(`用户ID: ${user.id}`);
    console.log(`用户名: ${user.username}`);
    console.log(`超级管理员: ${user.is_super_admin}`);
    console.log(`状态: ${user.status}`);

    // 2. 检查所有角色
    console.log('\n📋 检查所有角色:');
    const allRoles = await client.query(`
      SELECT id, name, code, status 
      FROM sys_roles 
      ORDER BY id;
    `);
    
    console.log('系统中的角色:');
    allRoles.rows.forEach(role => {
      console.log(`  ID: ${role.id}, 名称: ${role.name}, 代码: ${role.code}, 状态: ${role.status}`);
    });

    // 3. 检查用户角色关联表
    console.log('\n🔗 检查用户角色关联表:');
    const userRoles = await client.query(`
      SELECT ur.user_id, ur.role_id, u.username, r.name as role_name, r.code as role_code
      FROM sys_user_roles ur
      LEFT JOIN sys_users u ON ur.user_id = u.id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE ur.user_id = $1;
    `, [user.id]);
    
    console.log(`admin用户的角色关联 (${userRoles.rows.length} 条记录):`);
    userRoles.rows.forEach(ur => {
      console.log(`  用户ID: ${ur.user_id}, 角色ID: ${ur.role_id}, 角色名称: ${ur.role_name}, 角色代码: ${ur.role_code}`);
    });

    // 4. 检查关联表结构
    console.log('\n🏗️ 检查用户角色关联表结构:');
    const tableStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'sys_user_roles'
      ORDER BY ordinal_position;
    `);
    
    console.log('sys_user_roles表结构:');
    tableStructure.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (可空: ${col.is_nullable}, 默认值: ${col.column_default})`);
    });

    // 5. 检查所有用户角色关联
    console.log('\n📊 检查所有用户角色关联:');
    const allUserRoles = await client.query(`
      SELECT ur.user_id, ur.role_id, u.username, r.name as role_name, r.code as role_code
      FROM sys_user_roles ur
      LEFT JOIN sys_users u ON ur.user_id = u.id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      ORDER BY ur.user_id, ur.role_id;
    `);
    
    console.log(`所有用户角色关联 (${allUserRoles.rows.length} 条记录):`);
    allUserRoles.rows.forEach(ur => {
      console.log(`  用户: ${ur.username} (ID: ${ur.user_id}) -> 角色: ${ur.role_name} (ID: ${ur.role_id}, 代码: ${ur.role_code})`);
    });

    // 6. 测试TypeORM查询
    console.log('\n🧪 模拟TypeORM查询:');
    const typeormQuery = await client.query(`
      SELECT 
        u.id, u.username, u.is_super_admin,
        r.id as role_id, r.name as role_name, r.code as role_code
      FROM sys_users u
      LEFT JOIN sys_user_roles ur ON u.id = ur.user_id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE u.username = 'admin';
    `);
    
    console.log('TypeORM风格查询结果:');
    typeormQuery.rows.forEach(row => {
      console.log(`  用户: ${row.username} (ID: ${row.id}, 超级管理员: ${row.is_super_admin})`);
      if (row.role_id) {
        console.log(`    角色: ${row.role_name} (ID: ${row.role_id}, 代码: ${row.role_code})`);
      } else {
        console.log(`    ❌ 无角色关联`);
      }
    });

    await client.end();
    console.log('\n🎉 检查完成！');

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error(error.stack);
    await client.end();
    throw error;
  }
}

// 执行检查
if (require.main === module) {
  checkUserRoles()
    .then(() => {
      console.log('✅ 检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 检查失败:', error);
      process.exit(1);
    });
}

module.exports = { checkUserRoles };
