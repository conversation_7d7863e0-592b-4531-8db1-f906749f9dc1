const { Client } = require('pg');
const { getDatabaseConfig } = require('../database/config/database-config');

async function fixVipMenuComponent() {
  try {
    console.log('🔧 修复VIP菜单组件路径...');
    
    const config = getDatabaseConfig('local');
    const client = new Client(config);
    await client.connect();

    // 检查当前VIP菜单配置
    console.log('\n📋 修复前的VIP菜单配置:');
    const beforeUpdate = await client.query(`
      SELECT id, title, path, component, permission_code
      FROM sys_menus 
      WHERE path LIKE '%vip%'
    `);
    console.table(beforeUpdate.rows);

    // 修复VIP菜单的组件路径
    console.log('\n🔧 更新VIP菜单组件路径...');
    const updateResult = await client.query(`
      UPDATE sys_menus 
      SET component = 'config/vip-v2'
      WHERE path = '/config/vip'
      RETURNING id, title, path, component, permission_code
    `);
    
    console.log('✅ 更新结果:');
    console.table(updateResult.rows);

    // 验证修复结果
    console.log('\n📋 修复后的VIP菜单配置:');
    const afterUpdate = await client.query(`
      SELECT id, title, path, component, permission_code
      FROM sys_menus 
      WHERE path LIKE '%vip%'
    `);
    console.table(afterUpdate.rows);

    await client.end();
    console.log('\n✅ VIP菜单组件路径修复完成！');
    
  } catch (error) {
    console.error('修复失败:', error.message);
    console.error(error.stack);
  }
}

fixVipMenuComponent();
