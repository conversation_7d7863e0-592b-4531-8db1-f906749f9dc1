import{u as T,a as b,j as s,H as l}from"./index-DD0gcXtR.js";import{f as k,D as U,a as B,b as D}from"./detail-D2557-1F.js";import{a as i}from"./react-BUTTOX-3.js";import{B as x}from"./index-C1Do55qr.js";import{B as R}from"./index-DUsnaVFw.js";import{u as y}from"./useMutation-Dm45oaMR.js";import{getConstantColumns as M}from"./constants-BUr7icXD.js";import{aD as j,bU as z,d as F,bH as v,s as m,M as A}from"./antd-FyCAPQKa.js";import"./useQuery-Do4-mmZ5.js";import"./index-Bo3BoCEt.js";import"./Table-Blekjt7O.js";import"./index-DwiaqWIr.js";import"./index-BDysGMcU.js";import"./BaseForm-CUW86psc.js";import"./index-DhoWj0AL.js";import"./index-BhoZk0D4.js";function ee(){const{t:e}=T(),d=i.useRef(),[w,a]=i.useState(!1),[S,u]=i.useState(""),[g,c]=i.useState(null),r=b(t=>t.isSuperAdmin),p=y({mutationFn:B,onSuccess:()=>{m.success(e("common.deleteSuccess")),h()},onError:t=>{m.error(t.message||e("common.deleteFailed"))}}),f=y({mutationFn:D,onSuccess:t=>{const o=t.result.newPassword;A.success({title:e("system.user.resetPasswordSuccess"),content:s.jsxs("div",{children:[s.jsxs("p",{children:[e("system.user.newPassword"),":"]}),s.jsx("p",{style:{fontFamily:"monospace",fontSize:"16px",fontWeight:"bold",color:"#1890ff",backgroundColor:"#f0f0f0",padding:"8px",borderRadius:"4px"},children:o}),s.jsx("p",{style:{color:"#ff4d4f",fontSize:"12px"},children:e("system.user.passwordWarning")})]}),width:500})},onError:t=>{m.error(t.message||e("system.user.resetPasswordFailed"))}}),h=()=>{var t;(t=d.current)==null||t.reload()},P=t=>{a(t),t||c(null)},C=[...M(e),{title:e("common.action"),valueType:"option",key:"option",width:200,fixed:"right",render:(t,o,E,I)=>{const n=[];return r&&n.push(s.jsx(l,{type:"link",size:"small",onClick:()=>{c(o),a(!0),u(e("system.user.editUser"))},children:e("common.edit")},"edit")),r&&n.push(s.jsx(j,{title:e("system.user.resetPasswordConfirm"),onConfirm:()=>{f.mutate({userId:o.id})},okText:e("common.confirm"),cancelText:e("common.cancel"),children:s.jsx(l,{type:"link",size:"small",icon:s.jsx(z,{}),loading:f.isPending,children:e("system.user.resetPassword")})},"reset-password")),r&&!o.isSuperAdmin&&n.push(s.jsx(j,{title:e("common.deleteConfirm"),onConfirm:()=>{p.mutate(o.id)},okText:e("common.confirm"),cancelText:e("common.cancel"),children:s.jsx(l,{type:"link",size:"small",danger:!0,loading:p.isPending,children:e("common.delete")})},"delete")),n}}];return r?s.jsxs(x,{className:"h-full",children:[s.jsx(R,{columns:C,actionRef:d,request:async t=>{const o=await k(t);return{...o,data:o.result.list,total:o.result.total}},headerTitle:e("system.user.title"),toolBarRender:()=>[s.jsx(F,{icon:s.jsx(v,{}),type:"primary",onClick:()=>{c(null),a(!0),u(e("system.user.addUser"))},children:e("common.add")},"add-user")],rowKey:"id",search:{labelWidth:"auto"}}),s.jsx(U,{title:S,open:w,onCloseChange:P,detailData:g,refreshTable:h})]}):s.jsx(x,{className:"h-full",children:s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-center",children:[s.jsx("h3",{children:e("system.user.noPermission")}),s.jsx("p",{children:e("system.user.superAdminOnly")})]})})})}export{ee as default};
