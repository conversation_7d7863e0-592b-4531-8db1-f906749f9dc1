// 测试登录流程的脚本
const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testLoginFlow() {
  try {
    console.log('🚀 启动NestJS应用...');
    const app = await NestFactory.createApplicationContext(AppModule);
    console.log('✅ 应用启动成功');

    const authService = app.get('SystemAuthService');

    console.log('\n🔐 测试登录流程...');

    // 1. 测试用户验证
    console.log('\n📋 测试1: 用户验证');
    const validatedUser = await authService.validateUser('admin', 'admin123');
    
    if (validatedUser) {
      console.log(`  ✅ 用户验证成功: ${validatedUser.username} (ID: ${validatedUser.id})`);
      console.log(`  超级管理员: ${validatedUser.isSuperAdmin}`);
      console.log(`  roles属性存在: ${validatedUser.hasOwnProperty('roles')}`);
      console.log(`  roles类型: ${typeof validatedUser.roles}`);
      console.log(`  roles是数组: ${Array.isArray(validatedUser.roles)}`);
      console.log(`  roles长度: ${validatedUser.roles ? validatedUser.roles.length : 'undefined'}`);
      
      if (validatedUser.roles && validatedUser.roles.length > 0) {
        validatedUser.roles.forEach((role, index) => {
          console.log(`    角色${index + 1}: ${role.name} (${role.code})`);
        });
      }
    } else {
      console.log('  ❌ 用户验证失败');
      return;
    }

    // 2. 测试登录（生成JWT token）
    console.log('\n📋 测试2: 登录生成JWT token');
    const loginResult = await authService.login(validatedUser);
    
    console.log(`  ✅ 登录成功`);
    console.log(`  access_token长度: ${loginResult.access_token.length}`);
    console.log(`  refresh_token长度: ${loginResult.refresh_token.length}`);

    // 3. 解析JWT token内容
    console.log('\n📋 测试3: 解析JWT token内容');
    const jwt = require('jsonwebtoken');
    const decodedToken = jwt.decode(loginResult.access_token);
    
    console.log(`  用户ID: ${decodedToken.sub}`);
    console.log(`  用户名: ${decodedToken.username}`);
    console.log(`  超级管理员: ${decodedToken.isSuperAdmin}`);
    console.log(`  roles属性存在: ${decodedToken.hasOwnProperty('roles')}`);
    console.log(`  roles类型: ${typeof decodedToken.roles}`);
    console.log(`  roles是数组: ${Array.isArray(decodedToken.roles)}`);
    console.log(`  roles长度: ${decodedToken.roles ? decodedToken.roles.length : 'undefined'}`);
    console.log(`  roles内容: ${JSON.stringify(decodedToken.roles)}`);
    
    if (decodedToken.roles && decodedToken.roles.length > 0) {
      decodedToken.roles.forEach((roleCode, index) => {
        console.log(`    角色${index + 1}: ${roleCode}`);
      });
    }

    // 4. 测试JWT验证
    console.log('\n📋 测试4: JWT验证');
    const jwtStrategy = app.get('JwtStrategy');
    
    const validatedPayload = await jwtStrategy.validate(decodedToken);
    
    console.log(`  ✅ JWT验证成功`);
    console.log(`  用户ID: ${validatedPayload.userId}`);
    console.log(`  用户名: ${validatedPayload.username}`);
    console.log(`  超级管理员: ${validatedPayload.isSuperAdmin}`);
    console.log(`  roles属性存在: ${validatedPayload.hasOwnProperty('roles')}`);
    console.log(`  roles类型: ${typeof validatedPayload.roles}`);
    console.log(`  roles是数组: ${Array.isArray(validatedPayload.roles)}`);
    console.log(`  roles长度: ${validatedPayload.roles ? validatedPayload.roles.length : 'undefined'}`);
    console.log(`  roles内容: ${JSON.stringify(validatedPayload.roles)}`);

    // 5. 测试获取用户信息
    console.log('\n📋 测试5: 获取用户信息');
    const userInfo = await authService.getUserInfo(validatedPayload.userId);
    
    if (userInfo) {
      console.log(`  ✅ 获取用户信息成功: ${userInfo.username} (ID: ${userInfo.id})`);
      console.log(`  超级管理员: ${userInfo.isSuperAdmin}`);
      console.log(`  roles属性存在: ${userInfo.hasOwnProperty('roles')}`);
      console.log(`  roles类型: ${typeof userInfo.roles}`);
      console.log(`  roles是数组: ${Array.isArray(userInfo.roles)}`);
      console.log(`  roles长度: ${userInfo.roles ? userInfo.roles.length : 'undefined'}`);
      
      if (userInfo.roles && userInfo.roles.length > 0) {
        userInfo.roles.forEach((role, index) => {
          console.log(`    角色${index + 1}: ${role.name} (${role.code})`);
        });
      }
    }

    await app.close();
    console.log('\n🎉 登录流程测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 执行测试
if (require.main === module) {
  testLoginFlow()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testLoginFlow };
