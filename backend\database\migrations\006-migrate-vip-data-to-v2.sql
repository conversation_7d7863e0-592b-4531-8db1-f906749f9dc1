-- VIP配置数据迁移脚本 V12.0
-- 将旧版本VIP配置数据迁移到新的经济权益系统
-- 执行时间：2025-06-29

-- 1. 备份旧数据
CREATE TABLE IF NOT EXISTS vip_configs_backup AS 
SELECT * FROM vip_configs WHERE 1=1;

-- 2. 创建临时表存储用户当前VIP数据
CREATE TEMPORARY TABLE user_vip_backup AS
SELECT 
    id,
    vip_level,
    vip_points,
    total_recharge_amount,
    total_cash_consumption,
    total_gold_consumption
FROM app_users 
WHERE vip_level > 0;

-- 3. 清空旧的VIP配置表（如果存在冲突数据）
DELETE FROM vip_configs WHERE id IN (
    SELECT id FROM vip_configs_backup
);

-- 4. 插入新的VIP配置数据（基于表1.1的完整配置）
INSERT INTO vip_configs (
    vip_level, level_name, strategic_position, required_exp,
    real_money_recharge_exp_ratio, real_money_flow_exp_ratio, 
    gold_payment_exp_ratio, gold_flow_exp_ratio,
    daily_task_exp_min, daily_task_exp_max,
    daily_gold_reward, buyback_enabled, buyback_rate,
    c2c_fee_rate, rakeback_rate, withdrawal_fee_rate,
    daily_withdrawal_limit, withdrawal_priority,
    task_unlock_required, task_unlock_games_required,
    status, remark, created_by, updated_by
) VALUES 
-- VIP 0: 体验用户
(0, '体验用户', '体验用户/流量基础', 0,
 10.0000, 1.0000, 5.0000, 0.001000, 10, 50,
 0, FALSE, NULL, 15.00, 0.00, NULL, NULL, 1,
 FALSE, 0, 1, '新用户默认等级，无提现资格', 1, 1),

-- VIP 1: 活跃认证
(1, '活跃认证', '活跃认证', 500,
 10.0000, 1.0000, 5.0000, 0.001000, 10, 50,
 50, FALSE, NULL, 15.00, 0.00, 5.00, 2000, 1,
 FALSE, 0, 1, '活跃认证用户，开启基础提现权限', 1, 1),

-- VIP 2: 信任用户
(2, '信任用户', '信任用户', 2000,
 10.0000, 1.0000, 5.0000, 0.001000, 10, 50,
 100, FALSE, NULL, 12.00, 0.25, 4.50, 5000, 1,
 FALSE, 0, 1, '信任用户，享受优惠费率', 1, 1),

-- VIP 3: 忠诚用户
(3, '忠诚用户', '忠诚用户', 10000,
 10.0000, 1.0000, 5.0000, 0.001000, 10, 50,
 200, TRUE, 140, 10.00, 0.50, 4.00, 10000, 2,
 FALSE, 0, 1, '忠诚用户，开启回购权限', 1, 1),

-- VIP 4: 核心用户
(4, '核心用户', '核心用户', 50000,
 10.0000, 1.0000, 5.0000, 0.001000, 10, 50,
 500, TRUE, 135, 8.00, 1.00, 3.50, 25000, 2,
 FALSE, 0, 1, '核心用户，享受更优回购价格', 1, 1),

-- VIP 5: 高级用户
(5, '高级用户', '高级用户', 200000,
 10.0000, 1.0000, 5.0000, 0.001000, 10, 50,
 1000, TRUE, 130, 6.00, 1.50, 3.00, 50000, 2,
 FALSE, 0, 1, '高级用户，享受高级权益', 1, 1),

-- VIP 6: 超级用户
(6, '超级用户', '超级用户', 1000000,
 10.0000, 1.0000, 5.0000, 0.001000, 10, 50,
 2000, TRUE, 128, 4.00, 2.00, 2.50, 100000, 3,
 FALSE, 0, 1, '超级用户，享受顶级权益', 1, 1),

-- VIP 7: 至尊用户
(7, '至尊用户', '至尊用户', 5000000,
 10.0000, 1.0000, 5.0000, 0.001000, 10, 50,
 5000, TRUE, 125, 2.00, 2.50, 2.00, NULL, 3,
 FALSE, 0, 1, '至尊用户，享受最高权益，无提现限额', 1, 1);

-- 5. 为用户添加EXP字段（如果不存在）
ALTER TABLE app_users 
ADD COLUMN IF NOT EXISTS total_exp BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_exp_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 6. 根据旧的积分系统计算用户的初始EXP
-- 使用新的EXP计算规则：真金充值(1 INR = 10 EXP) + 真金流水(1 INR = 1 EXP) + 金币付费(1 INR = 5 EXP)
UPDATE app_users 
SET total_exp = COALESCE(
    (total_recharge_amount * 10) +  -- 真金充值EXP
    (total_cash_consumption * 1) +  -- 真金流水EXP  
    (total_gold_consumption / 1000 * 1), -- 金币流水EXP (假设1000金币=1 INR)
    0
),
last_exp_update = CURRENT_TIMESTAMP
WHERE id > 0;

-- 7. 根据新的EXP要求重新计算用户VIP等级
UPDATE app_users 
SET vip_level = (
    SELECT COALESCE(MAX(vip_level), 0)
    FROM vip_configs 
    WHERE required_exp <= app_users.total_exp 
    AND status = 1
)
WHERE id > 0;

-- 8. 创建EXP变更记录表（用于审计）
CREATE TABLE IF NOT EXISTS vip_exp_migration_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    old_vip_level INTEGER,
    new_vip_level INTEGER,
    old_vip_points DECIMAL(15,4),
    new_total_exp BIGINT,
    migration_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    migration_reason VARCHAR(255) DEFAULT 'V12.0 System Migration'
);

-- 9. 记录迁移日志
INSERT INTO vip_exp_migration_log (
    user_id, old_vip_level, new_vip_level, old_vip_points, new_total_exp
)
SELECT 
    u.id,
    b.vip_level as old_vip_level,
    u.vip_level as new_vip_level,
    b.vip_points as old_vip_points,
    u.total_exp as new_total_exp
FROM app_users u
LEFT JOIN user_vip_backup b ON u.id = b.id
WHERE u.id > 0;

-- 10. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_app_users_total_exp ON app_users(total_exp);
CREATE INDEX IF NOT EXISTS idx_app_users_vip_level_exp ON app_users(vip_level, total_exp);
CREATE INDEX IF NOT EXISTS idx_vip_configs_required_exp ON vip_configs(required_exp);
CREATE INDEX IF NOT EXISTS idx_vip_configs_level_status ON vip_configs(vip_level, status);

-- 11. 更新统计信息
ANALYZE app_users;
ANALYZE vip_configs;

-- 12. 验证迁移结果
SELECT 
    'Migration Summary' as report_type,
    COUNT(*) as total_users,
    COUNT(CASE WHEN total_exp > 0 THEN 1 END) as users_with_exp,
    AVG(total_exp) as avg_exp,
    MAX(total_exp) as max_exp,
    COUNT(DISTINCT vip_level) as vip_levels_in_use
FROM app_users;

-- 显示各VIP等级用户分布
SELECT 
    v.vip_level,
    v.level_name,
    v.required_exp,
    COUNT(u.id) as user_count,
    ROUND(COUNT(u.id) * 100.0 / (SELECT COUNT(*) FROM app_users), 2) as percentage
FROM vip_configs v
LEFT JOIN app_users u ON u.vip_level = v.vip_level
WHERE v.status = 1
GROUP BY v.vip_level, v.level_name, v.required_exp
ORDER BY v.vip_level;

-- 显示迁移前后对比
SELECT 
    'Before Migration' as period,
    old_vip_level as vip_level,
    COUNT(*) as user_count
FROM vip_exp_migration_log
GROUP BY old_vip_level
UNION ALL
SELECT 
    'After Migration' as period,
    new_vip_level as vip_level,
    COUNT(*) as user_count
FROM vip_exp_migration_log
GROUP BY new_vip_level
ORDER BY period, vip_level;

COMMIT;
