import{u as i,j as t}from"./index-DD0gcXtR.js";import{E as o}from"./index-ct7iarNQ.js";import{aq as s}from"./antd-FyCAPQKa.js";import"./react-BUTTOX-3.js";function l(){const{t:e}=i(),a={title:{text:"",subtext:""},xAxis:{type:"category",data:[e("home.directAccess"),e("home.emailMarketing"),e("home.affiliateAdvertise"),e("home.videoAdvertise")]},yAxis:{type:"value"},tooltip:{},series:[{type:"bar",data:[{value:335,name:e("home.directAccess")},{value:310,name:e("home.emailMarketing")},{value:234,name:e("home.affiliateAdvertise")},{value:135,name:e("home.videoAdvertise")}]}]};return t.jsx(s,{title:e("home.views"),children:t.jsx(o,{opts:{height:"auto",width:"auto"},option:a})})}export{l as default};
