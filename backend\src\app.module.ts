import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RouterModule } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
// System entities
import { SysUser, SysRole, SysPermission, SysMenu, SysWorkbook } from './system/entities';
// App entities
import {
  AppUser, AppCategory, AppProduct, AppOrder, AppOrderItem, AppUserAddress, AppShoppingCart, AppUserFavorite,
  ApplicationProvider, ProviderEnvironment, Application,
  MarketingChannel, MarketingAd, PromotionalPage,
  RiskEvent, DeviceLogRealtime, DeviceLogHistory,
  CashTransaction, GoldTransaction, RechargeTransaction
} from './app/entities';
import { VipConfigV2 } from './app/config/entities/vip-config-v2.entity';
import { MembershipCardConfig } from './app/config/entities/membership-card-config.entity';
import { GoldRechargeConfig } from './app/config/entities/gold-recharge-config.entity';
import { BalanceRechargeConfig } from './app/config/entities/balance-recharge-config.entity';
import { BalanceRechargeLimit } from './app/config/entities/balance-recharge-limit.entity';
import { AdConfig } from './app/config/entities/ad-config.entity';
import {
  AppHomeConfig,
  AppHomeRecommendedGame,
  AppHomeGameCategory,
  AppHomeCategoryGame
} from './app/config/entities';
// Modules
import { SystemModule } from './system/system.module';
import { AppBusinessModule } from './app/app.module';
import { ApiModule } from './api/api.module';
// Health check and warmup
import { DatabaseHealthService } from './common/database-health.service';
import { DatabaseHealthController } from './common/database-health.controller';
import { DatabaseWarmupService } from './common/database-warmup.service';
import { DatabaseMonitorService } from './common/database-monitor.service';


@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        return {
          type: 'postgres',
          host: configService.get('DB_HOST'),
          port: +configService.get('DB_PORT'),
          username: configService.get('DB_USERNAME'),
          password: configService.get('DB_PASSWORD'),
          database: configService.get('DB_DATABASE'),
          entities: [
          // System entities
          SysUser, SysRole, SysPermission, SysMenu, SysWorkbook,
          // App entities
          AppUser, AppCategory, AppProduct, AppOrder, AppOrderItem, AppUserAddress, AppShoppingCart, AppUserFavorite,
          ApplicationProvider, ProviderEnvironment, Application,
          // Marketing entities
          MarketingChannel, MarketingAd, PromotionalPage,
          // Risk control entities
          RiskEvent, DeviceLogRealtime, DeviceLogHistory,
          // Transaction entities
          CashTransaction, GoldTransaction, RechargeTransaction,
          // Config entities
          VipConfigV2,
          MembershipCardConfig,
          GoldRechargeConfig,
          BalanceRechargeConfig,
          BalanceRechargeLimit,
          AdConfig,
          // App Home Config entities
          AppHomeConfig,
          AppHomeRecommendedGame,
          AppHomeGameCategory,
          AppHomeCategoryGame,
          ],
          synchronize: false, // 禁用自动同步，使用手动创建的表
          logging: ['error'], // 只显示错误日志以提高性能
          logger: 'advanced-console', // 使用高级控制台日志记录器
          // SSL配置 - 根据环境动态设置
          ssl: configService.get('ENVIRONMENT') === 'supabase' ? {
            rejectUnauthorized: false, // Supabase需要SSL连接
          } : false, // 本地环境不需要SSL
          // 连接池配置优化 - 针对Supabase远程连接
          poolSize: +configService.get('DB_POOL_SIZE', 15), // 增加连接池大小
          extra: {
          // 连接超时配置
          connectionTimeoutMillis: +configService.get('DB_CONNECTION_TIMEOUT', 30000),
          acquireTimeoutMillis: +configService.get('DB_ACQUIRE_TIMEOUT', 30000),
          createTimeoutMillis: +configService.get('DB_CREATE_TIMEOUT', 30000),

          // 查询超时配置 - 增加到2分钟
          query_timeout: +configService.get('DB_QUERY_TIMEOUT', 120000),
          statement_timeout: +configService.get('DB_QUERY_TIMEOUT', 120000),

          // 连接池配置
          max: +configService.get('DB_MAX_CONNECTIONS', 20),
          min: +configService.get('DB_MIN_CONNECTIONS', 5),
          idleTimeoutMillis: +configService.get('DB_IDLE_TIMEOUT', 300000),

          // 连接保活配置
          keepAlive: configService.get('DB_KEEP_ALIVE', 'true') === 'true',
          keepAliveInitialDelayMillis: +configService.get('DB_KEEP_ALIVE_DELAY', 30000),

          // PostgreSQL特定优化
          application_name: 'inapp2-backend',
          tcp_keepalives_idle: 600,
          tcp_keepalives_interval: 30,
          tcp_keepalives_count: 3,

          // 性能优化
          parseInputDatesAsUTC: true,
          allowExitOnIdle: false,
          },
          // 重试配置 - 针对网络不稳定优化
          retryAttempts: +configService.get('DB_RETRY_ATTEMPTS', 3),
          retryDelay: +configService.get('DB_RETRY_DELAY', 2000),
        };
      },
      inject: [ConfigService],
    }),
    SystemModule,
    AppBusinessModule,
    ApiModule,
    RouterModule.register([
      {
        path: 'system',
        module: SystemModule,
      },
      {
        path: 'app',
        module: AppBusinessModule,
      },
      {
        path: 'api',
        module: ApiModule,
      },
    ]),
  ],
  controllers: [AppController, DatabaseHealthController],
  providers: [AppService, DatabaseHealthService, DatabaseWarmupService, DatabaseMonitorService],
})
export class AppModule {}
