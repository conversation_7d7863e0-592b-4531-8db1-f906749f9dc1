import{r as e}from"./index-DD0gcXtR.js";const a=[{label:"启用",value:1},{label:"禁用",value:0}],o=[{label:"媒体买量",value:1},{label:"推荐系统引流",value:2}],r=n=>n===1?"启用":"禁用",u=n=>n===1?"success":"default",l=n=>n===1?"媒体买量":"推荐系统引流",c=n=>n===1?"blue":"green";function h(n){return e.post("channels",{json:n}).json()}function g(n){return e.get("channels",{searchParams:n}).json()}function d(){return e.get("channels/simple").json()}function j(n,s){return e.patch(`channels/${n}`,{json:s}).json()}function p(n){return e.patch(`channels/${n}/toggle-status`).json()}function i(n){return e.post("channels/ads",{json:n}).json()}function f(n){return e.get("channels/ads",{searchParams:n}).json()}function A(n,s){return e.patch(`channels/ads/${n}`,{json:s}).json()}function C(n){return e.patch(`channels/ads/${n}/toggle-status`).json()}export{o as A,a as C,f as a,r as b,u as c,l as d,c as e,i as f,d as g,g as h,p as i,h as j,j as k,C as t,A as u};
