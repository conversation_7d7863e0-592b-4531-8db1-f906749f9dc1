import { Module } from '@nestjs/common';
import { VipConfigModule } from './vip-config.module';
import { VipConfigV2Module } from './vip-config-v2.module';
import { MembershipCardConfigModule } from './membership-card-config.module';
import { RechargeConfigModule } from './recharge-config.module';
import { AdConfigModule } from './ad-config.module';
import { AppHomeConfigModule } from './app-home-config.module';

@Module({
  imports: [
    VipConfigModule, // 保留旧版本兼容
    VipConfigV2Module, // 新版本VIP经济权益系统
    MembershipCardConfigModule,
    RechargeConfigModule,
    AdConfigModule,
    AppHomeConfigModule,
  ],
  exports: [
    VipConfigModule,
    VipConfigV2Module,
    MembershipCardConfigModule,
    RechargeConfigModule,
    AdConfigModule,
    AppHomeConfigModule,
  ],
})
export class ConfigModule {}
