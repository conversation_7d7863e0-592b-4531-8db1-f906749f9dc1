import { Module } from '@nestjs/common';
import { VipConfigV2Module } from './vip-config-v2.module';
import { MembershipCardConfigModule } from './membership-card-config.module';
import { RechargeConfigModule } from './recharge-config.module';
import { AdConfigModule } from './ad-config.module';
import { AppHomeConfigModule } from './app-home-config.module';

@Module({
  imports: [
    VipConfigV2Module, // VIP经济权益系统 V12.0
    MembershipCardConfigModule,
    RechargeConfigModule,
    AdConfigModule,
    AppHomeConfigModule,
  ],
  exports: [
    VipConfigV2Module,
    MembershipCardConfigModule,
    RechargeConfigModule,
    AdConfigModule,
    AppHomeConfigModule,
  ],
})
export class ConfigModule {}
