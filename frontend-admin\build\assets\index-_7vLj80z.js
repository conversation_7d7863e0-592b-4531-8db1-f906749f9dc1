import{u as w,j as o,H as f,a6 as M}from"./index-DD0gcXtR.js";import{b as k,c as R}from"./index-DkOkoykF.js";import{u as S}from"./index-DtsmHvPi.js";import{a}from"./react-BUTTOX-3.js";import{B as g}from"./index-C1Do55qr.js";import{B as I}from"./index-DUsnaVFw.js";import{Detail as P}from"./detail-D0A9dDYA.js";import{getConstantColumns as $}from"./constants-DYLSMTVr.js";import{aD as z,d as A,bH as E}from"./antd-FyCAPQKa.js";import{a as i}from"./constants-BR8N8nwY.js";import"./Table-Blekjt7O.js";import"./index-DwiaqWIr.js";import"./index-BDysGMcU.js";import"./BaseForm-CUW86psc.js";import"./index-DhoWj0AL.js";import"./index-BhoZk0D4.js";import"./index-B5ghf3TI.js";import"./index-DUwEwhNf.js";import"./index-NKI1j_n6.js";function te(){const{t:e}=w(),{hasAccessByCodes:m}=S(),[h,l]=a.useState(!1),[x,c]=a.useState(""),[y,u]=a.useState({}),[C,T]=a.useState([]),d=a.useRef(null),j=async(n,t)=>{var s,p;const r=await R(n);await((s=t==null?void 0:t.reload)==null?void 0:s.call(t)),(p=window.$message)==null||p.success(`${e("common.deleteSuccess")} id = ${r.result}`)},D=[...$(e),{title:e("common.action"),valueType:"option",key:"option",width:120,fixed:"right",render:(n,t,r,s)=>[o.jsx(f,{type:"link",size:"small",disabled:!m(i.update),onClick:async()=>{l(!0),c(e("system.menu.editMenu")),u({...t})},children:e("common.edit")},"editable"),o.jsx(z,{title:e("common.confirmDelete"),onConfirm:()=>j(t.id,s),okText:e("common.confirm"),cancelText:e("common.cancel"),children:o.jsx(f,{type:"link",size:"small",disabled:!m(i.delete),children:e("common.delete")})},"delete")]}],b=()=>{l(!1),u({})},B=()=>{var n;(n=d.current)==null||n.reload()};return o.jsxs(g,{className:"h-full",children:[o.jsx(I,{columns:D,actionRef:d,request:async n=>{const t=await k(n),r=M(t.result.list);return T(t.result.list.filter(s=>Number(s.menuType)===0).map(s=>({...s,name:e(s.name)}))),{...t,data:r,total:t.result.total}},headerTitle:e("common.menu.menu"),toolBarRender:()=>[o.jsx(A,{icon:o.jsx(E,{}),type:"primary",disabled:!m(i.add),onClick:()=>{l(!0),c(e("system.menu.addMenu"))},children:e("common.add")},"add-role")]}),o.jsx(P,{title:x,open:h,flatParentMenus:C,onCloseChange:b,detailData:y,refreshTable:B})]})}export{te as default};
