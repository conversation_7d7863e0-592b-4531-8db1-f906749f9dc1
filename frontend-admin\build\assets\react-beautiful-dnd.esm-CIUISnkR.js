import{R as F,a as S}from"./react-BUTTOX-3.js";import{v as tt,w as Vt}from"./index-DD0gcXtR.js";import{aW as nt,k as ze,i as E,r as Zn,aX as _n}from"./antd-FyCAPQKa.js";function q(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var at=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),it=function(){return Math.random().toString(36).substring(7).split("").join(".")},ot={INIT:"@@redux/INIT"+it(),REPLACE:"@@redux/REPLACE"+it()};function ea(e){if(typeof e!="object"||e===null)return!1;for(var r=e;Object.getPrototypeOf(r)!==null;)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(e)===r}function $t(e,r,t){var n;if(typeof r=="function"&&typeof t=="function"||typeof t=="function"&&typeof arguments[3]=="function")throw new Error(q(0));if(typeof r=="function"&&typeof t>"u"&&(t=r,r=void 0),typeof t<"u"){if(typeof t!="function")throw new Error(q(1));return t($t)(e,r)}if(typeof e!="function")throw new Error(q(2));var i=e,a=r,o=[],l=o,u=!1;function d(){l===o&&(l=o.slice())}function p(){if(u)throw new Error(q(3));return a}function s(g){if(typeof g!="function")throw new Error(q(4));if(u)throw new Error(q(5));var b=!0;return d(),l.push(g),function(){if(b){if(u)throw new Error(q(6));b=!1,d();var h=l.indexOf(g);l.splice(h,1),o=null}}}function c(g){if(!ea(g))throw new Error(q(7));if(typeof g.type>"u")throw new Error(q(8));if(u)throw new Error(q(9));try{u=!0,a=i(a,g)}finally{u=!1}for(var b=o=l,m=0;m<b.length;m++){var h=b[m];h()}return g}function v(g){if(typeof g!="function")throw new Error(q(10));i=g,c({type:ot.REPLACE})}function f(){var g,b=s;return g={subscribe:function(h){if(typeof h!="object"||h===null)throw new Error(q(11));function x(){h.next&&h.next(p())}x();var D=b(x);return{unsubscribe:D}}},g[at]=function(){return this},g}return c({type:ot.INIT}),n={dispatch:c,subscribe:s,getState:p,replaceReducer:v},n[at]=f,n}function lt(e,r){return function(){return r(e.apply(this,arguments))}}function ut(e,r){if(typeof e=="function")return lt(e,r);if(typeof e!="object"||e===null)throw new Error(q(16));var t={};for(var n in e){var i=e[n];typeof i=="function"&&(t[n]=lt(i,r))}return t}function zt(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.length===0?function(n){return n}:r.length===1?r[0]:r.reduce(function(n,i){return function(){return n(i.apply(void 0,arguments))}})}function ra(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(n){return function(){var i=n.apply(void 0,arguments),a=function(){throw new Error(q(15))},o={getState:i.getState,dispatch:function(){return a.apply(void 0,arguments)}},l=r.map(function(u){return u(o)});return a=zt.apply(void 0,l)(i.dispatch),nt(nt({},i),{},{dispatch:a})}}}var jt=F.createContext(null);function ta(e){e()}var Yt=ta,na=function(r){return Yt=r},aa=function(){return Yt};function ia(){var e=aa(),r=null,t=null;return{clear:function(){r=null,t=null},notify:function(){e(function(){for(var i=r;i;)i.callback(),i=i.next})},get:function(){for(var i=[],a=r;a;)i.push(a),a=a.next;return i},subscribe:function(i){var a=!0,o=t={callback:i,next:null,prev:t};return o.prev?o.prev.next=o:r=o,function(){!a||r===null||(a=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:r=o.next)}}}}var st={notify:function(){},get:function(){return[]}};function Kt(e,r){var t,n=st;function i(s){return u(),n.subscribe(s)}function a(){n.notify()}function o(){p.onStateChange&&p.onStateChange()}function l(){return!!t}function u(){t||(t=r?r.addNestedSub(o):e.subscribe(o),n=ia())}function d(){t&&(t(),t=void 0,n.clear(),n=st)}var p={addNestedSub:i,notifyNestedSubs:a,handleChangeWrapper:o,isSubscribed:l,trySubscribe:u,tryUnsubscribe:d,getListeners:function(){return n}};return p}var Jt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?S.useLayoutEffect:S.useEffect;function oa(e){var r=e.store,t=e.context,n=e.children,i=S.useMemo(function(){var l=Kt(r);return{store:r,subscription:l}},[r]),a=S.useMemo(function(){return r.getState()},[r]);Jt(function(){var l=i.subscription;return l.onStateChange=l.notifyNestedSubs,l.trySubscribe(),a!==r.getState()&&l.notifyNestedSubs(),function(){l.tryUnsubscribe(),l.onStateChange=null}},[i,a]);var o=t||jt;return F.createElement(o.Provider,{value:i},n)}var vr={exports:{}},N={};/** @license React v17.0.2
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ct;function la(){if(ct)return N;ct=1;var e=60103,r=60106,t=60107,n=60108,i=60114,a=60109,o=60110,l=60112,u=60113,d=60120,p=60115,s=60116,c=60121,v=60122,f=60117,g=60129,b=60131;if(typeof Symbol=="function"&&Symbol.for){var m=Symbol.for;e=m("react.element"),r=m("react.portal"),t=m("react.fragment"),n=m("react.strict_mode"),i=m("react.profiler"),a=m("react.provider"),o=m("react.context"),l=m("react.forward_ref"),u=m("react.suspense"),d=m("react.suspense_list"),p=m("react.memo"),s=m("react.lazy"),c=m("react.block"),v=m("react.server.block"),f=m("react.fundamental"),g=m("react.debug_trace_mode"),b=m("react.legacy_hidden")}function h(I){if(typeof I=="object"&&I!==null){var L=I.$$typeof;switch(L){case e:switch(I=I.type,I){case t:case i:case n:case u:case d:return I;default:switch(I=I&&I.$$typeof,I){case o:case l:case s:case p:case a:return I;default:return L}}case r:return L}}}var x=a,D=e,P=l,w=t,B=s,T=p,A=r,M=i,O=n,k=u;return N.ContextConsumer=o,N.ContextProvider=x,N.Element=D,N.ForwardRef=P,N.Fragment=w,N.Lazy=B,N.Memo=T,N.Portal=A,N.Profiler=M,N.StrictMode=O,N.Suspense=k,N.isAsyncMode=function(){return!1},N.isConcurrentMode=function(){return!1},N.isContextConsumer=function(I){return h(I)===o},N.isContextProvider=function(I){return h(I)===a},N.isElement=function(I){return typeof I=="object"&&I!==null&&I.$$typeof===e},N.isForwardRef=function(I){return h(I)===l},N.isFragment=function(I){return h(I)===t},N.isLazy=function(I){return h(I)===s},N.isMemo=function(I){return h(I)===p},N.isPortal=function(I){return h(I)===r},N.isProfiler=function(I){return h(I)===i},N.isStrictMode=function(I){return h(I)===n},N.isSuspense=function(I){return h(I)===u},N.isValidElementType=function(I){return typeof I=="string"||typeof I=="function"||I===t||I===i||I===g||I===n||I===u||I===d||I===b||typeof I=="object"&&I!==null&&(I.$$typeof===s||I.$$typeof===p||I.$$typeof===a||I.$$typeof===o||I.$$typeof===l||I.$$typeof===f||I.$$typeof===c||I[0]===v)},N.typeOf=h,N}var dt;function ua(){return dt||(dt=1,vr.exports=la()),vr.exports}var sa=ua(),ca=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],da=["reactReduxForwardedRef"],pa=[],va=[null,null];function fa(e,r){var t=e[1];return[r.payload,t+1]}function pt(e,r,t){Jt(function(){return e.apply(void 0,r)},t)}function ga(e,r,t,n,i,a,o){e.current=n,r.current=i,t.current=!1,a.current&&(a.current=null,o())}function ma(e,r,t,n,i,a,o,l,u,d){if(e){var p=!1,s=null,c=function(){if(!p){var g=r.getState(),b,m;try{b=n(g,i.current)}catch(h){m=h,s=h}m||(s=null),b===a.current?o.current||u():(a.current=b,l.current=b,o.current=!0,d({type:"STORE_UPDATED",payload:{error:m}}))}};t.onStateChange=c,t.trySubscribe(),c();var v=function(){if(p=!0,t.tryUnsubscribe(),t.onStateChange=null,s)throw s};return v}}var ba=function(){return[null,0]};function ha(e,r){r===void 0&&(r={});var t=r,n=t.getDisplayName,i=n===void 0?function(x){return"ConnectAdvanced("+x+")"}:n,a=t.methodName,o=a===void 0?"connectAdvanced":a,l=t.renderCountProp,u=l===void 0?void 0:l,d=t.shouldHandleStateChanges,p=d===void 0?!0:d,s=t.storeKey,c=s===void 0?"store":s;t.withRef;var v=t.forwardRef,f=v===void 0?!1:v,g=t.context,b=g===void 0?jt:g,m=ze(t,ca),h=b;return function(D){var P=D.displayName||D.name||"Component",w=i(P),B=E({},m,{getDisplayName:i,methodName:o,renderCountProp:u,shouldHandleStateChanges:p,storeKey:c,displayName:w,wrappedComponentName:P,WrappedComponent:D}),T=m.pure;function A(L){return e(L.dispatch,B)}var M=T?S.useMemo:function(L){return L()};function O(L){var Y=S.useMemo(function(){var Ie=L.reactReduxForwardedRef,pr=ze(L,da);return[L.context,Ie,pr]},[L]),K=Y[0],de=Y[1],ee=Y[2],pe=S.useMemo(function(){return K&&K.Consumer&&sa.isContextConsumer(F.createElement(K.Consumer,null))?K:h},[K,h]),Z=S.useContext(pe),le=!!L.store&&!!L.store.getState&&!!L.store.dispatch;Z&&Z.store;var V=le?L.store:Z.store,xe=S.useMemo(function(){return A(V)},[V]),Le=S.useMemo(function(){if(!p)return va;var Ie=Kt(V,le?null:Z.subscription),pr=Ie.notifyNestedSubs.bind(Ie);return[Ie,pr]},[V,le,Z]),re=Le[0],Fe=Le[1],Ge=S.useMemo(function(){return le?Z:E({},Z,{subscription:re})},[le,Z,re]),We=S.useReducer(fa,pa,ba),lr=We[0],ve=lr[0],ur=We[1];if(ve&&ve.error)throw ve.error;var et=S.useRef(),sr=S.useRef(ee),Ue=S.useRef(),rt=S.useRef(!1),cr=M(function(){return Ue.current&&ee===sr.current?Ue.current:xe(V.getState(),ee)},[V,ve,ee]);pt(ga,[sr,et,rt,ee,cr,Ue,Fe]),pt(ma,[p,V,re,xe,sr,et,rt,Ue,Fe,ur],[V,re,xe]);var dr=S.useMemo(function(){return F.createElement(D,E({},cr,{ref:de}))},[de,D,cr]),Qn=S.useMemo(function(){return p?F.createElement(pe.Provider,{value:Ge},dr):dr},[pe,dr,Ge]);return Qn}var k=T?F.memo(O):O;if(k.WrappedComponent=D,k.displayName=O.displayName=w,f){var I=F.forwardRef(function(Y,K){return F.createElement(k,E({},Y,{reactReduxForwardedRef:K}))});return I.displayName=w,I.WrappedComponent=D,tt(I,D)}return tt(k,D)}}function vt(e,r){return e===r?e!==0||r!==0||1/e===1/r:e!==e&&r!==r}function fr(e,r){if(vt(e,r))return!0;if(typeof e!="object"||e===null||typeof r!="object"||r===null)return!1;var t=Object.keys(e),n=Object.keys(r);if(t.length!==n.length)return!1;for(var i=0;i<t.length;i++)if(!Object.prototype.hasOwnProperty.call(r,t[i])||!vt(e[t[i]],r[t[i]]))return!1;return!0}function ya(e,r){var t={},n=function(o){var l=e[o];typeof l=="function"&&(t[o]=function(){return r(l.apply(void 0,arguments))})};for(var i in e)n(i);return t}function Or(e){return function(t,n){var i=e(t,n);function a(){return i}return a.dependsOnOwnProps=!1,a}}function ft(e){return e.dependsOnOwnProps!==null&&e.dependsOnOwnProps!==void 0?!!e.dependsOnOwnProps:e.length!==1}function Xt(e,r){return function(n,i){i.displayName;var a=function(l,u){return a.dependsOnOwnProps?a.mapToProps(l,u):a.mapToProps(l)};return a.dependsOnOwnProps=!0,a.mapToProps=function(l,u){a.mapToProps=e,a.dependsOnOwnProps=ft(e);var d=a(l,u);return typeof d=="function"&&(a.mapToProps=d,a.dependsOnOwnProps=ft(d),d=a(l,u)),d},a}}function Da(e){return typeof e=="function"?Xt(e):void 0}function xa(e){return e?void 0:Or(function(r){return{dispatch:r}})}function Ia(e){return e&&typeof e=="object"?Or(function(r){return ya(e,r)}):void 0}const Sa=[Da,xa,Ia];function Ca(e){return typeof e=="function"?Xt(e):void 0}function wa(e){return e?void 0:Or(function(){return{}})}const Ea=[Ca,wa];function Pa(e,r,t){return E({},t,e,r)}function Aa(e){return function(t,n){n.displayName;var i=n.pure,a=n.areMergedPropsEqual,o=!1,l;return function(d,p,s){var c=e(d,p,s);return o?(!i||!a(c,l))&&(l=c):(o=!0,l=c),l}}}function Ra(e){return typeof e=="function"?Aa(e):void 0}function Ba(e){return e?void 0:function(){return Pa}}const Oa=[Ra,Ba];var Na=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function Ta(e,r,t,n){return function(a,o){return t(e(a,o),r(n,o),o)}}function Ma(e,r,t,n,i){var a=i.areStatesEqual,o=i.areOwnPropsEqual,l=i.areStatePropsEqual,u=!1,d,p,s,c,v;function f(x,D){return d=x,p=D,s=e(d,p),c=r(n,p),v=t(s,c,p),u=!0,v}function g(){return s=e(d,p),r.dependsOnOwnProps&&(c=r(n,p)),v=t(s,c,p),v}function b(){return e.dependsOnOwnProps&&(s=e(d,p)),r.dependsOnOwnProps&&(c=r(n,p)),v=t(s,c,p),v}function m(){var x=e(d,p),D=!l(x,s);return s=x,D&&(v=t(s,c,p)),v}function h(x,D){var P=!o(D,p),w=!a(x,d,D,p);return d=x,p=D,P&&w?g():P?b():w?m():v}return function(D,P){return u?h(D,P):f(D,P)}}function La(e,r){var t=r.initMapStateToProps,n=r.initMapDispatchToProps,i=r.initMergeProps,a=ze(r,Na),o=t(e,a),l=n(e,a),u=i(e,a),d=a.pure?Ma:Ta;return d(o,l,u,e,a)}var Fa=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function gr(e,r,t){for(var n=r.length-1;n>=0;n--){var i=r[n](e);if(i)return i}return function(a,o){throw new Error("Invalid value of type "+typeof e+" for "+t+" argument when connecting component "+o.wrappedComponentName+".")}}function Ga(e,r){return e===r}function Wa(e){var r=e===void 0?{}:e,t=r.connectHOC,n=t===void 0?ha:t,i=r.mapStateToPropsFactories,a=i===void 0?Ea:i,o=r.mapDispatchToPropsFactories,l=o===void 0?Sa:o,u=r.mergePropsFactories,d=u===void 0?Oa:u,p=r.selectorFactory,s=p===void 0?La:p;return function(v,f,g,b){b===void 0&&(b={});var m=b,h=m.pure,x=h===void 0?!0:h,D=m.areStatesEqual,P=D===void 0?Ga:D,w=m.areOwnPropsEqual,B=w===void 0?fr:w,T=m.areStatePropsEqual,A=T===void 0?fr:T,M=m.areMergedPropsEqual,O=M===void 0?fr:M,k=ze(m,Fa),I=gr(v,a,"mapStateToProps"),L=gr(f,l,"mapDispatchToProps"),Y=gr(g,d,"mergeProps");return n(s,E({methodName:"connect",getDisplayName:function(de){return"Connect("+de+")"},shouldHandleStateChanges:!!v,initMapStateToProps:I,initMapDispatchToProps:L,initMergeProps:Y,pure:x,areStatesEqual:P,areOwnPropsEqual:B,areStatePropsEqual:A,areMergedPropsEqual:O},k))}}const Qt=Wa();na(Zn.unstable_batchedUpdates);function Ua(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(e[t]!==r[t])return!1;return!0}function Zt(e,r){var t=S.useState(function(){return{inputs:r,result:e()}})[0],n=S.useRef(!0),i=S.useRef(t),a=n.current||!!(r&&i.current.inputs&&Ua(r,i.current.inputs)),o=a?i.current:{inputs:r,result:e()};return S.useEffect(function(){n.current=!1,i.current=o},[o]),o.result}function Ha(e,r){return Zt(function(){return e},r)}var R=Zt,C=Ha,ka="Invariant failed";function qa(e,r){throw new Error(ka)}var _=function(r){var t=r.top,n=r.right,i=r.bottom,a=r.left,o=n-a,l=i-t,u={top:t,right:n,bottom:i,left:a,width:o,height:l,x:a,y:t,center:{x:(n+a)/2,y:(i+t)/2}};return u},Nr=function(r,t){return{top:r.top-t.top,left:r.left-t.left,bottom:r.bottom+t.bottom,right:r.right+t.right}},gt=function(r,t){return{top:r.top+t.top,left:r.left+t.left,bottom:r.bottom-t.bottom,right:r.right-t.right}},Va=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},mr={top:0,right:0,bottom:0,left:0},Tr=function(r){var t=r.borderBox,n=r.margin,i=n===void 0?mr:n,a=r.border,o=a===void 0?mr:a,l=r.padding,u=l===void 0?mr:l,d=_(Nr(t,i)),p=_(gt(t,o)),s=_(gt(p,u));return{marginBox:d,borderBox:_(t),paddingBox:p,contentBox:s,margin:i,border:o,padding:u}},J=function(r){var t=r.slice(0,-2),n=r.slice(-2);if(n!=="px")return 0;var i=Number(t);return isNaN(i)&&qa(),i},$a=function(){return{x:window.pageXOffset,y:window.pageYOffset}},je=function(r,t){var n=r.borderBox,i=r.border,a=r.margin,o=r.padding,l=Va(n,t);return Tr({borderBox:l,border:i,margin:a,padding:o})},Ye=function(r,t){return t===void 0&&(t=$a()),je(r,t)},_t=function(r,t){var n={top:J(t.marginTop),right:J(t.marginRight),bottom:J(t.marginBottom),left:J(t.marginLeft)},i={top:J(t.paddingTop),right:J(t.paddingRight),bottom:J(t.paddingBottom),left:J(t.paddingLeft)},a={top:J(t.borderTopWidth),right:J(t.borderRightWidth),bottom:J(t.borderBottomWidth),left:J(t.borderLeftWidth)};return Tr({borderBox:r,margin:n,padding:i,border:a})},en=function(r){var t=r.getBoundingClientRect(),n=window.getComputedStyle(r);return _t(t,n)},mt=Number.isNaN||function(r){return typeof r=="number"&&r!==r};function za(e,r){return!!(e===r||mt(e)&&mt(r))}function ja(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(!za(e[t],r[t]))return!1;return!0}function G(e,r){r===void 0&&(r=ja);var t,n=[],i,a=!1;function o(){for(var l=[],u=0;u<arguments.length;u++)l[u]=arguments[u];return a&&t===this&&r(l,n)||(i=e.apply(this,l),a=!0,t=this,n=l),i}return o}var Ae=function(r){var t=[],n=null,i=function(){for(var o=arguments.length,l=new Array(o),u=0;u<o;u++)l[u]=arguments[u];t=l,!n&&(n=requestAnimationFrame(function(){n=null,r.apply(void 0,t)}))};return i.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},i};function rn(e,r){}rn.bind(null,"warn");rn.bind(null,"error");function te(){}function Ya(e,r){return E({},e,{},r)}function X(e,r,t){var n=r.map(function(i){var a=Ya(t,i.options);return e.addEventListener(i.eventName,i.fn,a),function(){e.removeEventListener(i.eventName,i.fn,a)}});return function(){n.forEach(function(a){a()})}}var Ka="Invariant failed";function Ke(e){this.message=e}Ke.prototype.toString=function(){return this.message};function y(e,r){throw new Ke(Ka)}var Ja=function(e){Vt(r,e);function r(){for(var n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return n=e.call.apply(e,[this].concat(a))||this,n.callbacks=null,n.unbind=te,n.onWindowError=function(l){var u=n.getCallbacks();u.isDragging()&&u.tryAbort();var d=l.error;d instanceof Ke&&l.preventDefault()},n.getCallbacks=function(){if(!n.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return n.callbacks},n.setCallbacks=function(l){n.callbacks=l},n}var t=r.prototype;return t.componentDidMount=function(){this.unbind=X(window,[{eventName:"error",fn:this.onWindowError}])},t.componentDidCatch=function(i){if(i instanceof Ke){this.setState({});return}throw i},t.componentWillUnmount=function(){this.unbind()},t.render=function(){return this.props.children(this.setCallbacks)},r}(F.Component),Xa=`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,Je=function(r){return r+1},Qa=function(r){return`
  You have lifted an item in position `+Je(r.source.index)+`
`},tn=function(r,t){var n=r.droppableId===t.droppableId,i=Je(r.index),a=Je(t.index);return n?`
      You have moved the item from position `+i+`
      to position `+a+`
    `:`
    You have moved the item from position `+i+`
    in list `+r.droppableId+`
    to list `+t.droppableId+`
    in position `+a+`
  `},nn=function(r,t,n){var i=t.droppableId===n.droppableId;return i?`
      The item `+r+`
      has been combined with `+n.draggableId:`
      The item `+r+`
      in list `+t.droppableId+`
      has been combined with `+n.draggableId+`
      in list `+n.droppableId+`
    `},Za=function(r){var t=r.destination;if(t)return tn(r.source,t);var n=r.combine;return n?nn(r.draggableId,r.source,n):"You are over an area that cannot be dropped on"},bt=function(r){return`
  The item has returned to its starting position
  of `+Je(r.index)+`
`},_a=function(r){if(r.reason==="CANCEL")return`
      Movement cancelled.
      `+bt(r.source)+`
    `;var t=r.destination,n=r.combine;return t?`
      You have dropped the item.
      `+tn(r.source,t)+`
    `:n?`
      You have dropped the item.
      `+nn(r.draggableId,r.source,n)+`
    `:`
    The item has been dropped while not over a drop area.
    `+bt(r.source)+`
  `},$e={dragHandleUsageInstructions:Xa,onDragStart:Qa,onDragUpdate:Za,onDragEnd:_a},W={x:0,y:0},U=function(r,t){return{x:r.x+t.x,y:r.y+t.y}},$=function(r,t){return{x:r.x-t.x,y:r.y-t.y}},ne=function(r,t){return r.x===t.x&&r.y===t.y},he=function(r){return{x:r.x!==0?-r.x:0,y:r.y!==0?-r.y:0}},ce=function(r,t,n){var i;return n===void 0&&(n=0),i={},i[r]=t,i[r==="x"?"y":"x"]=n,i},Re=function(r,t){return Math.sqrt(Math.pow(t.x-r.x,2)+Math.pow(t.y-r.y,2))},ht=function(r,t){return Math.min.apply(Math,t.map(function(n){return Re(r,n)}))},an=function(r){return function(t){return{x:r(t.x),y:r(t.y)}}},ei=function(e,r){var t=_({top:Math.max(r.top,e.top),right:Math.min(r.right,e.right),bottom:Math.min(r.bottom,e.bottom),left:Math.max(r.left,e.left)});return t.width<=0||t.height<=0?null:t},Te=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},yt=function(r){return[{x:r.left,y:r.top},{x:r.right,y:r.top},{x:r.left,y:r.bottom},{x:r.right,y:r.bottom}]},ri={top:0,right:0,bottom:0,left:0},ti=function(r,t){return t?Te(r,t.scroll.diff.displacement):r},ni=function(r,t,n){if(n&&n.increasedBy){var i;return E({},r,(i={},i[t.end]=r[t.end]+n.increasedBy[t.line],i))}return r},ai=function(r,t){return t&&t.shouldClipSubject?ei(t.pageMarginBox,r):_(r)},ge=function(e){var r=e.page,t=e.withPlaceholder,n=e.axis,i=e.frame,a=ti(r.marginBox,i),o=ni(a,n,t),l=ai(o,i);return{page:r,withPlaceholder:t,active:l}},Mr=function(e,r){e.frame||y();var t=e.frame,n=$(r,t.scroll.initial),i=he(n),a=E({},t,{scroll:{initial:t.scroll.initial,current:r,diff:{value:n,displacement:i},max:t.scroll.max}}),o=ge({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:a}),l=E({},e,{frame:a,subject:o});return l};function Xe(e){return Object.values?Object.values(e):Object.keys(e).map(function(r){return e[r]})}function Lr(e,r){if(e.findIndex)return e.findIndex(r);for(var t=0;t<e.length;t++)if(r(e[t]))return t;return-1}function oe(e,r){if(e.find)return e.find(r);var t=Lr(e,r);if(t!==-1)return e[t]}function on(e){return Array.prototype.slice.call(e)}var ln=G(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),un=G(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),er=G(function(e){return Xe(e)}),ii=G(function(e){return Xe(e)}),ye=G(function(e,r){var t=ii(r).filter(function(n){return e===n.descriptor.droppableId}).sort(function(n,i){return n.descriptor.index-i.descriptor.index});return t});function Fr(e){return e.at&&e.at.type==="REORDER"?e.at.destination:null}function rr(e){return e.at&&e.at.type==="COMBINE"?e.at.combine:null}var tr=G(function(e,r){return r.filter(function(t){return t.descriptor.id!==e.descriptor.id})}),oi=function(e){var r=e.isMovingForward,t=e.draggable,n=e.destination,i=e.insideDestination,a=e.previousImpact;if(!n.isCombineEnabled)return null;var o=Fr(a);if(!o)return null;function l(g){var b={type:"COMBINE",combine:{draggableId:g,droppableId:n.descriptor.id}};return E({},a,{at:b})}var u=a.displaced.all,d=u.length?u[0]:null;if(r)return d?l(d):null;var p=tr(t,i);if(!d){if(!p.length)return null;var s=p[p.length-1];return l(s.descriptor.id)}var c=Lr(p,function(g){return g.descriptor.id===d});c===-1&&y();var v=c-1;if(v<0)return null;var f=p[v];return l(f.descriptor.id)},De=function(e,r){return e.descriptor.droppableId===r.descriptor.id},sn={point:W,value:0},Be={invisible:{},visible:{},all:[]},li={displaced:Be,displacedBy:sn,at:null},Q=function(e,r){return function(t){return e<=t&&t<=r}},cn=function(e){var r=Q(e.top,e.bottom),t=Q(e.left,e.right);return function(n){var i=r(n.top)&&r(n.bottom)&&t(n.left)&&t(n.right);if(i)return!0;var a=r(n.top)||r(n.bottom),o=t(n.left)||t(n.right),l=a&&o;if(l)return!0;var u=n.top<e.top&&n.bottom>e.bottom,d=n.left<e.left&&n.right>e.right,p=u&&d;if(p)return!0;var s=u&&o||d&&a;return s}},ui=function(e){var r=Q(e.top,e.bottom),t=Q(e.left,e.right);return function(n){var i=r(n.top)&&r(n.bottom)&&t(n.left)&&t(n.right);return i}},Gr={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},dn={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},si=function(e){return function(r){var t=Q(r.top,r.bottom),n=Q(r.left,r.right);return function(i){return e===Gr?t(i.top)&&t(i.bottom):n(i.left)&&n(i.right)}}},ci=function(r,t){var n=t.frame?t.frame.scroll.diff.displacement:W;return Te(r,n)},di=function(r,t,n){return t.subject.active?n(t.subject.active)(r):!1},pi=function(r,t,n){return n(t)(r)},Wr=function(r){var t=r.target,n=r.destination,i=r.viewport,a=r.withDroppableDisplacement,o=r.isVisibleThroughFrameFn,l=a?ci(t,n):t;return di(l,n,o)&&pi(l,i,o)},vi=function(r){return Wr(E({},r,{isVisibleThroughFrameFn:cn}))},pn=function(r){return Wr(E({},r,{isVisibleThroughFrameFn:ui}))},fi=function(r){return Wr(E({},r,{isVisibleThroughFrameFn:si(r.destination.axis)}))},gi=function(r,t,n){if(typeof n=="boolean")return n;if(!t)return!0;var i=t.invisible,a=t.visible;if(i[r])return!1;var o=a[r];return o?o.shouldAnimate:!0};function mi(e,r){var t=e.page.marginBox,n={top:r.point.y,right:0,bottom:0,left:r.point.x};return _(Nr(t,n))}function Oe(e){var r=e.afterDragging,t=e.destination,n=e.displacedBy,i=e.viewport,a=e.forceShouldAnimate,o=e.last;return r.reduce(function(u,d){var p=mi(d,n),s=d.descriptor.id;u.all.push(s);var c=vi({target:p,destination:t,viewport:i,withDroppableDisplacement:!0});if(!c)return u.invisible[d.descriptor.id]=!0,u;var v=gi(s,o,a),f={draggableId:s,shouldAnimate:v};return u.visible[s]=f,u},{all:[],visible:{},invisible:{}})}function bi(e,r){if(!e.length)return 0;var t=e[e.length-1].descriptor.index;return r.inHomeList?t:t+1}function Dt(e){var r=e.insideDestination,t=e.inHomeList,n=e.displacedBy,i=e.destination,a=bi(r,{inHomeList:t});return{displaced:Be,displacedBy:n,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:a}}}}function Qe(e){var r=e.draggable,t=e.insideDestination,n=e.destination,i=e.viewport,a=e.displacedBy,o=e.last,l=e.index,u=e.forceShouldAnimate,d=De(r,n);if(l==null)return Dt({insideDestination:t,inHomeList:d,displacedBy:a,destination:n});var p=oe(t,function(g){return g.descriptor.index===l});if(!p)return Dt({insideDestination:t,inHomeList:d,displacedBy:a,destination:n});var s=tr(r,t),c=t.indexOf(p),v=s.slice(c),f=Oe({afterDragging:v,destination:n,displacedBy:a,last:o,viewport:i.frame,forceShouldAnimate:u});return{displaced:f,displacedBy:a,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function ie(e,r){return!!r.effected[e]}var hi=function(e){var r=e.isMovingForward,t=e.destination,n=e.draggables,i=e.combine,a=e.afterCritical;if(!t.isCombineEnabled)return null;var o=i.draggableId,l=n[o],u=l.descriptor.index,d=ie(o,a);return d?r?u:u-1:r?u+1:u},yi=function(e){var r=e.isMovingForward,t=e.isInHomeList,n=e.insideDestination,i=e.location;if(!n.length)return null;var a=i.index,o=r?a+1:a-1,l=n[0].descriptor.index,u=n[n.length-1].descriptor.index,d=t?u:u+1;return o<l||o>d?null:o},Di=function(e){var r=e.isMovingForward,t=e.isInHomeList,n=e.draggable,i=e.draggables,a=e.destination,o=e.insideDestination,l=e.previousImpact,u=e.viewport,d=e.afterCritical,p=l.at;if(p||y(),p.type==="REORDER"){var s=yi({isMovingForward:r,isInHomeList:t,location:p.destination,insideDestination:o});return s==null?null:Qe({draggable:n,insideDestination:o,destination:a,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:s})}var c=hi({isMovingForward:r,destination:a,displaced:l.displaced,draggables:i,combine:p.combine,afterCritical:d});return c==null?null:Qe({draggable:n,insideDestination:o,destination:a,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:c})},xi=function(e){var r=e.displaced,t=e.afterCritical,n=e.combineWith,i=e.displacedBy,a=!!(r.visible[n]||r.invisible[n]);return ie(n,t)?a?W:he(i.point):a?i.point:W},Ii=function(e){var r=e.afterCritical,t=e.impact,n=e.draggables,i=rr(t);i||y();var a=i.draggableId,o=n[a].page.borderBox.center,l=xi({displaced:t.displaced,afterCritical:r,combineWith:a,displacedBy:t.displacedBy});return U(o,l)},vn=function(r,t){return t.margin[r.start]+t.borderBox[r.size]/2},Si=function(r,t){return t.margin[r.end]+t.borderBox[r.size]/2},Ur=function(r,t,n){return t[r.crossAxisStart]+n.margin[r.crossAxisStart]+n.borderBox[r.crossAxisSize]/2},xt=function(r){var t=r.axis,n=r.moveRelativeTo,i=r.isMoving;return ce(t.line,n.marginBox[t.end]+vn(t,i),Ur(t,n.marginBox,i))},It=function(r){var t=r.axis,n=r.moveRelativeTo,i=r.isMoving;return ce(t.line,n.marginBox[t.start]-Si(t,i),Ur(t,n.marginBox,i))},Ci=function(r){var t=r.axis,n=r.moveInto,i=r.isMoving;return ce(t.line,n.contentBox[t.start]+vn(t,i),Ur(t,n.contentBox,i))},wi=function(e){var r=e.impact,t=e.draggable,n=e.draggables,i=e.droppable,a=e.afterCritical,o=ye(i.descriptor.id,n),l=t.page,u=i.axis;if(!o.length)return Ci({axis:u,moveInto:i.page,isMoving:l});var d=r.displaced,p=r.displacedBy,s=d.all[0];if(s){var c=n[s];if(ie(s,a))return It({axis:u,moveRelativeTo:c.page,isMoving:l});var v=je(c.page,p.point);return It({axis:u,moveRelativeTo:v,isMoving:l})}var f=o[o.length-1];if(f.descriptor.id===t.descriptor.id)return l.borderBox.center;if(ie(f.descriptor.id,a)){var g=je(f.page,he(a.displacedBy.point));return xt({axis:u,moveRelativeTo:g,isMoving:l})}return xt({axis:u,moveRelativeTo:f.page,isMoving:l})},wr=function(e,r){var t=e.frame;return t?U(r,t.scroll.diff.displacement):r},Ei=function(r){var t=r.impact,n=r.draggable,i=r.droppable,a=r.draggables,o=r.afterCritical,l=n.page.borderBox.center,u=t.at;return!i||!u?l:u.type==="REORDER"?wi({impact:t,draggable:n,draggables:a,droppable:i,afterCritical:o}):Ii({impact:t,draggables:a,afterCritical:o})},nr=function(e){var r=Ei(e),t=e.droppable,n=t?wr(t,r):r;return n},fn=function(e,r){var t=$(r,e.scroll.initial),n=he(t),i=_({top:r.y,bottom:r.y+e.frame.height,left:r.x,right:r.x+e.frame.width}),a={frame:i,scroll:{initial:e.scroll.initial,max:e.scroll.max,current:r,diff:{value:t,displacement:n}}};return a};function St(e,r){return e.map(function(t){return r[t]})}function Pi(e,r){for(var t=0;t<r.length;t++){var n=r[t].visible[e];if(n)return n}return null}var Ai=function(e){var r=e.impact,t=e.viewport,n=e.destination,i=e.draggables,a=e.maxScrollChange,o=fn(t,U(t.scroll.current,a)),l=n.frame?Mr(n,U(n.frame.scroll.current,a)):n,u=r.displaced,d=Oe({afterDragging:St(u.all,i),destination:n,displacedBy:r.displacedBy,viewport:o.frame,last:u,forceShouldAnimate:!1}),p=Oe({afterDragging:St(u.all,i),destination:l,displacedBy:r.displacedBy,viewport:t.frame,last:u,forceShouldAnimate:!1}),s={},c={},v=[u,d,p];u.all.forEach(function(g){var b=Pi(g,v);if(b){c[g]=b;return}s[g]=!0});var f=E({},r,{displaced:{all:u.all,invisible:s,visible:c}});return f},Ri=function(e,r){return U(e.scroll.diff.displacement,r)},Hr=function(e){var r=e.pageBorderBoxCenter,t=e.draggable,n=e.viewport,i=Ri(n,r),a=$(i,t.page.borderBox.center);return U(t.client.borderBox.center,a)},gn=function(e){var r=e.draggable,t=e.destination,n=e.newPageBorderBoxCenter,i=e.viewport,a=e.withDroppableDisplacement,o=e.onlyOnMainAxis,l=o===void 0?!1:o,u=$(n,r.page.borderBox.center),d=Te(r.page.borderBox,u),p={target:d,destination:t,withDroppableDisplacement:a,viewport:i};return l?fi(p):pn(p)},Bi=function(e){var r=e.isMovingForward,t=e.draggable,n=e.destination,i=e.draggables,a=e.previousImpact,o=e.viewport,l=e.previousPageBorderBoxCenter,u=e.previousClientSelection,d=e.afterCritical;if(!n.isEnabled)return null;var p=ye(n.descriptor.id,i),s=De(t,n),c=oi({isMovingForward:r,draggable:t,destination:n,insideDestination:p,previousImpact:a})||Di({isMovingForward:r,isInHomeList:s,draggable:t,draggables:i,destination:n,insideDestination:p,previousImpact:a,viewport:o,afterCritical:d});if(!c)return null;var v=nr({impact:c,draggable:t,droppable:n,draggables:i,afterCritical:d}),f=gn({draggable:t,destination:n,newPageBorderBoxCenter:v,viewport:o.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});if(f){var g=Hr({pageBorderBoxCenter:v,draggable:t,viewport:o});return{clientSelection:g,impact:c,scrollJumpRequest:null}}var b=$(v,l),m=Ai({impact:c,viewport:o,destination:n,draggables:i,maxScrollChange:b});return{clientSelection:u,impact:m,scrollJumpRequest:b}},H=function(r){var t=r.subject.active;return t||y(),t},Oi=function(e){var r=e.isMovingForward,t=e.pageBorderBoxCenter,n=e.source,i=e.droppables,a=e.viewport,o=n.subject.active;if(!o)return null;var l=n.axis,u=Q(o[l.start],o[l.end]),d=er(i).filter(function(s){return s!==n}).filter(function(s){return s.isEnabled}).filter(function(s){return!!s.subject.active}).filter(function(s){return cn(a.frame)(H(s))}).filter(function(s){var c=H(s);return r?o[l.crossAxisEnd]<c[l.crossAxisEnd]:c[l.crossAxisStart]<o[l.crossAxisStart]}).filter(function(s){var c=H(s),v=Q(c[l.start],c[l.end]);return u(c[l.start])||u(c[l.end])||v(o[l.start])||v(o[l.end])}).sort(function(s,c){var v=H(s)[l.crossAxisStart],f=H(c)[l.crossAxisStart];return r?v-f:f-v}).filter(function(s,c,v){return H(s)[l.crossAxisStart]===H(v[0])[l.crossAxisStart]});if(!d.length)return null;if(d.length===1)return d[0];var p=d.filter(function(s){var c=Q(H(s)[l.start],H(s)[l.end]);return c(t[l.line])});return p.length===1?p[0]:p.length>1?p.sort(function(s,c){return H(s)[l.start]-H(c)[l.start]})[0]:d.sort(function(s,c){var v=ht(t,yt(H(s))),f=ht(t,yt(H(c)));return v!==f?v-f:H(s)[l.start]-H(c)[l.start]})[0]},Ct=function(r,t){var n=r.page.borderBox.center;return ie(r.descriptor.id,t)?$(n,t.displacedBy.point):n},Ni=function(r,t){var n=r.page.borderBox;return ie(r.descriptor.id,t)?Te(n,he(t.displacedBy.point)):n},Ti=function(e){var r=e.pageBorderBoxCenter,t=e.viewport,n=e.destination,i=e.insideDestination,a=e.afterCritical,o=i.filter(function(l){return pn({target:Ni(l,a),destination:n,viewport:t.frame,withDroppableDisplacement:!0})}).sort(function(l,u){var d=Re(r,wr(n,Ct(l,a))),p=Re(r,wr(n,Ct(u,a)));return d<p?-1:p<d?1:l.descriptor.index-u.descriptor.index});return o[0]||null},Me=G(function(r,t){var n=t[r.line];return{value:n,point:ce(r.line,n)}}),Mi=function(r,t,n){var i=r.axis;if(r.descriptor.mode==="virtual")return ce(i.line,t[i.line]);var a=r.subject.page.contentBox[i.size],o=ye(r.descriptor.id,n),l=o.reduce(function(p,s){return p+s.client.marginBox[i.size]},0),u=l+t[i.line],d=u-a;return d<=0?null:ce(i.line,d)},mn=function(r,t){return E({},r,{scroll:E({},r.scroll,{max:t})})},bn=function(r,t,n){var i=r.frame;De(t,r)&&y(),r.subject.withPlaceholder&&y();var a=Me(r.axis,t.displaceBy).point,o=Mi(r,a,n),l={placeholderSize:a,increasedBy:o,oldFrameMaxScroll:r.frame?r.frame.scroll.max:null};if(!i){var u=ge({page:r.subject.page,withPlaceholder:l,axis:r.axis,frame:r.frame});return E({},r,{subject:u})}var d=o?U(i.scroll.max,o):i.scroll.max,p=mn(i,d),s=ge({page:r.subject.page,withPlaceholder:l,axis:r.axis,frame:p});return E({},r,{subject:s,frame:p})},Li=function(r){var t=r.subject.withPlaceholder;t||y();var n=r.frame;if(!n){var i=ge({page:r.subject.page,axis:r.axis,frame:null,withPlaceholder:null});return E({},r,{subject:i})}var a=t.oldFrameMaxScroll;a||y();var o=mn(n,a),l=ge({page:r.subject.page,axis:r.axis,frame:o,withPlaceholder:null});return E({},r,{subject:l,frame:o})},Fi=function(e){var r=e.previousPageBorderBoxCenter,t=e.moveRelativeTo,n=e.insideDestination,i=e.draggable,a=e.draggables,o=e.destination,l=e.viewport,u=e.afterCritical;if(!t){if(n.length)return null;var d={displaced:Be,displacedBy:sn,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:0}}},p=nr({impact:d,draggable:i,droppable:o,draggables:a,afterCritical:u}),s=De(i,o)?o:bn(o,i,a),c=gn({draggable:i,destination:s,newPageBorderBoxCenter:p,viewport:l.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});return c?d:null}var v=r[o.axis.line]<=t.page.borderBox.center[o.axis.line],f=function(){var b=t.descriptor.index;return t.descriptor.id===i.descriptor.id||v?b:b+1}(),g=Me(o.axis,i.displaceBy);return Qe({draggable:i,insideDestination:n,destination:o,viewport:l,displacedBy:g,last:Be,index:f})},Gi=function(e){var r=e.isMovingForward,t=e.previousPageBorderBoxCenter,n=e.draggable,i=e.isOver,a=e.draggables,o=e.droppables,l=e.viewport,u=e.afterCritical,d=Oi({isMovingForward:r,pageBorderBoxCenter:t,source:i,droppables:o,viewport:l});if(!d)return null;var p=ye(d.descriptor.id,a),s=Ti({pageBorderBoxCenter:t,viewport:l,destination:d,insideDestination:p,afterCritical:u}),c=Fi({previousPageBorderBoxCenter:t,destination:d,draggable:n,draggables:a,moveRelativeTo:s,insideDestination:p,viewport:l,afterCritical:u});if(!c)return null;var v=nr({impact:c,draggable:n,droppable:d,draggables:a,afterCritical:u}),f=Hr({pageBorderBoxCenter:v,draggable:n,viewport:l});return{clientSelection:f,impact:c,scrollJumpRequest:null}},z=function(e){var r=e.at;return r?r.type==="REORDER"?r.destination.droppableId:r.combine.droppableId:null},Wi=function(r,t){var n=z(r);return n?t[n]:null},Ui=function(e){var r=e.state,t=e.type,n=Wi(r.impact,r.dimensions.droppables),i=!!n,a=r.dimensions.droppables[r.critical.droppable.id],o=n||a,l=o.axis.direction,u=l==="vertical"&&(t==="MOVE_UP"||t==="MOVE_DOWN")||l==="horizontal"&&(t==="MOVE_LEFT"||t==="MOVE_RIGHT");if(u&&!i)return null;var d=t==="MOVE_DOWN"||t==="MOVE_RIGHT",p=r.dimensions.draggables[r.critical.draggable.id],s=r.current.page.borderBoxCenter,c=r.dimensions,v=c.draggables,f=c.droppables;return u?Bi({isMovingForward:d,previousPageBorderBoxCenter:s,draggable:p,destination:o,draggables:v,viewport:r.viewport,previousClientSelection:r.current.client.selection,previousImpact:r.impact,afterCritical:r.afterCritical}):Gi({isMovingForward:d,previousPageBorderBoxCenter:s,draggable:p,isOver:o,draggables:v,droppables:f,viewport:r.viewport,afterCritical:r.afterCritical})};function se(e){return e.phase==="DRAGGING"||e.phase==="COLLECTING"}function hn(e){var r=Q(e.top,e.bottom),t=Q(e.left,e.right);return function(i){return r(i.y)&&t(i.x)}}function Hi(e,r){return e.left<r.right&&e.right>r.left&&e.top<r.bottom&&e.bottom>r.top}function ki(e){var r=e.pageBorderBox,t=e.draggable,n=e.candidates,i=t.page.borderBox.center,a=n.map(function(o){var l=o.axis,u=ce(o.axis.line,r.center[l.line],o.page.borderBox.center[l.crossAxisLine]);return{id:o.descriptor.id,distance:Re(i,u)}}).sort(function(o,l){return l.distance-o.distance});return a[0]?a[0].id:null}function qi(e){var r=e.pageBorderBox,t=e.draggable,n=e.droppables,i=er(n).filter(function(a){if(!a.isEnabled)return!1;var o=a.subject.active;if(!o||!Hi(r,o))return!1;if(hn(o)(r.center))return!0;var l=a.axis,u=o.center[l.crossAxisLine],d=r[l.crossAxisStart],p=r[l.crossAxisEnd],s=Q(o[l.crossAxisStart],o[l.crossAxisEnd]),c=s(d),v=s(p);return!c&&!v?!0:c?d<u:p>u});return i.length?i.length===1?i[0].descriptor.id:ki({pageBorderBox:r,draggable:t,candidates:i}):null}var yn=function(r,t){return _(Te(r,t))},Vi=function(e,r){var t=e.frame;return t?yn(r,t.scroll.diff.value):r};function Dn(e){var r=e.displaced,t=e.id;return!!(r.visible[t]||r.invisible[t])}function $i(e){var r=e.draggable,t=e.closest,n=e.inHomeList;return t?n&&t.descriptor.index>r.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}var zi=function(e){var r=e.pageBorderBoxWithDroppableScroll,t=e.draggable,n=e.destination,i=e.insideDestination,a=e.last,o=e.viewport,l=e.afterCritical,u=n.axis,d=Me(n.axis,t.displaceBy),p=d.value,s=r[u.start],c=r[u.end],v=tr(t,i),f=oe(v,function(b){var m=b.descriptor.id,h=b.page.borderBox.center[u.line],x=ie(m,l),D=Dn({displaced:a,id:m});return x?D?c<=h:s<h-p:D?c<=h+p:s<h}),g=$i({draggable:t,closest:f,inHomeList:De(t,n)});return Qe({draggable:t,insideDestination:i,destination:n,viewport:o,last:a,displacedBy:d,index:g})},ji=4,Yi=function(e){var r=e.draggable,t=e.pageBorderBoxWithDroppableScroll,n=e.previousImpact,i=e.destination,a=e.insideDestination,o=e.afterCritical;if(!i.isCombineEnabled)return null;var l=i.axis,u=Me(i.axis,r.displaceBy),d=u.value,p=t[l.start],s=t[l.end],c=tr(r,a),v=oe(c,function(g){var b=g.descriptor.id,m=g.page.borderBox,h=m[l.size],x=h/ji,D=ie(b,o),P=Dn({displaced:n.displaced,id:b});return D?P?s>m[l.start]+x&&s<m[l.end]-x:p>m[l.start]-d+x&&p<m[l.end]-d-x:P?s>m[l.start]+d+x&&s<m[l.end]+d-x:p>m[l.start]+x&&p<m[l.end]-x});if(!v)return null;var f={displacedBy:u,displaced:n.displaced,at:{type:"COMBINE",combine:{draggableId:v.descriptor.id,droppableId:i.descriptor.id}}};return f},xn=function(e){var r=e.pageOffset,t=e.draggable,n=e.draggables,i=e.droppables,a=e.previousImpact,o=e.viewport,l=e.afterCritical,u=yn(t.page.borderBox,r),d=qi({pageBorderBox:u,draggable:t,droppables:i});if(!d)return li;var p=i[d],s=ye(p.descriptor.id,n),c=Vi(p,u);return Yi({pageBorderBoxWithDroppableScroll:c,draggable:t,previousImpact:a,destination:p,insideDestination:s,afterCritical:l})||zi({pageBorderBoxWithDroppableScroll:c,draggable:t,destination:p,insideDestination:s,last:a.displaced,viewport:o,afterCritical:l})},kr=function(e,r){var t;return E({},e,(t={},t[r.descriptor.id]=r,t))},Ki=function(r){var t=r.previousImpact,n=r.impact,i=r.droppables,a=z(t),o=z(n);if(!a||a===o)return i;var l=i[a];if(!l.subject.withPlaceholder)return i;var u=Li(l);return kr(i,u)},Ji=function(e){var r=e.draggable,t=e.draggables,n=e.droppables,i=e.previousImpact,a=e.impact,o=Ki({previousImpact:i,impact:a,droppables:n}),l=z(a);if(!l)return o;var u=n[l];if(De(r,u)||u.subject.withPlaceholder)return o;var d=bn(u,r,t);return kr(o,d)},Ee=function(e){var r=e.state,t=e.clientSelection,n=e.dimensions,i=e.viewport,a=e.impact,o=e.scrollJumpRequest,l=i||r.viewport,u=n||r.dimensions,d=t||r.current.client.selection,p=$(d,r.initial.client.selection),s={offset:p,selection:d,borderBoxCenter:U(r.initial.client.borderBoxCenter,p)},c={selection:U(s.selection,l.scroll.current),borderBoxCenter:U(s.borderBoxCenter,l.scroll.current),offset:U(s.offset,l.scroll.diff.value)},v={client:s,page:c};if(r.phase==="COLLECTING")return E({phase:"COLLECTING"},r,{dimensions:u,viewport:l,current:v});var f=u.draggables[r.critical.draggable.id],g=a||xn({pageOffset:c.offset,draggable:f,draggables:u.draggables,droppables:u.droppables,previousImpact:r.impact,viewport:l,afterCritical:r.afterCritical}),b=Ji({draggable:f,impact:g,previousImpact:r.impact,draggables:u.draggables,droppables:u.droppables}),m=E({},r,{current:v,dimensions:{draggables:u.draggables,droppables:b},impact:g,viewport:l,scrollJumpRequest:o||null,forceShouldAnimate:o?!1:null});return m};function Xi(e,r){return e.map(function(t){return r[t]})}var In=function(e){var r=e.impact,t=e.viewport,n=e.draggables,i=e.destination,a=e.forceShouldAnimate,o=r.displaced,l=Xi(o.all,n),u=Oe({afterDragging:l,destination:i,displacedBy:r.displacedBy,viewport:t.frame,forceShouldAnimate:a,last:o});return E({},r,{displaced:u})},Sn=function(e){var r=e.impact,t=e.draggable,n=e.droppable,i=e.draggables,a=e.viewport,o=e.afterCritical,l=nr({impact:r,draggable:t,draggables:i,droppable:n,afterCritical:o});return Hr({pageBorderBoxCenter:l,draggable:t,viewport:a})},Cn=function(e){var r=e.state,t=e.dimensions,n=e.viewport;r.movementMode!=="SNAP"&&y();var i=r.impact,a=n||r.viewport,o=t||r.dimensions,l=o.draggables,u=o.droppables,d=l[r.critical.draggable.id],p=z(i);p||y();var s=u[p],c=In({impact:i,viewport:a,destination:s,draggables:l}),v=Sn({impact:c,draggable:d,droppable:s,draggables:l,viewport:a,afterCritical:r.afterCritical});return Ee({impact:c,clientSelection:v,state:r,dimensions:o,viewport:a})},Qi=function(e){return{index:e.index,droppableId:e.droppableId}},wn=function(e){var r=e.draggable,t=e.home,n=e.draggables,i=e.viewport,a=Me(t.axis,r.displaceBy),o=ye(t.descriptor.id,n),l=o.indexOf(r);l===-1&&y();var u=o.slice(l+1),d=u.reduce(function(v,f){return v[f.descriptor.id]=!0,v},{}),p={inVirtualList:t.descriptor.mode==="virtual",displacedBy:a,effected:d},s=Oe({afterDragging:u,destination:t,displacedBy:a,last:null,viewport:i.frame,forceShouldAnimate:!1}),c={displaced:s,displacedBy:a,at:{type:"REORDER",destination:Qi(r.descriptor)}};return{impact:c,afterCritical:p}},Zi=function(e,r){return{draggables:e.draggables,droppables:kr(e.droppables,r)}},_i=function(e){var r=e.draggable,t=e.offset,n=e.initialWindowScroll,i=je(r.client,t),a=Ye(i,n),o=E({},r,{placeholder:E({},r.placeholder,{client:i}),client:i,page:a});return o},eo=function(e){var r=e.frame;return r||y(),r},ro=function(e){var r=e.additions,t=e.updatedDroppables,n=e.viewport,i=n.scroll.diff.value;return r.map(function(a){var o=a.descriptor.droppableId,l=t[o],u=eo(l),d=u.scroll.diff.value,p=U(i,d),s=_i({draggable:a,offset:p,initialWindowScroll:n.scroll.initial});return s})},to=function(e){var r=e.state,t=e.published,n=t.modified.map(function(x){var D=r.dimensions.droppables[x.droppableId],P=Mr(D,x.scroll);return P}),i=E({},r.dimensions.droppables,{},ln(n)),a=un(ro({additions:t.additions,updatedDroppables:i,viewport:r.viewport})),o=E({},r.dimensions.draggables,{},a);t.removals.forEach(function(x){delete o[x]});var l={droppables:i,draggables:o},u=z(r.impact),d=u?l.droppables[u]:null,p=l.draggables[r.critical.draggable.id],s=l.droppables[r.critical.droppable.id],c=wn({draggable:p,home:s,draggables:o,viewport:r.viewport}),v=c.impact,f=c.afterCritical,g=d&&d.isCombineEnabled?r.impact:v,b=xn({pageOffset:r.current.page.offset,draggable:l.draggables[r.critical.draggable.id],draggables:l.draggables,droppables:l.droppables,previousImpact:g,viewport:r.viewport,afterCritical:f}),m=E({phase:"DRAGGING"},r,{phase:"DRAGGING",impact:b,onLiftImpact:v,dimensions:l,afterCritical:f,forceShouldAnimate:!1});if(r.phase==="COLLECTING")return m;var h=E({phase:"DROP_PENDING"},m,{phase:"DROP_PENDING",reason:r.reason,isWaiting:!1});return h},Er=function(r){return r.movementMode==="SNAP"},br=function(r,t,n){var i=Zi(r.dimensions,t);return!Er(r)||n?Ee({state:r,dimensions:i}):Cn({state:r,dimensions:i})};function hr(e){return e.isDragging&&e.movementMode==="SNAP"?E({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}var wt={phase:"IDLE",completed:null,shouldFlush:!1},no=function(e,r){if(e===void 0&&(e=wt),r.type==="FLUSH")return E({},wt,{shouldFlush:!0});if(r.type==="INITIAL_PUBLISH"){e.phase!=="IDLE"&&y();var t=r.payload,n=t.critical,i=t.clientSelection,a=t.viewport,o=t.dimensions,l=t.movementMode,u=o.draggables[n.draggable.id],d=o.droppables[n.droppable.id],p={selection:i,borderBoxCenter:u.client.borderBox.center,offset:W},s={client:p,page:{selection:U(p.selection,a.scroll.initial),borderBoxCenter:U(p.selection,a.scroll.initial),offset:U(p.selection,a.scroll.diff.value)}},c=er(o.droppables).every(function(ur){return!ur.isFixedOnPage}),v=wn({draggable:u,home:d,draggables:o.draggables,viewport:a}),f=v.impact,g=v.afterCritical,b={phase:"DRAGGING",isDragging:!0,critical:n,movementMode:l,dimensions:o,initial:s,current:s,isWindowScrollAllowed:c,impact:f,afterCritical:g,onLiftImpact:f,viewport:a,scrollJumpRequest:null,forceShouldAnimate:null};return b}if(r.type==="COLLECTION_STARTING"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&y();var m=E({phase:"COLLECTING"},e,{phase:"COLLECTING"});return m}if(r.type==="PUBLISH_WHILE_DRAGGING")return e.phase==="COLLECTING"||e.phase==="DROP_PENDING"||y(),to({state:e,published:r.payload});if(r.type==="MOVE"){if(e.phase==="DROP_PENDING")return e;se(e)||y();var h=r.payload.client;return ne(h,e.current.client.selection)?e:Ee({state:e,clientSelection:h,impact:Er(e)?e.impact:null})}if(r.type==="UPDATE_DROPPABLE_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="COLLECTING")return hr(e);se(e)||y();var x=r.payload,D=x.id,P=x.newScroll,w=e.dimensions.droppables[D];if(!w)return e;var B=Mr(w,P);return br(e,B,!1)}if(r.type==="UPDATE_DROPPABLE_IS_ENABLED"){if(e.phase==="DROP_PENDING")return e;se(e)||y();var T=r.payload,A=T.id,M=T.isEnabled,O=e.dimensions.droppables[A];O||y(),O.isEnabled===M&&y();var k=E({},O,{isEnabled:M});return br(e,k,!0)}if(r.type==="UPDATE_DROPPABLE_IS_COMBINE_ENABLED"){if(e.phase==="DROP_PENDING")return e;se(e)||y();var I=r.payload,L=I.id,Y=I.isCombineEnabled,K=e.dimensions.droppables[L];K||y(),K.isCombineEnabled===Y&&y();var de=E({},K,{isCombineEnabled:Y});return br(e,de,!0)}if(r.type==="MOVE_BY_WINDOW_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="DROP_ANIMATING")return e;se(e)||y(),e.isWindowScrollAllowed||y();var ee=r.payload.newScroll;if(ne(e.viewport.scroll.current,ee))return hr(e);var pe=fn(e.viewport,ee);return Er(e)?Cn({state:e,viewport:pe}):Ee({state:e,viewport:pe})}if(r.type==="UPDATE_VIEWPORT_MAX_SCROLL"){if(!se(e))return e;var Z=r.payload.maxScroll;if(ne(Z,e.viewport.scroll.max))return e;var le=E({},e.viewport,{scroll:E({},e.viewport.scroll,{max:Z})});return E({phase:"DRAGGING"},e,{viewport:le})}if(r.type==="MOVE_UP"||r.type==="MOVE_DOWN"||r.type==="MOVE_LEFT"||r.type==="MOVE_RIGHT"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&y();var V=Ui({state:e,type:r.type});return V?Ee({state:e,impact:V.impact,clientSelection:V.clientSelection,scrollJumpRequest:V.scrollJumpRequest}):e}if(r.type==="DROP_PENDING"){var xe=r.payload.reason;e.phase!=="COLLECTING"&&y();var Le=E({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:xe});return Le}if(r.type==="DROP_ANIMATE"){var re=r.payload,Fe=re.completed,Ge=re.dropDuration,We=re.newHomeClientOffset;e.phase==="DRAGGING"||e.phase==="DROP_PENDING"||y();var lr={phase:"DROP_ANIMATING",completed:Fe,dropDuration:Ge,newHomeClientOffset:We,dimensions:e.dimensions};return lr}if(r.type==="DROP_COMPLETE"){var ve=r.payload.completed;return{phase:"IDLE",completed:ve,shouldFlush:!1}}return e},ao=function(r){return{type:"BEFORE_INITIAL_CAPTURE",payload:r}},io=function(r){return{type:"LIFT",payload:r}},oo=function(r){return{type:"INITIAL_PUBLISH",payload:r}},lo=function(r){return{type:"PUBLISH_WHILE_DRAGGING",payload:r}},uo=function(){return{type:"COLLECTION_STARTING",payload:null}},so=function(r){return{type:"UPDATE_DROPPABLE_SCROLL",payload:r}},co=function(r){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:r}},po=function(r){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:r}},En=function(r){return{type:"MOVE",payload:r}},vo=function(r){return{type:"MOVE_BY_WINDOW_SCROLL",payload:r}},fo=function(r){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:r}},go=function(){return{type:"MOVE_UP",payload:null}},mo=function(){return{type:"MOVE_DOWN",payload:null}},bo=function(){return{type:"MOVE_RIGHT",payload:null}},ho=function(){return{type:"MOVE_LEFT",payload:null}},qr=function(){return{type:"FLUSH",payload:null}},yo=function(r){return{type:"DROP_ANIMATE",payload:r}},Vr=function(r){return{type:"DROP_COMPLETE",payload:r}},Pn=function(r){return{type:"DROP",payload:r}},Do=function(r){return{type:"DROP_PENDING",payload:r}},An=function(){return{type:"DROP_ANIMATION_FINISHED",payload:null}},xo=function(e){return function(r){var t=r.getState,n=r.dispatch;return function(i){return function(a){if(a.type!=="LIFT"){i(a);return}var o=a.payload,l=o.id,u=o.clientSelection,d=o.movementMode,p=t();p.phase==="DROP_ANIMATING"&&n(Vr({completed:p.completed})),t().phase!=="IDLE"&&y(),n(qr()),n(ao({draggableId:l,movementMode:d}));var s={shouldPublishImmediately:d==="SNAP"},c={draggableId:l,scrollOptions:s},v=e.startPublishing(c),f=v.critical,g=v.dimensions,b=v.viewport;n(oo({critical:f,dimensions:g,clientSelection:u,movementMode:d,viewport:b}))}}}},Io=function(e){return function(){return function(r){return function(t){t.type==="INITIAL_PUBLISH"&&e.dragging(),t.type==="DROP_ANIMATE"&&e.dropping(t.payload.completed.result.reason),(t.type==="FLUSH"||t.type==="DROP_COMPLETE")&&e.resting(),r(t)}}}},$r={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},Ne={opacity:{drop:0,combining:.7},scale:{drop:.75}},zr={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},ue=zr.outOfTheWay+"s "+$r.outOfTheWay,Pe={fluid:"opacity "+ue,snap:"transform "+ue+", opacity "+ue,drop:function(r){var t=r+"s "+$r.drop;return"transform "+t+", opacity "+t},outOfTheWay:"transform "+ue,placeholder:"height "+ue+", width "+ue+", margin "+ue},Et=function(r){return ne(r,W)?null:"translate("+r.x+"px, "+r.y+"px)"},Pr={moveTo:Et,drop:function(r,t){var n=Et(r);return n?t?n+" scale("+Ne.scale.drop+")":n:null}},Ar=zr.minDropTime,Rn=zr.maxDropTime,So=Rn-Ar,Pt=1500,Co=.6,wo=function(e){var r=e.current,t=e.destination,n=e.reason,i=Re(r,t);if(i<=0)return Ar;if(i>=Pt)return Rn;var a=i/Pt,o=Ar+So*a,l=n==="CANCEL"?o*Co:o;return Number(l.toFixed(2))},Eo=function(e){var r=e.impact,t=e.draggable,n=e.dimensions,i=e.viewport,a=e.afterCritical,o=n.draggables,l=n.droppables,u=z(r),d=u?l[u]:null,p=l[t.descriptor.droppableId],s=Sn({impact:r,draggable:t,draggables:o,afterCritical:a,droppable:d||p,viewport:i}),c=$(s,t.client.borderBox.center);return c},Po=function(e){var r=e.draggables,t=e.reason,n=e.lastImpact,i=e.home,a=e.viewport,o=e.onLiftImpact;if(!n.at||t!=="DROP"){var l=In({draggables:r,impact:o,destination:i,viewport:a,forceShouldAnimate:!0});return{impact:l,didDropInsideDroppable:!1}}if(n.at.type==="REORDER")return{impact:n,didDropInsideDroppable:!0};var u=E({},n,{displaced:Be});return{impact:u,didDropInsideDroppable:!0}},Ao=function(e){var r=e.getState,t=e.dispatch;return function(n){return function(i){if(i.type!=="DROP"){n(i);return}var a=r(),o=i.payload.reason;if(a.phase==="COLLECTING"){t(Do({reason:o}));return}if(a.phase!=="IDLE"){var l=a.phase==="DROP_PENDING"&&a.isWaiting;l&&y(),a.phase==="DRAGGING"||a.phase==="DROP_PENDING"||y();var u=a.critical,d=a.dimensions,p=d.draggables[a.critical.draggable.id],s=Po({reason:o,lastImpact:a.impact,afterCritical:a.afterCritical,onLiftImpact:a.onLiftImpact,home:a.dimensions.droppables[a.critical.droppable.id],viewport:a.viewport,draggables:a.dimensions.draggables}),c=s.impact,v=s.didDropInsideDroppable,f=v?Fr(c):null,g=v?rr(c):null,b={index:u.draggable.index,droppableId:u.droppable.id},m={draggableId:p.descriptor.id,type:p.descriptor.type,source:b,reason:o,mode:a.movementMode,destination:f,combine:g},h=Eo({impact:c,draggable:p,dimensions:d,viewport:a.viewport,afterCritical:a.afterCritical}),x={critical:a.critical,afterCritical:a.afterCritical,result:m,impact:c},D=!ne(a.current.client.offset,h)||!!m.combine;if(!D){t(Vr({completed:x}));return}var P=wo({current:a.current.client.offset,destination:h,reason:o}),w={newHomeClientOffset:h,dropDuration:P,completed:x};t(yo(w))}}}},Bn=function(){return{x:window.pageXOffset,y:window.pageYOffset}};function Ro(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(t){t.target!==window&&t.target!==window.document||e()}}}function Bo(e){var r=e.onWindowScroll;function t(){r(Bn())}var n=Ae(t),i=Ro(n),a=te;function o(){return a!==te}function l(){o()&&y(),a=X(window,[i])}function u(){o()||y(),n.cancel(),a(),a=te}return{start:l,stop:u,isActive:o}}var Oo=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},No=function(e){var r=Bo({onWindowScroll:function(n){e.dispatch(vo({newScroll:n}))}});return function(t){return function(n){!r.isActive()&&n.type==="INITIAL_PUBLISH"&&r.start(),r.isActive()&&Oo(n)&&r.stop(),t(n)}}},To=function(e){var r=!1,t=!1,n=setTimeout(function(){t=!0}),i=function(o){r||t||(r=!0,e(o),clearTimeout(n))};return i.wasCalled=function(){return r},i},Mo=function(){var e=[],r=function(a){var o=Lr(e,function(d){return d.timerId===a});o===-1&&y();var l=e.splice(o,1),u=l[0];u.callback()},t=function(a){var o=setTimeout(function(){return r(o)}),l={timerId:o,callback:a};e.push(l)},n=function(){if(e.length){var a=[].concat(e);e.length=0,a.forEach(function(o){clearTimeout(o.timerId),o.callback()})}};return{add:t,flush:n}},Lo=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.droppableId===t.droppableId&&r.index===t.index},Fo=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.draggableId===t.draggableId&&r.droppableId===t.droppableId},Go=function(r,t){if(r===t)return!0;var n=r.draggable.id===t.draggable.id&&r.draggable.droppableId===t.draggable.droppableId&&r.draggable.type===t.draggable.type&&r.draggable.index===t.draggable.index,i=r.droppable.id===t.droppable.id&&r.droppable.type===t.droppable.type;return n&&i},Se=function(r,t){t()},He=function(r,t){return{draggableId:r.draggable.id,type:r.droppable.type,source:{droppableId:r.droppable.id,index:r.draggable.index},mode:t}},yr=function(r,t,n,i){if(!r){n(i(t));return}var a=To(n),o={announce:a};r(t,o),a.wasCalled()||n(i(t))},Wo=function(e,r){var t=Mo(),n=null,i=function(c,v){n&&y(),Se("onBeforeCapture",function(){var f=e().onBeforeCapture;if(f){var g={draggableId:c,mode:v};f(g)}})},a=function(c,v){n&&y(),Se("onBeforeDragStart",function(){var f=e().onBeforeDragStart;f&&f(He(c,v))})},o=function(c,v){n&&y();var f=He(c,v);n={mode:v,lastCritical:c,lastLocation:f.source,lastCombine:null},t.add(function(){Se("onDragStart",function(){return yr(e().onDragStart,f,r,$e.onDragStart)})})},l=function(c,v){var f=Fr(v),g=rr(v);n||y();var b=!Go(c,n.lastCritical);b&&(n.lastCritical=c);var m=!Lo(n.lastLocation,f);m&&(n.lastLocation=f);var h=!Fo(n.lastCombine,g);if(h&&(n.lastCombine=g),!(!b&&!m&&!h)){var x=E({},He(c,n.mode),{combine:g,destination:f});t.add(function(){Se("onDragUpdate",function(){return yr(e().onDragUpdate,x,r,$e.onDragUpdate)})})}},u=function(){n||y(),t.flush()},d=function(c){n||y(),n=null,Se("onDragEnd",function(){return yr(e().onDragEnd,c,r,$e.onDragEnd)})},p=function(){if(n){var c=E({},He(n.lastCritical,n.mode),{combine:null,destination:null,reason:"CANCEL"});d(c)}};return{beforeCapture:i,beforeStart:a,start:o,update:l,flush:u,drop:d,abort:p}},Uo=function(e,r){var t=Wo(e,r);return function(n){return function(i){return function(a){if(a.type==="BEFORE_INITIAL_CAPTURE"){t.beforeCapture(a.payload.draggableId,a.payload.movementMode);return}if(a.type==="INITIAL_PUBLISH"){var o=a.payload.critical;t.beforeStart(o,a.payload.movementMode),i(a),t.start(o,a.payload.movementMode);return}if(a.type==="DROP_COMPLETE"){var l=a.payload.completed.result;t.flush(),i(a),t.drop(l);return}if(i(a),a.type==="FLUSH"){t.abort();return}var u=n.getState();u.phase==="DRAGGING"&&t.update(u.critical,u.impact)}}}},Ho=function(e){return function(r){return function(t){if(t.type!=="DROP_ANIMATION_FINISHED"){r(t);return}var n=e.getState();n.phase!=="DROP_ANIMATING"&&y(),e.dispatch(Vr({completed:n.completed}))}}},ko=function(e){var r=null,t=null;function n(){t&&(cancelAnimationFrame(t),t=null),r&&(r(),r=null)}return function(i){return function(a){if((a.type==="FLUSH"||a.type==="DROP_COMPLETE"||a.type==="DROP_ANIMATION_FINISHED")&&n(),i(a),a.type==="DROP_ANIMATE"){var o={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){var u=e.getState();u.phase==="DROP_ANIMATING"&&e.dispatch(An())}};t=requestAnimationFrame(function(){t=null,r=X(window,[o])})}}}},qo=function(e){return function(){return function(r){return function(t){(t.type==="DROP_COMPLETE"||t.type==="FLUSH"||t.type==="DROP_ANIMATE")&&e.stopPublishing(),r(t)}}}},Vo=function(e){var r=!1;return function(){return function(t){return function(n){if(n.type==="INITIAL_PUBLISH"){r=!0,e.tryRecordFocus(n.payload.critical.draggable.id),t(n),e.tryRestoreFocusRecorded();return}if(t(n),!!r){if(n.type==="FLUSH"){r=!1,e.tryRestoreFocusRecorded();return}if(n.type==="DROP_COMPLETE"){r=!1;var i=n.payload.completed.result;i.combine&&e.tryShiftRecord(i.draggableId,i.combine.draggableId),e.tryRestoreFocusRecorded()}}}}}},$o=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},zo=function(e){return function(r){return function(t){return function(n){if($o(n)){e.stop(),t(n);return}if(n.type==="INITIAL_PUBLISH"){t(n);var i=r.getState();i.phase!=="DRAGGING"&&y(),e.start(i);return}t(n),e.scroll(r.getState())}}}},jo=function(e){return function(r){return function(t){if(r(t),t.type==="PUBLISH_WHILE_DRAGGING"){var n=e.getState();n.phase==="DROP_PENDING"&&(n.isWaiting||e.dispatch(Pn({reason:n.reason})))}}}},Yo=zt,Ko=function(e){var r=e.dimensionMarshal,t=e.focusMarshal,n=e.styleMarshal,i=e.getResponders,a=e.announce,o=e.autoScroller;return $t(no,Yo(ra(Io(n),qo(r),xo(r),Ao,Ho,ko,jo,zo(o),No,Vo(t),Uo(i,a))))},Dr=function(){return{additions:{},removals:{},modified:{}}};function Jo(e){var r=e.registry,t=e.callbacks,n=Dr(),i=null,a=function(){i||(t.collectionStarting(),i=requestAnimationFrame(function(){i=null;var p=n,s=p.additions,c=p.removals,v=p.modified,f=Object.keys(s).map(function(m){return r.draggable.getById(m).getDimension(W)}).sort(function(m,h){return m.descriptor.index-h.descriptor.index}),g=Object.keys(v).map(function(m){var h=r.droppable.getById(m),x=h.callbacks.getScrollWhileDragging();return{droppableId:m,scroll:x}}),b={additions:f,removals:Object.keys(c),modified:g};n=Dr(),t.publish(b)}))},o=function(p){var s=p.descriptor.id;n.additions[s]=p,n.modified[p.descriptor.droppableId]=!0,n.removals[s]&&delete n.removals[s],a()},l=function(p){var s=p.descriptor;n.removals[s.id]=!0,n.modified[s.droppableId]=!0,n.additions[s.id]&&delete n.additions[s.id],a()},u=function(){i&&(cancelAnimationFrame(i),i=null,n=Dr())};return{add:o,remove:l,stop:u}}var On=function(e){var r=e.scrollHeight,t=e.scrollWidth,n=e.height,i=e.width,a=$({x:t,y:r},{x:i,y:n}),o={x:Math.max(0,a.x),y:Math.max(0,a.y)};return o},Nn=function(){var e=document.documentElement;return e||y(),e},Tn=function(){var e=Nn(),r=On({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight});return r},Xo=function(){var e=Bn(),r=Tn(),t=e.y,n=e.x,i=Nn(),a=i.clientWidth,o=i.clientHeight,l=n+a,u=t+o,d=_({top:t,left:n,right:l,bottom:u}),p={frame:d,scroll:{initial:e,current:e,max:r,diff:{value:W,displacement:W}}};return p},Qo=function(e){var r=e.critical,t=e.scrollOptions,n=e.registry,i=Xo(),a=i.scroll.current,o=r.droppable,l=n.droppable.getAllByType(o.type).map(function(s){return s.callbacks.getDimensionAndWatchScroll(a,t)}),u=n.draggable.getAllByType(r.draggable.type).map(function(s){return s.getDimension(a)}),d={draggables:un(u),droppables:ln(l)},p={dimensions:d,critical:r,viewport:i};return p};function At(e,r,t){if(t.descriptor.id===r.id||t.descriptor.type!==r.type)return!1;var n=e.droppable.getById(t.descriptor.droppableId);return n.descriptor.mode==="virtual"}var Zo=function(e,r){var t=null,n=Jo({callbacks:{publish:r.publishWhileDragging,collectionStarting:r.collectionStarting},registry:e}),i=function(v,f){e.droppable.exists(v)||y(),t&&r.updateDroppableIsEnabled({id:v,isEnabled:f})},a=function(v,f){t&&(e.droppable.exists(v)||y(),r.updateDroppableIsCombineEnabled({id:v,isCombineEnabled:f}))},o=function(v,f){t&&(e.droppable.exists(v)||y(),r.updateDroppableScroll({id:v,newScroll:f}))},l=function(v,f){t&&e.droppable.getById(v).callbacks.scroll(f)},u=function(){if(t){n.stop();var v=t.critical.droppable;e.droppable.getAllByType(v.type).forEach(function(f){return f.callbacks.dragStopped()}),t.unsubscribe(),t=null}},d=function(v){t||y();var f=t.critical.draggable;v.type==="ADDITION"&&At(e,f,v.value)&&n.add(v.value),v.type==="REMOVAL"&&At(e,f,v.value)&&n.remove(v.value)},p=function(v){t&&y();var f=e.draggable.getById(v.draggableId),g=e.droppable.getById(f.descriptor.droppableId),b={draggable:f.descriptor,droppable:g.descriptor},m=e.subscribe(d);return t={critical:b,unsubscribe:m},Qo({critical:b,registry:e,scrollOptions:v.scrollOptions})},s={updateDroppableIsEnabled:i,updateDroppableIsCombineEnabled:a,scrollDroppable:l,updateDroppableScroll:o,startPublishing:p,stopPublishing:u};return s},Mn=function(e,r){return e.phase==="IDLE"?!0:e.phase!=="DROP_ANIMATING"||e.completed.result.draggableId===r?!1:e.completed.result.reason==="DROP"},_o=function(e){window.scrollBy(e.x,e.y)},el=G(function(e){return er(e).filter(function(r){return!(!r.isEnabled||!r.frame)})}),rl=function(r,t){var n=oe(el(t),function(i){return i.frame||y(),hn(i.frame.pageMarginBox)(r)});return n},tl=function(e){var r=e.center,t=e.destination,n=e.droppables;if(t){var i=n[t];return i.frame?i:null}var a=rl(r,n);return a},ae={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:function(r){return Math.pow(r,2)},durationDampening:{stopDampeningAt:1200,accelerateAt:360}},nl=function(e,r){var t=e[r.size]*ae.startFromPercentage,n=e[r.size]*ae.maxScrollAtPercentage,i={startScrollingFrom:t,maxScrollValueAt:n};return i},Ln=function(e){var r=e.startOfRange,t=e.endOfRange,n=e.current,i=t-r;if(i===0)return 0;var a=n-r,o=a/i;return o},jr=1,al=function(e,r){if(e>r.startScrollingFrom)return 0;if(e<=r.maxScrollValueAt)return ae.maxPixelScroll;if(e===r.startScrollingFrom)return jr;var t=Ln({startOfRange:r.maxScrollValueAt,endOfRange:r.startScrollingFrom,current:e}),n=1-t,i=ae.maxPixelScroll*ae.ease(n);return Math.ceil(i)},Rt=ae.durationDampening.accelerateAt,Bt=ae.durationDampening.stopDampeningAt,il=function(e,r){var t=r,n=Bt,i=Date.now(),a=i-t;if(a>=Bt)return e;if(a<Rt)return jr;var o=Ln({startOfRange:Rt,endOfRange:n,current:a}),l=e*ae.ease(o);return Math.ceil(l)},Ot=function(e){var r=e.distanceToEdge,t=e.thresholds,n=e.dragStartTime,i=e.shouldUseTimeDampening,a=al(r,t);return a===0?0:i?Math.max(il(a,n),jr):a},Nt=function(e){var r=e.container,t=e.distanceToEdges,n=e.dragStartTime,i=e.axis,a=e.shouldUseTimeDampening,o=nl(r,i),l=t[i.end]<t[i.start];return l?Ot({distanceToEdge:t[i.end],thresholds:o,dragStartTime:n,shouldUseTimeDampening:a}):-1*Ot({distanceToEdge:t[i.start],thresholds:o,dragStartTime:n,shouldUseTimeDampening:a})},ol=function(e){var r=e.container,t=e.subject,n=e.proposedScroll,i=t.height>r.height,a=t.width>r.width;return!a&&!i?n:a&&i?null:{x:a?0:n.x,y:i?0:n.y}},ll=an(function(e){return e===0?0:e}),Fn=function(e){var r=e.dragStartTime,t=e.container,n=e.subject,i=e.center,a=e.shouldUseTimeDampening,o={top:i.y-t.top,right:t.right-i.x,bottom:t.bottom-i.y,left:i.x-t.left},l=Nt({container:t,distanceToEdges:o,dragStartTime:r,axis:Gr,shouldUseTimeDampening:a}),u=Nt({container:t,distanceToEdges:o,dragStartTime:r,axis:dn,shouldUseTimeDampening:a}),d=ll({x:u,y:l});if(ne(d,W))return null;var p=ol({container:t,subject:n,proposedScroll:d});return p?ne(p,W)?null:p:null},ul=an(function(e){return e===0?0:e>0?1:-1}),Yr=function(){var e=function(t,n){return t<0?t:t>n?t-n:0};return function(r){var t=r.current,n=r.max,i=r.change,a=U(t,i),o={x:e(a.x,n.x),y:e(a.y,n.y)};return ne(o,W)?null:o}}(),Gn=function(r){var t=r.max,n=r.current,i=r.change,a={x:Math.max(n.x,t.x),y:Math.max(n.y,t.y)},o=ul(i),l=Yr({max:a,current:n,change:o});return!l||o.x!==0&&l.x===0||o.y!==0&&l.y===0},Kr=function(r,t){return Gn({current:r.scroll.current,max:r.scroll.max,change:t})},sl=function(r,t){if(!Kr(r,t))return null;var n=r.scroll.max,i=r.scroll.current;return Yr({current:i,max:n,change:t})},Jr=function(r,t){var n=r.frame;return n?Gn({current:n.scroll.current,max:n.scroll.max,change:t}):!1},cl=function(r,t){var n=r.frame;return!n||!Jr(r,t)?null:Yr({current:n.scroll.current,max:n.scroll.max,change:t})},dl=function(e){var r=e.viewport,t=e.subject,n=e.center,i=e.dragStartTime,a=e.shouldUseTimeDampening,o=Fn({dragStartTime:i,container:r.frame,subject:t,center:n,shouldUseTimeDampening:a});return o&&Kr(r,o)?o:null},pl=function(e){var r=e.droppable,t=e.subject,n=e.center,i=e.dragStartTime,a=e.shouldUseTimeDampening,o=r.frame;if(!o)return null;var l=Fn({dragStartTime:i,container:o.pageMarginBox,subject:t,center:n,shouldUseTimeDampening:a});return l&&Jr(r,l)?l:null},Tt=function(e){var r=e.state,t=e.dragStartTime,n=e.shouldUseTimeDampening,i=e.scrollWindow,a=e.scrollDroppable,o=r.current.page.borderBoxCenter,l=r.dimensions.draggables[r.critical.draggable.id],u=l.page.marginBox;if(r.isWindowScrollAllowed){var d=r.viewport,p=dl({dragStartTime:t,viewport:d,subject:u,center:o,shouldUseTimeDampening:n});if(p){i(p);return}}var s=tl({center:o,destination:z(r.impact),droppables:r.dimensions.droppables});if(s){var c=pl({dragStartTime:t,droppable:s,subject:u,center:o,shouldUseTimeDampening:n});c&&a(s.descriptor.id,c)}},vl=function(e){var r=e.scrollWindow,t=e.scrollDroppable,n=Ae(r),i=Ae(t),a=null,o=function(p){a||y();var s=a,c=s.shouldUseTimeDampening,v=s.dragStartTime;Tt({state:p,scrollWindow:n,scrollDroppable:i,dragStartTime:v,shouldUseTimeDampening:c})},l=function(p){a&&y();var s=Date.now(),c=!1,v=function(){c=!0};Tt({state:p,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:v,scrollDroppable:v}),a={dragStartTime:s,shouldUseTimeDampening:c},c&&o(p)},u=function(){a&&(n.cancel(),i.cancel(),a=null)};return{start:l,stop:u,scroll:o}},fl=function(e){var r=e.move,t=e.scrollDroppable,n=e.scrollWindow,i=function(d,p){var s=U(d.current.client.selection,p);r({client:s})},a=function(d,p){if(!Jr(d,p))return p;var s=cl(d,p);if(!s)return t(d.descriptor.id,p),null;var c=$(p,s);t(d.descriptor.id,c);var v=$(p,c);return v},o=function(d,p,s){if(!d||!Kr(p,s))return s;var c=sl(p,s);if(!c)return n(s),null;var v=$(s,c);n(v);var f=$(s,v);return f},l=function(d){var p=d.scrollJumpRequest;if(p){var s=z(d.impact);s||y();var c=a(d.dimensions.droppables[s],p);if(c){var v=d.viewport,f=o(d.isWindowScrollAllowed,v,c);f&&i(d,f)}}};return l},gl=function(e){var r=e.scrollDroppable,t=e.scrollWindow,n=e.move,i=vl({scrollWindow:t,scrollDroppable:r}),a=fl({move:n,scrollWindow:t,scrollDroppable:r}),o=function(d){if(d.phase==="DRAGGING"){if(d.movementMode==="FLUID"){i.scroll(d);return}d.scrollJumpRequest&&a(d)}},l={scroll:o,start:i.start,stop:i.stop};return l},me="data-rbd",be=function(){var e=me+"-drag-handle";return{base:e,draggableId:e+"-draggable-id",contextId:e+"-context-id"}}(),Rr=function(){var e=me+"-draggable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),ml=function(){var e=me+"-droppable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),Mt={contextId:me+"-scroll-container-context-id"},bl=function(r){return function(t){return"["+t+'="'+r+'"]'}},Ce=function(r,t){return r.map(function(n){var i=n.styles[t];return i?n.selector+" { "+i+" }":""}).join(" ")},hl="pointer-events: none;",yl=function(e){var r=bl(e),t=function(){var l=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:r(be.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:l,dragging:hl,dropAnimating:l}}}(),n=function(){var l=`
      transition: `+Pe.outOfTheWay+`;
    `;return{selector:r(Rr.contextId),styles:{dragging:l,dropAnimating:l,userCancel:l}}}(),i={selector:r(ml.contextId),styles:{always:"overflow-anchor: none;"}},a={selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}},o=[n,t,i,a];return{always:Ce(o,"always"),resting:Ce(o,"resting"),dragging:Ce(o,"dragging"),dropAnimating:Ce(o,"dropAnimating"),userCancel:Ce(o,"userCancel")}},j=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?S.useLayoutEffect:S.useEffect,xr=function(){var r=document.querySelector("head");return r||y(),r},Lt=function(r){var t=document.createElement("style");return r&&t.setAttribute("nonce",r),t.type="text/css",t};function Dl(e,r){var t=R(function(){return yl(e)},[e]),n=S.useRef(null),i=S.useRef(null),a=C(G(function(s){var c=i.current;c||y(),c.textContent=s}),[]),o=C(function(s){var c=n.current;c||y(),c.textContent=s},[]);j(function(){!n.current&&!i.current||y();var s=Lt(r),c=Lt(r);return n.current=s,i.current=c,s.setAttribute(me+"-always",e),c.setAttribute(me+"-dynamic",e),xr().appendChild(s),xr().appendChild(c),o(t.always),a(t.resting),function(){var v=function(g){var b=g.current;b||y(),xr().removeChild(b),g.current=null};v(n),v(i)}},[r,o,a,t.always,t.resting,e]);var l=C(function(){return a(t.dragging)},[a,t.dragging]),u=C(function(s){if(s==="DROP"){a(t.dropAnimating);return}a(t.userCancel)},[a,t.dropAnimating,t.userCancel]),d=C(function(){i.current&&a(t.resting)},[a,t.resting]),p=R(function(){return{dragging:l,dropping:u,resting:d}},[l,u,d]);return p}var Wn=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function ar(e){return e instanceof Wn(e).HTMLElement}function xl(e,r){var t="["+be.contextId+'="'+e+'"]',n=on(document.querySelectorAll(t));if(!n.length)return null;var i=oe(n,function(a){return a.getAttribute(be.draggableId)===r});return!i||!ar(i)?null:i}function Il(e){var r=S.useRef({}),t=S.useRef(null),n=S.useRef(null),i=S.useRef(!1),a=C(function(c,v){var f={id:c,focus:v};return r.current[c]=f,function(){var b=r.current,m=b[c];m!==f&&delete b[c]}},[]),o=C(function(c){var v=xl(e,c);v&&v!==document.activeElement&&v.focus()},[e]),l=C(function(c,v){t.current===c&&(t.current=v)},[]),u=C(function(){n.current||i.current&&(n.current=requestAnimationFrame(function(){n.current=null;var c=t.current;c&&o(c)}))},[o]),d=C(function(c){t.current=null;var v=document.activeElement;v&&v.getAttribute(be.draggableId)===c&&(t.current=c)},[]);j(function(){return i.current=!0,function(){i.current=!1;var c=n.current;c&&cancelAnimationFrame(c)}},[]);var p=R(function(){return{register:a,tryRecordFocus:d,tryRestoreFocusRecorded:u,tryShiftRecord:l}},[a,d,u,l]);return p}function Sl(){var e={draggables:{},droppables:{}},r=[];function t(s){return r.push(s),function(){var v=r.indexOf(s);v!==-1&&r.splice(v,1)}}function n(s){r.length&&r.forEach(function(c){return c(s)})}function i(s){return e.draggables[s]||null}function a(s){var c=i(s);return c||y(),c}var o={register:function(c){e.draggables[c.descriptor.id]=c,n({type:"ADDITION",value:c})},update:function(c,v){var f=e.draggables[v.descriptor.id];f&&f.uniqueId===c.uniqueId&&(delete e.draggables[v.descriptor.id],e.draggables[c.descriptor.id]=c)},unregister:function(c){var v=c.descriptor.id,f=i(v);f&&c.uniqueId===f.uniqueId&&(delete e.draggables[v],n({type:"REMOVAL",value:c}))},getById:a,findById:i,exists:function(c){return!!i(c)},getAllByType:function(c){return Xe(e.draggables).filter(function(v){return v.descriptor.type===c})}};function l(s){return e.droppables[s]||null}function u(s){var c=l(s);return c||y(),c}var d={register:function(c){e.droppables[c.descriptor.id]=c},unregister:function(c){var v=l(c.descriptor.id);v&&c.uniqueId===v.uniqueId&&delete e.droppables[c.descriptor.id]},getById:u,findById:l,exists:function(c){return!!l(c)},getAllByType:function(c){return Xe(e.droppables).filter(function(v){return v.descriptor.type===c})}};function p(){e.draggables={},e.droppables={},r.length=0}return{draggable:o,droppable:d,subscribe:t,clean:p}}function Cl(){var e=R(Sl,[]);return S.useEffect(function(){return function(){requestAnimationFrame(e.clean)}},[e]),e}var Xr=F.createContext(null),Ze=function(){var e=document.body;return e||y(),e},wl={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},El=function(r){return"rbd-announcement-"+r};function Pl(e){var r=R(function(){return El(e)},[e]),t=S.useRef(null);S.useEffect(function(){var a=document.createElement("div");return t.current=a,a.id=r,a.setAttribute("aria-live","assertive"),a.setAttribute("aria-atomic","true"),E(a.style,wl),Ze().appendChild(a),function(){setTimeout(function(){var u=Ze();u.contains(a)&&u.removeChild(a),a===t.current&&(t.current=null)})}},[r]);var n=C(function(i){var a=t.current;if(a){a.textContent=i;return}},[]);return n}var Al=0,Rl={separator:"::"};function Qr(e,r){return r===void 0&&(r=Rl),R(function(){return""+e+r.separator+Al++},[r.separator,e])}function Bl(e){var r=e.contextId,t=e.uniqueId;return"rbd-hidden-text-"+r+"-"+t}function Ol(e){var r=e.contextId,t=e.text,n=Qr("hidden-text",{separator:"-"}),i=R(function(){return Bl({contextId:r,uniqueId:n})},[n,r]);return S.useEffect(function(){var o=document.createElement("div");return o.id=i,o.textContent=t,o.style.display="none",Ze().appendChild(o),function(){var u=Ze();u.contains(o)&&u.removeChild(o)}},[i,t]),i}var ir=F.createContext(null);function Un(e){var r=S.useRef(e);return S.useEffect(function(){r.current=e}),r}function Nl(){var e=null;function r(){return!!e}function t(o){return o===e}function n(o){e&&y();var l={abandon:o};return e=l,l}function i(){e||y(),e=null}function a(){e&&(e.abandon(),i())}return{isClaimed:r,isActive:t,claim:n,release:i,tryAbandon:a}}var Tl=9,Ml=13,Zr=27,Hn=32,Ll=33,Fl=34,Gl=35,Wl=36,Ul=37,Hl=38,kl=39,ql=40,ke,Vl=(ke={},ke[Ml]=!0,ke[Tl]=!0,ke),kn=function(e){Vl[e.keyCode]&&e.preventDefault()},or=function(){var e="visibilitychange";if(typeof document>"u")return e;var r=[e,"ms"+e,"webkit"+e,"moz"+e,"o"+e],t=oe(r,function(n){return"on"+n in document});return t||e}(),qn=0,Ft=5;function $l(e,r){return Math.abs(r.x-e.x)>=Ft||Math.abs(r.y-e.y)>=Ft}var Gt={type:"IDLE"};function zl(e){var r=e.cancel,t=e.completed,n=e.getPhase,i=e.setPhase;return[{eventName:"mousemove",fn:function(o){var l=o.button,u=o.clientX,d=o.clientY;if(l===qn){var p={x:u,y:d},s=n();if(s.type==="DRAGGING"){o.preventDefault(),s.actions.move(p);return}s.type!=="PENDING"&&y();var c=s.point;if($l(c,p)){o.preventDefault();var v=s.actions.fluidLift(p);i({type:"DRAGGING",actions:v})}}}},{eventName:"mouseup",fn:function(o){var l=n();if(l.type!=="DRAGGING"){r();return}o.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:function(o){n().type==="DRAGGING"&&o.preventDefault(),r()}},{eventName:"keydown",fn:function(o){var l=n();if(l.type==="PENDING"){r();return}if(o.keyCode===Zr){o.preventDefault(),r();return}kn(o)}},{eventName:"resize",fn:r},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){n().type==="PENDING"&&r()}},{eventName:"webkitmouseforcedown",fn:function(o){var l=n();if(l.type==="IDLE"&&y(),l.actions.shouldRespectForcePress()){r();return}o.preventDefault()}},{eventName:or,fn:r}]}function jl(e){var r=S.useRef(Gt),t=S.useRef(te),n=R(function(){return{eventName:"mousedown",fn:function(s){if(!s.defaultPrevented&&s.button===qn&&!(s.ctrlKey||s.metaKey||s.shiftKey||s.altKey)){var c=e.findClosestDraggableId(s);if(c){var v=e.tryGetLock(c,o,{sourceEvent:s});if(v){s.preventDefault();var f={x:s.clientX,y:s.clientY};t.current(),d(v,f)}}}}}},[e]),i=R(function(){return{eventName:"webkitmouseforcewillbegin",fn:function(s){if(!s.defaultPrevented){var c=e.findClosestDraggableId(s);if(c){var v=e.findOptionsForDraggable(c);v&&(v.shouldRespectForcePress||e.canGetLock(c)&&s.preventDefault())}}}}},[e]),a=C(function(){var s={passive:!1,capture:!0};t.current=X(window,[i,n],s)},[i,n]),o=C(function(){var p=r.current;p.type!=="IDLE"&&(r.current=Gt,t.current(),a())},[a]),l=C(function(){var p=r.current;o(),p.type==="DRAGGING"&&p.actions.cancel({shouldBlockNextClick:!0}),p.type==="PENDING"&&p.actions.abort()},[o]),u=C(function(){var s={capture:!0,passive:!1},c=zl({cancel:l,completed:o,getPhase:function(){return r.current},setPhase:function(f){r.current=f}});t.current=X(window,c,s)},[l,o]),d=C(function(s,c){r.current.type!=="IDLE"&&y(),r.current={type:"PENDING",point:c,actions:s},u()},[u]);j(function(){return a(),function(){t.current()}},[a])}var fe;function Yl(){}var Kl=(fe={},fe[Fl]=!0,fe[Ll]=!0,fe[Wl]=!0,fe[Gl]=!0,fe);function Jl(e,r){function t(){r(),e.cancel()}function n(){r(),e.drop()}return[{eventName:"keydown",fn:function(a){if(a.keyCode===Zr){a.preventDefault(),t();return}if(a.keyCode===Hn){a.preventDefault(),n();return}if(a.keyCode===ql){a.preventDefault(),e.moveDown();return}if(a.keyCode===Hl){a.preventDefault(),e.moveUp();return}if(a.keyCode===kl){a.preventDefault(),e.moveRight();return}if(a.keyCode===Ul){a.preventDefault(),e.moveLeft();return}if(Kl[a.keyCode]){a.preventDefault();return}kn(a)}},{eventName:"mousedown",fn:t},{eventName:"mouseup",fn:t},{eventName:"click",fn:t},{eventName:"touchstart",fn:t},{eventName:"resize",fn:t},{eventName:"wheel",fn:t,options:{passive:!0}},{eventName:or,fn:t}]}function Xl(e){var r=S.useRef(Yl),t=R(function(){return{eventName:"keydown",fn:function(a){if(a.defaultPrevented||a.keyCode!==Hn)return;var o=e.findClosestDraggableId(a);if(!o)return;var l=e.tryGetLock(o,p,{sourceEvent:a});if(!l)return;a.preventDefault();var u=!0,d=l.snapLift();r.current();function p(){u||y(),u=!1,r.current(),n()}r.current=X(window,Jl(d,p),{capture:!0,passive:!1})}}},[e]),n=C(function(){var a={passive:!1,capture:!0};r.current=X(window,[t],a)},[t]);j(function(){return n(),function(){r.current()}},[n])}var Ir={type:"IDLE"},Ql=120,Zl=.15;function _l(e){var r=e.cancel,t=e.getPhase;return[{eventName:"orientationchange",fn:r},{eventName:"resize",fn:r},{eventName:"contextmenu",fn:function(i){i.preventDefault()}},{eventName:"keydown",fn:function(i){if(t().type!=="DRAGGING"){r();return}i.keyCode===Zr&&i.preventDefault(),r()}},{eventName:or,fn:r}]}function eu(e){var r=e.cancel,t=e.completed,n=e.getPhase;return[{eventName:"touchmove",options:{capture:!1},fn:function(a){var o=n();if(o.type!=="DRAGGING"){r();return}o.hasMoved=!0;var l=a.touches[0],u=l.clientX,d=l.clientY,p={x:u,y:d};a.preventDefault(),o.actions.move(p)}},{eventName:"touchend",fn:function(a){var o=n();if(o.type!=="DRAGGING"){r();return}a.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:function(a){if(n().type!=="DRAGGING"){r();return}a.preventDefault(),r()}},{eventName:"touchforcechange",fn:function(a){var o=n();o.type==="IDLE"&&y();var l=a.touches[0];if(l){var u=l.force>=Zl;if(u){var d=o.actions.shouldRespectForcePress();if(o.type==="PENDING"){d&&r();return}if(d){if(o.hasMoved){a.preventDefault();return}r();return}a.preventDefault()}}}},{eventName:or,fn:r}]}function ru(e){var r=S.useRef(Ir),t=S.useRef(te),n=C(function(){return r.current},[]),i=C(function(v){r.current=v},[]),a=R(function(){return{eventName:"touchstart",fn:function(v){if(!v.defaultPrevented){var f=e.findClosestDraggableId(v);if(f){var g=e.tryGetLock(f,l,{sourceEvent:v});if(g){var b=v.touches[0],m=b.clientX,h=b.clientY,x={x:m,y:h};t.current(),s(g,x)}}}}}},[e]),o=C(function(){var v={capture:!0,passive:!1};t.current=X(window,[a],v)},[a]),l=C(function(){var c=r.current;c.type!=="IDLE"&&(c.type==="PENDING"&&clearTimeout(c.longPressTimerId),i(Ir),t.current(),o())},[o,i]),u=C(function(){var c=r.current;l(),c.type==="DRAGGING"&&c.actions.cancel({shouldBlockNextClick:!0}),c.type==="PENDING"&&c.actions.abort()},[l]),d=C(function(){var v={capture:!0,passive:!1},f={cancel:u,completed:l,getPhase:n},g=X(window,eu(f),v),b=X(window,_l(f),v);t.current=function(){g(),b()}},[u,n,l]),p=C(function(){var v=n();v.type!=="PENDING"&&y();var f=v.actions.fluidLift(v.point);i({type:"DRAGGING",actions:f,hasMoved:!1})},[n,i]),s=C(function(v,f){n().type!=="IDLE"&&y();var g=setTimeout(p,Ql);i({type:"PENDING",point:f,actions:v,longPressTimerId:g}),d()},[d,n,i,p]);j(function(){return o(),function(){t.current();var f=n();f.type==="PENDING"&&(clearTimeout(f.longPressTimerId),i(Ir))}},[n,o,i]),j(function(){var v=X(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}]);return v},[])}var tu={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function Vn(e,r){if(r==null)return!1;var t=!!tu[r.tagName.toLowerCase()];if(t)return!0;var n=r.getAttribute("contenteditable");return n==="true"||n===""?!0:r===e?!1:Vn(e,r.parentElement)}function nu(e,r){var t=r.target;return ar(t)?Vn(e,t):!1}var au=function(e){return _(e.getBoundingClientRect()).center};function iu(e){return e instanceof Wn(e).Element}var ou=function(){var e="matches";if(typeof document>"u")return e;var r=[e,"msMatchesSelector","webkitMatchesSelector"],t=oe(r,function(n){return n in Element.prototype});return t||e}();function $n(e,r){return e==null?null:e[ou](r)?e:$n(e.parentElement,r)}function lu(e,r){return e.closest?e.closest(r):$n(e,r)}function uu(e){return"["+be.contextId+'="'+e+'"]'}function su(e,r){var t=r.target;if(!iu(t))return null;var n=uu(e),i=lu(t,n);return!i||!ar(i)?null:i}function cu(e,r){var t=su(e,r);return t?t.getAttribute(be.draggableId):null}function du(e,r){var t="["+Rr.contextId+'="'+e+'"]',n=on(document.querySelectorAll(t)),i=oe(n,function(a){return a.getAttribute(Rr.id)===r});return!i||!ar(i)?null:i}function pu(e){e.preventDefault()}function qe(e){var r=e.expected,t=e.phase,n=e.isLockActive;return e.shouldWarn,!(!n()||r!==t)}function zn(e){var r=e.lockAPI,t=e.store,n=e.registry,i=e.draggableId;if(r.isClaimed())return!1;var a=n.draggable.findById(i);return!(!a||!a.options.isEnabled||!Mn(t.getState(),i))}function vu(e){var r=e.lockAPI,t=e.contextId,n=e.store,i=e.registry,a=e.draggableId,o=e.forceSensorStop,l=e.sourceEvent,u=zn({lockAPI:r,store:n,registry:i,draggableId:a});if(!u)return null;var d=i.draggable.getById(a),p=du(t,d.descriptor.id);if(!p||l&&!d.options.canDragInteractiveElements&&nu(p,l))return null;var s=r.claim(o||te),c="PRE_DRAG";function v(){return d.options.shouldRespectForcePress}function f(){return r.isActive(s)}function g(w,B){qe({expected:w,phase:c,isLockActive:f,shouldWarn:!0})&&n.dispatch(B())}var b=g.bind(null,"DRAGGING");function m(w){function B(){r.release(),c="COMPLETED"}c!=="PRE_DRAG"&&(B(),c!=="PRE_DRAG"&&y()),n.dispatch(io(w.liftActionArgs)),c="DRAGGING";function T(A,M){if(M===void 0&&(M={shouldBlockNextClick:!1}),w.cleanup(),M.shouldBlockNextClick){var O=X(window,[{eventName:"click",fn:pu,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(O)}B(),n.dispatch(Pn({reason:A}))}return E({isActive:function(){return qe({expected:"DRAGGING",phase:c,isLockActive:f,shouldWarn:!1})},shouldRespectForcePress:v,drop:function(M){return T("DROP",M)},cancel:function(M){return T("CANCEL",M)}},w.actions)}function h(w){var B=Ae(function(A){b(function(){return En({client:A})})}),T=m({liftActionArgs:{id:a,clientSelection:w,movementMode:"FLUID"},cleanup:function(){return B.cancel()},actions:{move:B}});return E({},T,{move:B})}function x(){var w={moveUp:function(){return b(go)},moveRight:function(){return b(bo)},moveDown:function(){return b(mo)},moveLeft:function(){return b(ho)}};return m({liftActionArgs:{id:a,clientSelection:au(p),movementMode:"SNAP"},cleanup:te,actions:w})}function D(){var w=qe({expected:"PRE_DRAG",phase:c,isLockActive:f,shouldWarn:!0});w&&r.release()}var P={isActive:function(){return qe({expected:"PRE_DRAG",phase:c,isLockActive:f,shouldWarn:!1})},shouldRespectForcePress:v,fluidLift:h,snapLift:x,abort:D};return P}var fu=[jl,Xl,ru];function gu(e){var r=e.contextId,t=e.store,n=e.registry,i=e.customSensors,a=e.enableDefaultSensors,o=[].concat(a?fu:[],i||[]),l=S.useState(function(){return Nl()})[0],u=C(function(h,x){h.isDragging&&!x.isDragging&&l.tryAbandon()},[l]);j(function(){var h=t.getState(),x=t.subscribe(function(){var D=t.getState();u(h,D),h=D});return x},[l,t,u]),j(function(){return l.tryAbandon},[l.tryAbandon]);for(var d=C(function(m){return zn({lockAPI:l,registry:n,store:t,draggableId:m})},[l,n,t]),p=C(function(m,h,x){return vu({lockAPI:l,registry:n,contextId:r,store:t,draggableId:m,forceSensorStop:h,sourceEvent:x&&x.sourceEvent?x.sourceEvent:null})},[r,l,n,t]),s=C(function(m){return cu(r,m)},[r]),c=C(function(m){var h=n.draggable.findById(m);return h?h.options:null},[n.draggable]),v=C(function(){l.isClaimed()&&(l.tryAbandon(),t.getState().phase!=="IDLE"&&t.dispatch(qr()))},[l,t]),f=C(l.isClaimed,[l]),g=R(function(){return{canGetLock:d,tryGetLock:p,findClosestDraggableId:s,findOptionsForDraggable:c,tryReleaseLock:v,isLockClaimed:f}},[d,p,s,c,v,f]),b=0;b<o.length;b++)o[b](g)}var mu=function(r){return{onBeforeCapture:r.onBeforeCapture,onBeforeDragStart:r.onBeforeDragStart,onDragStart:r.onDragStart,onDragEnd:r.onDragEnd,onDragUpdate:r.onDragUpdate}};function we(e){return e.current||y(),e.current}function bu(e){var r=e.contextId,t=e.setCallbacks,n=e.sensors,i=e.nonce,a=e.dragHandleUsageInstructions,o=S.useRef(null),l=Un(e),u=C(function(){return mu(l.current)},[l]),d=Pl(r),p=Ol({contextId:r,text:a}),s=Dl(r,i),c=C(function(A){we(o).dispatch(A)},[]),v=R(function(){return ut({publishWhileDragging:lo,updateDroppableScroll:so,updateDroppableIsEnabled:co,updateDroppableIsCombineEnabled:po,collectionStarting:uo},c)},[c]),f=Cl(),g=R(function(){return Zo(f,v)},[f,v]),b=R(function(){return gl(E({scrollWindow:_o,scrollDroppable:g.scrollDroppable},ut({move:En},c)))},[g.scrollDroppable,c]),m=Il(r),h=R(function(){return Ko({announce:d,autoScroller:b,dimensionMarshal:g,focusMarshal:m,getResponders:u,styleMarshal:s})},[d,b,g,m,u,s]);o.current=h;var x=C(function(){var A=we(o),M=A.getState();M.phase!=="IDLE"&&A.dispatch(qr())},[]),D=C(function(){var A=we(o).getState();return A.isDragging||A.phase==="DROP_ANIMATING"},[]),P=R(function(){return{isDragging:D,tryAbort:x}},[D,x]);t(P);var w=C(function(A){return Mn(we(o).getState(),A)},[]),B=C(function(){return se(we(o).getState())},[]),T=R(function(){return{marshal:g,focus:m,contextId:r,canLift:w,isMovementAllowed:B,dragHandleUsageInstructionsId:p,registry:f}},[r,g,p,m,w,B,f]);return gu({contextId:r,store:h,registry:f,customSensors:n,enableDefaultSensors:e.enableDefaultSensors!==!1}),S.useEffect(function(){return x},[x]),F.createElement(ir.Provider,{value:T},F.createElement(oa,{context:Xr,store:h},e.children))}var hu=0;function yu(){return R(function(){return""+hu++},[])}function ds(e){var r=yu(),t=e.dragHandleUsageInstructions||$e.dragHandleUsageInstructions;return F.createElement(Ja,null,function(n){return F.createElement(bu,{nonce:e.nonce,contextId:r,setCallbacks:n,dragHandleUsageInstructions:t,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd},e.children)})}var jn=function(r){return function(t){return r===t}},Du=jn("scroll"),xu=jn("auto"),Wt=function(r,t){return t(r.overflowX)||t(r.overflowY)},Iu=function(r){var t=window.getComputedStyle(r),n={overflowX:t.overflowX,overflowY:t.overflowY};return Wt(n,Du)||Wt(n,xu)},Su=function(){return!1},Cu=function e(r){return r==null?null:r===document.body?Su()?r:null:r===document.documentElement?null:Iu(r)?r:e(r.parentElement)},Br=function(e){return{x:e.scrollLeft,y:e.scrollTop}},wu=function e(r){if(!r)return!1;var t=window.getComputedStyle(r);return t.position==="fixed"?!0:e(r.parentElement)},Eu=function(e){var r=Cu(e),t=wu(e);return{closestScrollable:r,isFixedOnPage:t}},Pu=function(e){var r=e.descriptor,t=e.isEnabled,n=e.isCombineEnabled,i=e.isFixedOnPage,a=e.direction,o=e.client,l=e.page,u=e.closest,d=function(){if(!u)return null;var v=u.scrollSize,f=u.client,g=On({scrollHeight:v.scrollHeight,scrollWidth:v.scrollWidth,height:f.paddingBox.height,width:f.paddingBox.width});return{pageMarginBox:u.page.marginBox,frameClient:f,scrollSize:v,shouldClipSubject:u.shouldClipSubject,scroll:{initial:u.scroll,current:u.scroll,max:g,diff:{value:W,displacement:W}}}}(),p=a==="vertical"?Gr:dn,s=ge({page:l,withPlaceholder:null,axis:p,frame:d}),c={descriptor:r,isCombineEnabled:n,isFixedOnPage:i,axis:p,isEnabled:t,client:o,page:l,frame:d,subject:s};return c},Au=function(r,t){var n=en(r);if(!t||r!==t)return n;var i=n.paddingBox.top-t.scrollTop,a=n.paddingBox.left-t.scrollLeft,o=i+t.scrollHeight,l=a+t.scrollWidth,u={top:i,right:l,bottom:o,left:a},d=Nr(u,n.border),p=Tr({borderBox:d,margin:n.margin,border:n.border,padding:n.padding});return p},Ru=function(e){var r=e.ref,t=e.descriptor,n=e.env,i=e.windowScroll,a=e.direction,o=e.isDropDisabled,l=e.isCombineEnabled,u=e.shouldClipSubject,d=n.closestScrollable,p=Au(r,d),s=Ye(p,i),c=function(){if(!d)return null;var f=en(d),g={scrollHeight:d.scrollHeight,scrollWidth:d.scrollWidth};return{client:f,page:Ye(f,i),scroll:Br(d),scrollSize:g,shouldClipSubject:u}}(),v=Pu({descriptor:t,isEnabled:!o,isCombineEnabled:l,isFixedOnPage:n.isFixedOnPage,direction:a,client:p,page:s,closest:c});return v},Bu={passive:!1},Ou={passive:!0},Ut=function(e){return e.shouldPublishImmediately?Bu:Ou};function _e(e){var r=S.useContext(e);return r||y(),r}var Ve=function(r){return r&&r.env.closestScrollable||null};function Nu(e){var r=S.useRef(null),t=_e(ir),n=Qr("droppable"),i=t.registry,a=t.marshal,o=Un(e),l=R(function(){return{id:e.droppableId,type:e.type,mode:e.mode}},[e.droppableId,e.mode,e.type]),u=S.useRef(l),d=R(function(){return G(function(D,P){r.current||y();var w={x:D,y:P};a.updateDroppableScroll(l.id,w)})},[l.id,a]),p=C(function(){var D=r.current;return!D||!D.env.closestScrollable?W:Br(D.env.closestScrollable)},[]),s=C(function(){var D=p();d(D.x,D.y)},[p,d]),c=R(function(){return Ae(s)},[s]),v=C(function(){var D=r.current,P=Ve(D);D&&P||y();var w=D.scrollOptions;if(w.shouldPublishImmediately){s();return}c()},[c,s]),f=C(function(D,P){r.current&&y();var w=o.current,B=w.getDroppableRef();B||y();var T=Eu(B),A={ref:B,descriptor:l,env:T,scrollOptions:P};r.current=A;var M=Ru({ref:B,descriptor:l,env:T,windowScroll:D,direction:w.direction,isDropDisabled:w.isDropDisabled,isCombineEnabled:w.isCombineEnabled,shouldClipSubject:!w.ignoreContainerClipping}),O=T.closestScrollable;return O&&(O.setAttribute(Mt.contextId,t.contextId),O.addEventListener("scroll",v,Ut(A.scrollOptions))),M},[t.contextId,l,v,o]),g=C(function(){var D=r.current,P=Ve(D);return D&&P||y(),Br(P)},[]),b=C(function(){var D=r.current;D||y();var P=Ve(D);r.current=null,P&&(c.cancel(),P.removeAttribute(Mt.contextId),P.removeEventListener("scroll",v,Ut(D.scrollOptions)))},[v,c]),m=C(function(D){var P=r.current;P||y();var w=Ve(P);w||y(),w.scrollTop+=D.y,w.scrollLeft+=D.x},[]),h=R(function(){return{getDimensionAndWatchScroll:f,getScrollWhileDragging:g,dragStopped:b,scroll:m}},[b,f,g,m]),x=R(function(){return{uniqueId:n,descriptor:l,callbacks:h}},[h,l,n]);j(function(){return u.current=x.descriptor,i.droppable.register(x),function(){r.current&&b(),i.droppable.unregister(x)}},[h,l,b,x,a,i.droppable]),j(function(){r.current&&a.updateDroppableIsEnabled(u.current.id,!e.isDropDisabled)},[e.isDropDisabled,a]),j(function(){r.current&&a.updateDroppableIsCombineEnabled(u.current.id,e.isCombineEnabled)},[e.isCombineEnabled,a])}function Sr(){}var Ht={width:0,height:0,margin:ri},Tu=function(r){var t=r.isAnimatingOpenOnMount,n=r.placeholder,i=r.animate;return t||i==="close"?Ht:{height:n.client.borderBox.height,width:n.client.borderBox.width,margin:n.client.margin}},Mu=function(r){var t=r.isAnimatingOpenOnMount,n=r.placeholder,i=r.animate,a=Tu({isAnimatingOpenOnMount:t,placeholder:n,animate:i});return{display:n.display,boxSizing:"border-box",width:a.width,height:a.height,marginTop:a.margin.top,marginRight:a.margin.right,marginBottom:a.margin.bottom,marginLeft:a.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:i!=="none"?Pe.placeholder:null}};function Lu(e){var r=S.useRef(null),t=C(function(){r.current&&(clearTimeout(r.current),r.current=null)},[]),n=e.animate,i=e.onTransitionEnd,a=e.onClose,o=e.contextId,l=S.useState(e.animate==="open"),u=l[0],d=l[1];S.useEffect(function(){return u?n!=="open"?(t(),d(!1),Sr):r.current?Sr:(r.current=setTimeout(function(){r.current=null,d(!1)}),t):Sr},[n,u,t]);var p=C(function(c){c.propertyName==="height"&&(i(),n==="close"&&a())},[n,a,i]),s=Mu({isAnimatingOpenOnMount:u,animate:e.animate,placeholder:e.placeholder});return F.createElement(e.placeholder.tagName,{style:s,"data-rbd-placeholder-context-id":o,onTransitionEnd:p,ref:e.innerRef})}var Fu=F.memo(Lu),_r=F.createContext(null),Gu=function(e){Vt(r,e);function r(){for(var n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return n=e.call.apply(e,[this].concat(a))||this,n.state={isVisible:!!n.props.on,data:n.props.on,animate:n.props.shouldAnimate&&n.props.on?"open":"none"},n.onClose=function(){n.state.animate==="close"&&n.setState({isVisible:!1})},n}r.getDerivedStateFromProps=function(i,a){return i.shouldAnimate?i.on?{isVisible:!0,data:i.on,animate:"open"}:a.isVisible?{isVisible:!0,data:a.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!i.on,data:i.on,animate:"none"}};var t=r.prototype;return t.render=function(){if(!this.state.isVisible)return null;var i={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(i)},r}(F.PureComponent),kt={dragging:5e3,dropAnimating:4500},Wu=function(r,t){return t?Pe.drop(t.duration):r?Pe.snap:Pe.fluid},Uu=function(r,t){return r?t?Ne.opacity.drop:Ne.opacity.combining:null},Hu=function(r){return r.forceShouldAnimate!=null?r.forceShouldAnimate:r.mode==="SNAP"};function ku(e){var r=e.dimension,t=r.client,n=e.offset,i=e.combineWith,a=e.dropping,o=!!i,l=Hu(e),u=!!a,d=u?Pr.drop(n,o):Pr.moveTo(n),p={position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:Wu(l,a),transform:d,opacity:Uu(o,u),zIndex:u?kt.dropAnimating:kt.dragging,pointerEvents:"none"};return p}function qu(e){return{transform:Pr.moveTo(e.offset),transition:e.shouldAnimateDisplacement?null:"none"}}function Vu(e){return e.type==="DRAGGING"?ku(e):qu(e)}function $u(e,r,t){t===void 0&&(t=W);var n=window.getComputedStyle(r),i=r.getBoundingClientRect(),a=_t(i,n),o=Ye(a,t),l={client:a,tagName:r.tagName.toLowerCase(),display:n.display},u={x:a.marginBox.width,y:a.marginBox.height},d={descriptor:e,placeholder:l,displaceBy:u,client:a,page:o};return d}function zu(e){var r=Qr("draggable"),t=e.descriptor,n=e.registry,i=e.getDraggableRef,a=e.canDragInteractiveElements,o=e.shouldRespectForcePress,l=e.isEnabled,u=R(function(){return{canDragInteractiveElements:a,shouldRespectForcePress:o,isEnabled:l}},[a,l,o]),d=C(function(v){var f=i();return f||y(),$u(t,f,v)},[t,i]),p=R(function(){return{uniqueId:r,descriptor:t,options:u,getDimension:d}},[t,d,u,r]),s=S.useRef(p),c=S.useRef(!0);j(function(){return n.draggable.register(s.current),function(){return n.draggable.unregister(s.current)}},[n.draggable]),j(function(){if(c.current){c.current=!1;return}var v=s.current;s.current=p,n.draggable.update(p,v)},[p,n.draggable])}function ju(e){e.preventDefault()}function Yu(e){var r=S.useRef(null),t=C(function(A){r.current=A},[]),n=C(function(){return r.current},[]),i=_e(ir),a=i.contextId,o=i.dragHandleUsageInstructionsId,l=i.registry,u=_e(_r),d=u.type,p=u.droppableId,s=R(function(){return{id:e.draggableId,index:e.index,type:d,droppableId:p}},[e.draggableId,e.index,d,p]),c=e.children,v=e.draggableId,f=e.isEnabled,g=e.shouldRespectForcePress,b=e.canDragInteractiveElements,m=e.isClone,h=e.mapped,x=e.dropAnimationFinished;if(!m){var D=R(function(){return{descriptor:s,registry:l,getDraggableRef:n,canDragInteractiveElements:b,shouldRespectForcePress:g,isEnabled:f}},[s,l,n,b,g,f]);zu(D)}var P=R(function(){return f?{tabIndex:0,role:"button","aria-describedby":o,"data-rbd-drag-handle-draggable-id":v,"data-rbd-drag-handle-context-id":a,draggable:!1,onDragStart:ju}:null},[a,o,v,f]),w=C(function(A){h.type==="DRAGGING"&&h.dropping&&A.propertyName==="transform"&&x()},[x,h]),B=R(function(){var A=Vu(h),M=h.type==="DRAGGING"&&h.dropping?w:null,O={innerRef:t,draggableProps:{"data-rbd-draggable-context-id":a,"data-rbd-draggable-id":v,style:A,onTransitionEnd:M},dragHandleProps:P};return O},[a,P,v,h,w,t]),T=R(function(){return{draggableId:s.id,type:s.type,source:{index:s.index,droppableId:s.droppableId}}},[s.droppableId,s.id,s.index,s.type]);return c(B,h.snapshot,T)}var Yn=function(e,r){return e===r},Kn=function(e){var r=e.combine,t=e.destination;return t?t.droppableId:r?r.droppableId:null},Ku=function(r){return r.combine?r.combine.draggableId:null},Ju=function(r){return r.at&&r.at.type==="COMBINE"?r.at.combine.draggableId:null};function Xu(){var e=G(function(i,a){return{x:i,y:a}}),r=G(function(i,a,o,l,u){return{isDragging:!0,isClone:a,isDropAnimating:!!u,dropAnimation:u,mode:i,draggingOver:o,combineWith:l,combineTargetFor:null}}),t=G(function(i,a,o,l,u,d,p){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:u,combineWith:d,mode:a,offset:i,dimension:o,forceShouldAnimate:p,snapshot:r(a,l,u,d,null)}}}),n=function(a,o){if(a.isDragging){if(a.critical.draggable.id!==o.draggableId)return null;var l=a.current.client.offset,u=a.dimensions.draggables[o.draggableId],d=z(a.impact),p=Ju(a.impact),s=a.forceShouldAnimate;return t(e(l.x,l.y),a.movementMode,u,o.isClone,d,p,s)}if(a.phase==="DROP_ANIMATING"){var c=a.completed;if(c.result.draggableId!==o.draggableId)return null;var v=o.isClone,f=a.dimensions.draggables[o.draggableId],g=c.result,b=g.mode,m=Kn(g),h=Ku(g),x=a.dropDuration,D={duration:x,curve:$r.drop,moveTo:a.newHomeClientOffset,opacity:h?Ne.opacity.drop:null,scale:h?Ne.scale.drop:null};return{mapped:{type:"DRAGGING",offset:a.newHomeClientOffset,dimension:f,dropping:D,draggingOver:m,combineWith:h,mode:b,forceShouldAnimate:null,snapshot:r(b,v,m,h,D)}}}return null};return n}function Jn(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var Qu={mapped:{type:"SECONDARY",offset:W,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:Jn(null)}};function Zu(){var e=G(function(o,l){return{x:o,y:l}}),r=G(Jn),t=G(function(o,l,u){return l===void 0&&(l=null),{mapped:{type:"SECONDARY",offset:o,combineTargetFor:l,shouldAnimateDisplacement:u,snapshot:r(l)}}}),n=function(l){return l?t(W,l,!0):null},i=function(l,u,d,p){var s=d.displaced.visible[l],c=!!(p.inVirtualList&&p.effected[l]),v=rr(d),f=v&&v.draggableId===l?u:null;if(!s){if(!c)return n(f);if(d.displaced.invisible[l])return null;var g=he(p.displacedBy.point),b=e(g.x,g.y);return t(b,f,!0)}if(c)return n(f);var m=d.displacedBy.point,h=e(m.x,m.y);return t(h,f,s.shouldAnimate)},a=function(l,u){if(l.isDragging)return l.critical.draggable.id===u.draggableId?null:i(u.draggableId,l.critical.draggable.id,l.impact,l.afterCritical);if(l.phase==="DROP_ANIMATING"){var d=l.completed;return d.result.draggableId===u.draggableId?null:i(u.draggableId,d.result.draggableId,d.impact,d.afterCritical)}return null};return a}var _u=function(){var r=Xu(),t=Zu(),n=function(a,o){return r(a,o)||t(a,o)||Qu};return n},es={dropAnimationFinished:An},rs=Qt(_u,es,null,{context:Xr,pure:!0,areStatePropsEqual:Yn})(Yu);function Xn(e){var r=_e(_r),t=r.isUsingCloneFor;return t===e.draggableId&&!e.isClone?null:F.createElement(rs,e)}function ps(e){var r=typeof e.isDragDisabled=="boolean"?!e.isDragDisabled:!0,t=!!e.disableInteractiveElementBlocking,n=!!e.shouldRespectForcePress;return F.createElement(Xn,E({},e,{isClone:!1,isEnabled:r,canDragInteractiveElements:t,shouldRespectForcePress:n}))}function ts(e){var r=S.useContext(ir);r||y();var t=r.contextId,n=r.isMovementAllowed,i=S.useRef(null),a=S.useRef(null),o=e.children,l=e.droppableId,u=e.type,d=e.mode,p=e.direction,s=e.ignoreContainerClipping,c=e.isDropDisabled,v=e.isCombineEnabled,f=e.snapshot,g=e.useClone,b=e.updateViewportMaxScroll,m=e.getContainerForClone,h=C(function(){return i.current},[]),x=C(function(O){i.current=O},[]);C(function(){return a.current},[]);var D=C(function(O){a.current=O},[]),P=C(function(){n()&&b({maxScroll:Tn()})},[n,b]);Nu({droppableId:l,type:u,mode:d,direction:p,isDropDisabled:c,isCombineEnabled:v,ignoreContainerClipping:s,getDroppableRef:h});var w=F.createElement(Gu,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},function(O){var k=O.onClose,I=O.data,L=O.animate;return F.createElement(Fu,{placeholder:I,onClose:k,innerRef:D,animate:L,contextId:t,onTransitionEnd:P})}),B=R(function(){return{innerRef:x,placeholder:w,droppableProps:{"data-rbd-droppable-id":l,"data-rbd-droppable-context-id":t}}},[t,l,w,x]),T=g?g.dragging.draggableId:null,A=R(function(){return{droppableId:l,type:u,isUsingCloneFor:T}},[l,T,u]);function M(){if(!g)return null;var O=g.dragging,k=g.render,I=F.createElement(Xn,{draggableId:O.draggableId,index:O.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},function(L,Y){return k(L,Y,O)});return _n.createPortal(I,m())}return F.createElement(_r.Provider,{value:A},o(B,f),M())}var Cr=function(r,t){return r===t.droppable.type},qt=function(r,t){return t.draggables[r.draggable.id]},ns=function(){var r={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t=E({},r,{shouldAnimatePlaceholder:!1}),n=G(function(o){return{draggableId:o.id,type:o.type,source:{index:o.index,droppableId:o.droppableId}}}),i=G(function(o,l,u,d,p,s){var c=p.descriptor.id,v=p.descriptor.droppableId===o;if(v){var f=s?{render:s,dragging:n(p.descriptor)}:null,g={isDraggingOver:u,draggingOverWith:u?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!1,snapshot:g,useClone:f}}if(!l)return t;if(!d)return r;var b={isDraggingOver:u,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!0,snapshot:b,useClone:null}}),a=function(l,u){var d=u.droppableId,p=u.type,s=!u.isDropDisabled,c=u.renderClone;if(l.isDragging){var v=l.critical;if(!Cr(p,v))return t;var f=qt(v,l.dimensions),g=z(l.impact)===d;return i(d,s,g,g,f,c)}if(l.phase==="DROP_ANIMATING"){var b=l.completed;if(!Cr(p,b.critical))return t;var m=qt(b.critical,l.dimensions);return i(d,s,Kn(b.result)===d,z(b.impact)===d,m,c)}if(l.phase==="IDLE"&&l.completed&&!l.shouldFlush){var h=l.completed;if(!Cr(p,h.critical))return t;var x=z(h.impact)===d,D=!!(h.impact.at&&h.impact.at.type==="COMBINE"),P=h.critical.droppable.id===d;return x?D?r:t:P?r:t}return t};return a},as={updateViewportMaxScroll:fo};function is(){return document.body||y(),document.body}var os={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:is},ls=Qt(ns,as,null,{context:Xr,pure:!0,areStatePropsEqual:Yn})(ts);ls.defaultProps=os;export{ls as C,ds as D,ps as P};
