const { Client } = require('pg');
const { getDatabaseConfig } = require('../database/config/database-config');

async function checkVipMenuComponent() {
  try {
    console.log('🔍 检查VIP菜单组件配置...');
    
    const config = getDatabaseConfig('local');
    const client = new Client(config);
    await client.connect();

    // 检查VIP菜单的详细配置
    console.log('\n📋 VIP菜单详细配置:');
    const vipMenu = await client.query(`
      SELECT
        id,
        title,
        path,
        component,
        parent_id,
        permission_code,
        status,
        icon,
        type
      FROM sys_menus
      WHERE path LIKE '%vip%'
      ORDER BY id
    `);
    
    console.table(vipMenu.rows);

    // 检查配置管理父菜单
    console.log('\n📋 配置管理父菜单:');
    const configParent = await client.query(`
      SELECT 
        id, 
        title, 
        path, 
        component, 
        parent_id, 
        permission_code,
        status,
        type
      FROM sys_menus 
      WHERE path = '/config' OR title LIKE '%配置管理%'
      ORDER BY id
    `);
    
    console.table(configParent.rows);

    // 检查配置管理下的所有子菜单
    console.log('\n📋 配置管理子菜单:');
    const configChildren = await client.query(`
      SELECT 
        id, 
        title, 
        path, 
        component, 
        parent_id, 
        permission_code,
        status,
        type
      FROM sys_menus 
      WHERE parent_id IN (
        SELECT id FROM sys_menus WHERE path = '/config' OR title LIKE '%配置管理%'
      )
      ORDER BY id
    `);
    
    console.table(configChildren.rows);

    // 检查菜单层级结构
    console.log('\n🌳 菜单层级结构:');
    const menuHierarchy = await client.query(`
      WITH RECURSIVE menu_tree AS (
        -- 根菜单
        SELECT 
          id, 
          title, 
          path, 
          component,
          parent_id, 
          0 as level,
          CAST(title AS TEXT) as hierarchy
        FROM sys_menus 
        WHERE parent_id IS NULL AND status = 1
        
        UNION ALL
        
        -- 子菜单
        SELECT 
          m.id, 
          m.title, 
          m.path, 
          m.component,
          m.parent_id, 
          mt.level + 1,
          mt.hierarchy || ' -> ' || m.title
        FROM sys_menus m
        INNER JOIN menu_tree mt ON m.parent_id = mt.id
        WHERE m.status = 1
      )
      SELECT 
        level,
        hierarchy,
        path,
        component
      FROM menu_tree 
      WHERE hierarchy LIKE '%配置%' OR hierarchy LIKE '%VIP%'
      ORDER BY level, hierarchy
    `);
    
    console.table(menuHierarchy.rows);

    await client.end();
    
  } catch (error) {
    console.error('检查失败:', error.message);
    console.error(error.stack);
  }
}

checkVipMenuComponent();
