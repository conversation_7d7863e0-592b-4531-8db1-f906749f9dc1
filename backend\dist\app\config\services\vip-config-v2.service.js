"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipConfigV2Service = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const vip_config_v2_entity_1 = require("../entities/vip-config-v2.entity");
const app_user_entity_1 = require("../../entities/app-user.entity");
const vip_exp_service_1 = require("./vip-exp.service");
let VipConfigV2Service = class VipConfigV2Service {
    vipConfigRepository;
    appUserRepository;
    vipExpService;
    constructor(vipConfigRepository, appUserRepository, vipExpService) {
        this.vipConfigRepository = vipConfigRepository;
        this.appUserRepository = appUserRepository;
        this.vipExpService = vipExpService;
    }
    async create(createVipConfigDto, userId) {
        const existingConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: createVipConfigDto.vipLevel },
        });
        if (existingConfig) {
            throw new common_1.ConflictException(`VIP等级 ${createVipConfigDto.vipLevel} 已存在`);
        }
        await this.validateExpIncrement(createVipConfigDto.vipLevel, createVipConfigDto.requiredExp);
        this.validateBusinessRules(createVipConfigDto);
        const vipConfig = this.vipConfigRepository.create({
            ...createVipConfigDto,
            createdBy: userId,
            updatedBy: userId,
        });
        return await this.vipConfigRepository.save(vipConfig);
    }
    async findAll(query) {
        const { page = 1, pageSize = 10, vipLevel, status, levelName, buybackEnabled } = query;
        const queryBuilder = this.vipConfigRepository.createQueryBuilder('vip')
            .leftJoinAndSelect('vip.creator', 'creator')
            .leftJoinAndSelect('vip.updater', 'updater');
        if (vipLevel !== undefined) {
            queryBuilder.andWhere('vip.vipLevel = :vipLevel', { vipLevel });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('vip.status = :status', { status });
        }
        if (levelName) {
            queryBuilder.andWhere('vip.levelName LIKE :levelName', { levelName: `%${levelName}%` });
        }
        if (buybackEnabled !== undefined) {
            queryBuilder.andWhere('vip.buybackEnabled = :buybackEnabled', { buybackEnabled });
        }
        queryBuilder.orderBy('vip.vipLevel', 'ASC');
        const skip = (page - 1) * pageSize;
        queryBuilder.skip(skip).take(pageSize);
        const [list, total] = await queryBuilder.getManyAndCount();
        return {
            list,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        };
    }
    async findOne(id) {
        const vipConfig = await this.vipConfigRepository.findOne({
            where: { id },
            relations: ['creator', 'updater'],
        });
        if (!vipConfig) {
            throw new common_1.NotFoundException('VIP配置不存在');
        }
        return vipConfig;
    }
    async findByLevel(vipLevel) {
        const vipConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel, status: 1 },
        });
        if (!vipConfig) {
            throw new common_1.NotFoundException(`VIP等级 ${vipLevel} 配置不存在`);
        }
        return vipConfig;
    }
    async update(id, updateVipConfigDto, userId) {
        const existingConfig = await this.findOne(id);
        if (updateVipConfigDto.vipLevel !== undefined && updateVipConfigDto.vipLevel !== existingConfig.vipLevel) {
            const conflictConfig = await this.vipConfigRepository.findOne({
                where: { vipLevel: updateVipConfigDto.vipLevel },
            });
            if (conflictConfig) {
                throw new common_1.ConflictException(`VIP等级 ${updateVipConfigDto.vipLevel} 已存在`);
            }
        }
        if (updateVipConfigDto.requiredExp !== undefined) {
            await this.validateExpIncrement(updateVipConfigDto.vipLevel || existingConfig.vipLevel, updateVipConfigDto.requiredExp, id);
        }
        this.validateBusinessRules({ ...existingConfig, ...updateVipConfigDto });
        await this.vipConfigRepository.update(id, {
            ...updateVipConfigDto,
            updatedBy: userId,
        });
        return await this.findOne(id);
    }
    async remove(id) {
        const vipConfig = await this.findOne(id);
        const userCount = await this.appUserRepository.count({
            where: { vipLevel: vipConfig.vipLevel },
        });
        if (userCount > 0) {
            throw new common_1.BadRequestException(`无法删除VIP等级 ${vipConfig.vipLevel}，还有 ${userCount} 个用户正在使用此等级`);
        }
        await this.vipConfigRepository.remove(vipConfig);
        return { message: '删除成功' };
    }
    async getUserVipBenefits(userId) {
        return await this.vipExpService.getUserVipBenefits(userId);
    }
    async addUserExp(userId, expType, amount, description) {
        return await this.vipExpService.addExp(userId, expType, amount, description);
    }
    async recalculateAllUserVipLevels() {
        return await this.vipExpService.recalculateAllUserVipLevels();
    }
    async getVipLevelComparison() {
        const configs = await this.vipConfigRepository.find({
            where: { status: 1 },
            order: { vipLevel: 'ASC' },
        });
        return configs.map(config => ({
            vipLevel: config.vipLevel,
            levelName: config.levelName,
            strategicPosition: config.strategicPosition,
            requiredExp: config.requiredExp,
            dailyGoldReward: config.dailyGoldReward,
            buybackRateDisplay: config.getBuybackRateDisplay(),
            c2cFeeRate: `${config.c2cFeeRate}%`,
            rakebackRate: `${config.rakebackRate}%`,
            withdrawalFeeRate: `${config.withdrawalFeeRate}%`,
            withdrawalLimitDisplay: config.getWithdrawalLimitDisplay(),
            withdrawalPriorityDisplay: config.getWithdrawalPriorityDisplay(),
        }));
    }
    async validateExpIncrement(vipLevel, requiredExp, excludeId) {
        const lowerLevelConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: vipLevel - 1 },
        });
        const higherLevelConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: vipLevel + 1 },
        });
        if (lowerLevelConfig && requiredExp <= lowerLevelConfig.requiredExp) {
            throw new common_1.BadRequestException(`VIP等级 ${vipLevel} 所需EXP (${requiredExp}) 必须大于等级 ${vipLevel - 1} 的EXP (${lowerLevelConfig.requiredExp})`);
        }
        if (higherLevelConfig && requiredExp >= higherLevelConfig.requiredExp) {
            throw new common_1.BadRequestException(`VIP等级 ${vipLevel} 所需EXP (${requiredExp}) 必须小于等级 ${vipLevel + 1} 的EXP (${higherLevelConfig.requiredExp})`);
        }
    }
    validateBusinessRules(config) {
        if (config.dailyTaskExpMin > config.dailyTaskExpMax) {
            throw new common_1.BadRequestException('活跃任务最小EXP不能大于最大EXP');
        }
        if (config.buybackEnabled && !config.buybackRate) {
            throw new common_1.BadRequestException('开启回购功能时必须设置回购价格');
        }
        if (!config.buybackEnabled && config.buybackRate) {
            throw new common_1.BadRequestException('未开启回购功能时不应设置回购价格');
        }
        if (config.withdrawalPriority < 1 || config.withdrawalPriority > 3) {
            throw new common_1.BadRequestException('提现优先级必须在1-3之间');
        }
        if (config.taskUnlockRequired && config.taskUnlockGamesRequired <= 0) {
            throw new common_1.BadRequestException('需要解锁时必须设置大于0的游戏局数');
        }
    }
};
exports.VipConfigV2Service = VipConfigV2Service;
exports.VipConfigV2Service = VipConfigV2Service = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vip_config_v2_entity_1.VipConfigV2)),
    __param(1, (0, typeorm_1.InjectRepository)(app_user_entity_1.AppUser)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        vip_exp_service_1.VipExpService])
], VipConfigV2Service);
//# sourceMappingURL=vip-config-v2.service.js.map