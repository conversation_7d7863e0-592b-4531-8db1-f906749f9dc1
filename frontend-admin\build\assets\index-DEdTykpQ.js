import{j as e}from"./index-DD0gcXtR.js";import{a as i}from"./react-BUTTOX-3.js";import{B as z}from"./index-C1Do55qr.js";import{av as t,aq as G,ar as m,az as k,Q as d,d as s,u as X,aB as b,a$ as $,ak as B,b0 as W,M as j,a6 as f,ao as U,ap as R,I,q as l,S as Z,p as E,s as r,an as y,aM as A,aC as K}from"./antd-FyCAPQKa.js";const{TabPane:n}=f,{Option:u}=E,ae=()=>{const[g,v]=i.useState([]),[C,p]=i.useState(!1),[P,c]=i.useState(!1),[O,F]=i.useState(!1),[L,w]=i.useState(!1),[Q,q]=i.useState(null),[S,V]=i.useState([]),[o]=t.useForm(),[_]=t.useForm(),x=async()=>{p(!0);try{v([{id:1,vipLevel:0,levelName:"体验用户",strategicPosition:"体验用户/流量基础",requiredExp:0,realMoneyRechargeExpRatio:10,realMoneyFlowExpRatio:1,goldPaymentExpRatio:5,goldFlowExpRatio:.001,dailyTaskExpMin:10,dailyTaskExpMax:50,dailyGoldReward:0,buybackEnabled:!1,buybackRate:null,c2cFeeRate:15,rakebackRate:0,withdrawalFeeRate:0,dailyWithdrawalLimit:null,withdrawalPriority:1,taskUnlockRequired:!1,taskUnlockGamesRequired:0,status:1,remark:"新用户默认等级，无提现资格",createTime:"2025-06-29T10:00:00Z",updateTime:"2025-06-29T10:00:00Z"},{id:2,vipLevel:1,levelName:"活跃认证",strategicPosition:"活跃认证",requiredExp:500,realMoneyRechargeExpRatio:10,realMoneyFlowExpRatio:1,goldPaymentExpRatio:5,goldFlowExpRatio:.001,dailyTaskExpMin:10,dailyTaskExpMax:50,dailyGoldReward:50,buybackEnabled:!1,buybackRate:null,c2cFeeRate:15,rakebackRate:0,withdrawalFeeRate:5,dailyWithdrawalLimit:2e3,withdrawalPriority:1,taskUnlockRequired:!1,taskUnlockGamesRequired:0,status:1,remark:"活跃认证用户，开启基础提现权限",createTime:"2025-06-29T10:00:00Z",updateTime:"2025-06-29T10:00:00Z"}])}catch(a){r.error("获取VIP配置失败"),console.error("获取VIP配置失败:",a)}finally{p(!1)}},M=async()=>{try{V([{vipLevel:0,levelName:"体验用户",strategicPosition:"体验用户/流量基础",requiredExp:0,dailyGoldReward:0,buybackRateDisplay:"无资格",c2cFeeRate:"15%",rakebackRate:"0%",withdrawalFeeRate:"无资格",withdrawalLimitDisplay:"无资格",withdrawalPriorityDisplay:"普通"}])}catch{r.error("获取对比数据失败")}};i.useEffect(()=>{x()},[]);const T=[{title:"VIP等级",dataIndex:"vipLevel",key:"vipLevel",width:100,render:a=>e.jsxs(y,{color:"gold",icon:e.jsx(A,{}),children:["VIP",a]})},{title:"等级名称",dataIndex:"levelName",key:"levelName",width:120},{title:"战略定位",dataIndex:"strategicPosition",key:"strategicPosition",width:150,ellipsis:!0},{title:"所需EXP",dataIndex:"requiredExp",key:"requiredExp",width:120,render:a=>a.toLocaleString()},{title:"每日金币",dataIndex:"dailyGoldReward",key:"dailyGoldReward",width:100,render:a=>`${a.toLocaleString()}`},{title:"回购权益",key:"buyback",width:120,render:a=>e.jsxs(d,{direction:"vertical",size:"small",children:[e.jsx(y,{color:a.buybackEnabled?"green":"red",children:a.buybackEnabled?"已开启":"未开启"}),a.buybackEnabled&&a.buybackRate&&e.jsxs("span",{style:{fontSize:"12px"},children:[a.buybackRate,":1"]})]})},{title:"C2C费率",dataIndex:"c2cFeeRate",key:"c2cFeeRate",width:100,render:a=>`${a}%`},{title:"返水比例",dataIndex:"rakebackRate",key:"rakebackRate",width:100,render:a=>`${a}%`},{title:"提现权益",key:"withdrawal",width:150,render:a=>e.jsxs(d,{direction:"vertical",size:"small",children:[a.withdrawalFeeRate&&e.jsxs("span",{style:{fontSize:"12px"},children:["费率: ",a.withdrawalFeeRate,"%"]}),a.dailyWithdrawalLimit?e.jsxs("span",{style:{fontSize:"12px"},children:["限额: ₹",a.dailyWithdrawalLimit.toLocaleString()]}):e.jsx("span",{style:{fontSize:"12px"},children:"限额: 无上限"})]})},{title:"状态",dataIndex:"status",key:"status",width:80,render:a=>e.jsx(y,{color:a===1?"green":"red",children:a===1?"启用":"禁用"})},{title:"操作",key:"action",width:150,fixed:"right",render:(a,h)=>e.jsxs(d,{children:[e.jsx(s,{type:"primary",size:"small",icon:e.jsx(K,{}),onClick:()=>{q(h),o.setFieldsValue(h),c(!0)},children:"编辑"}),e.jsx(s,{size:"small",icon:e.jsx(b,{}),onClick:()=>{},children:"详情"})]})}],N=async a=>{try{r.success("更新成功"),c(!1),x()}catch{r.error("更新失败")}},D=async()=>{try{r.success("重新计算完成")}catch{r.error("重新计算失败")}};return e.jsxs(z,{children:[e.jsxs(G,{title:e.jsxs(d,{children:[e.jsx(W,{}),"VIP经济权益配置管理 V12.0"]}),extra:e.jsxs(d,{children:[e.jsx(s,{type:"primary",icon:e.jsx(X,{}),onClick:()=>F(!0),children:"新增配置"}),e.jsx(s,{icon:e.jsx(b,{}),onClick:()=>{M(),w(!0)},children:"权益对比"}),e.jsx(s,{type:"primary",icon:e.jsx($,{}),onClick:D,children:"重新计算等级"}),e.jsx(s,{icon:e.jsx(B,{}),onClick:x,children:"刷新"})]}),children:[e.jsx("div",{style:{marginBottom:16},children:e.jsxs(m,{column:2,bordered:!0,size:"small",children:[e.jsx(m.Item,{label:"EXP计算规则",children:"真金充值(1 INR = 10 EXP) + 真金流水(1 INR = 1 EXP) + 金币付费(1 INR = 5 EXP) + 金币流水(1000金币 = 1 EXP) + 活跃任务(10-50 EXP)"}),e.jsx(m.Item,{label:"权益体系",children:"包含回购价格、C2C手续费、返水比例、提现权限等完整经济权益"})]})}),e.jsx(k,{columns:T,dataSource:g,rowKey:"id",loading:C,pagination:!1,size:"middle",scroll:{x:1500}})]}),e.jsx(j,{title:"编辑VIP配置",open:P,onCancel:()=>c(!1),onOk:()=>o.submit(),width:800,destroyOnClose:!0,children:e.jsx(t,{form:o,layout:"vertical",onFinish:N,children:e.jsxs(f,{defaultActiveKey:"basic",children:[e.jsxs(n,{tab:"基础信息",children:[e.jsxs(U,{gutter:16,children:[e.jsx(R,{span:12,children:e.jsx(t.Item,{label:"等级名称",name:"levelName",rules:[{required:!0,message:"请输入等级名称"}],children:e.jsx(I,{})})}),e.jsx(R,{span:12,children:e.jsx(t.Item,{label:"所需EXP",name:"requiredExp",rules:[{required:!0,message:"请输入所需EXP"}],children:e.jsx(l,{min:0,style:{width:"100%"}})})})]}),e.jsx(t.Item,{label:"战略定位",name:"strategicPosition",children:e.jsx(I.TextArea,{rows:2})}),e.jsx(t.Item,{label:"每日金币奖励",name:"dailyGoldReward",rules:[{required:!0,message:"请输入每日金币奖励"}],children:e.jsx(l,{min:0,style:{width:"100%"}})})]},"basic"),e.jsxs(n,{tab:"回购权益",children:[e.jsx(t.Item,{label:"开启回购",name:"buybackEnabled",valuePropName:"checked",children:e.jsx(Z,{})}),e.jsx(t.Item,{label:"回购价格 (金币:1 INR)",name:"buybackRate",tooltip:"如140表示140金币兑换1 INR",children:e.jsx(l,{min:1,style:{width:"100%"}})})]},"buyback"),e.jsxs(n,{tab:"交易权益",children:[e.jsx(t.Item,{label:"C2C手续费率 (%)",name:"c2cFeeRate",rules:[{required:!0,message:"请输入C2C手续费率"}],children:e.jsx(l,{min:0,max:100,precision:2,style:{width:"100%"}})}),e.jsx(t.Item,{label:"周返水比例 (%)",name:"rakebackRate",rules:[{required:!0,message:"请输入返水比例"}],children:e.jsx(l,{min:0,max:100,precision:2,style:{width:"100%"}})})]},"trading"),e.jsxs(n,{tab:"提现权益",children:[e.jsx(t.Item,{label:"提现手续费率 (%)",name:"withdrawalFeeRate",children:e.jsx(l,{min:0,max:100,precision:2,style:{width:"100%"}})}),e.jsx(t.Item,{label:"每日提现额度 (INR)",name:"dailyWithdrawalLimit",tooltip:"留空表示无上限",children:e.jsx(l,{min:1,style:{width:"100%"}})}),e.jsx(t.Item,{label:"提现优先级",name:"withdrawalPriority",rules:[{required:!0,message:"请选择提现优先级"}],children:e.jsxs(E,{style:{width:"100%"},children:[e.jsx(u,{value:1,children:"普通"}),e.jsx(u,{value:2,children:"优先"}),e.jsx(u,{value:3,children:"VIP专享"})]})})]},"withdrawal")]})})}),e.jsx(j,{title:"VIP等级权益对比",open:L,onCancel:()=>w(!1),footer:null,width:1200,children:e.jsx(k,{dataSource:S,rowKey:"vipLevel",pagination:!1,size:"small",scroll:{x:1e3},columns:[{title:"VIP等级",dataIndex:"vipLevel",key:"vipLevel",width:80},{title:"等级名称",dataIndex:"levelName",key:"levelName",width:100},{title:"所需EXP",dataIndex:"requiredExp",key:"requiredExp",width:100},{title:"每日金币",dataIndex:"dailyGoldReward",key:"dailyGoldReward",width:100},{title:"回购价格",dataIndex:"buybackRateDisplay",key:"buybackRateDisplay",width:100},{title:"C2C费率",dataIndex:"c2cFeeRate",key:"c2cFeeRate",width:100},{title:"返水比例",dataIndex:"rakebackRate",key:"rakebackRate",width:100},{title:"提现费率",dataIndex:"withdrawalFeeRate",key:"withdrawalFeeRate",width:100},{title:"提现额度",dataIndex:"withdrawalLimitDisplay",key:"withdrawalLimitDisplay",width:120}]})})]})};export{ae as default};
