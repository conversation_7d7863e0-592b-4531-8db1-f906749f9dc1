import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Form,
  InputNumber,
  Input,
  message,
  Space,
  Tag,
  Descriptions,
  Modal,
  Switch,
  Select,
  Tabs,
  Row,
  Col,
  Statistic,
  Progress
} from 'antd';
import {
  VipConfigV2,
  fetchVipConfigsV2,
  fetchUpdateVipConfigV2,
  fetchCreateVipConfigV2,
  fetchDeleteVipConfigV2,
  fetchVipConfigV2Comparison,
  fetchRecalculateAllVipV2
} from '#src/api/config';
import { 
  EditOutlined, 
  CalculatorOutlined, 
  ReloadOutlined, 
  CrownOutlined,
  PlusOutlined,
  EyeOutlined,
  TrophyOutlined,
  DollarOutlined,
  GiftOutlined
} from '@ant-design/icons';
import { BasicContent } from '#src/components';

const { TabPane } = Tabs;
const { Option } = Select;

// 使用从API模块导入的VipConfigV2类型

const VipConfigV2Page: React.FC = () => {
  const [configs, setConfigs] = useState<VipConfigV2[]>([]);
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [comparisonModalVisible, setComparisonModalVisible] = useState(false);
  const [currentConfig, setCurrentConfig] = useState<VipConfigV2 | null>(null);
  const [comparisonData, setComparisonData] = useState<any[]>([]);
  const [form] = Form.useForm();
  const [createForm] = Form.useForm();

  // 获取VIP配置列表
  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const response = await fetchVipConfigsV2();
      if (response.code === 200) {
        setConfigs(response.result.list);
      } else {
        message.error(response.message || '获取VIP配置失败');
      }
    } catch (error) {
      message.error('获取VIP配置失败');
      console.error('获取VIP配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取权益对比数据
  const fetchComparisonData = async () => {
    try {
      const response = await fetchVipConfigV2Comparison();
      if (response.code === 200) {
        setComparisonData(response.result);
      } else {
        message.error(response.message || '获取权益对比数据失败');
      }
    } catch (error) {
      message.error('获取对比数据失败');
    }
  };

  useEffect(() => {
    fetchConfigs();
  }, []);

  const columns = [
    {
      title: 'VIP等级',
      dataIndex: 'vipLevel',
      key: 'vipLevel',
      width: 100,
      render: (level: number) => (
        <Tag color="gold" icon={<CrownOutlined />}>
          VIP{level}
        </Tag>
      ),
    },
    {
      title: '等级名称',
      dataIndex: 'levelName',
      key: 'levelName',
      width: 120,
    },
    {
      title: '战略定位',
      dataIndex: 'strategicPosition',
      key: 'strategicPosition',
      width: 150,
      ellipsis: true,
    },
    {
      title: '所需EXP',
      dataIndex: 'requiredExp',
      key: 'requiredExp',
      width: 120,
      render: (exp: number) => exp.toLocaleString(),
    },
    {
      title: '每日金币',
      dataIndex: 'dailyGoldReward',
      key: 'dailyGoldReward',
      width: 100,
      render: (reward: number) => `${reward.toLocaleString()}`,
    },
    {
      title: '回购权益',
      key: 'buyback',
      width: 120,
      render: (record: VipConfigV2) => (
        <Space direction="vertical" size="small">
          <Tag color={record.buybackEnabled ? 'green' : 'red'}>
            {record.buybackEnabled ? '已开启' : '未开启'}
          </Tag>
          {record.buybackEnabled && record.buybackRate && (
            <span style={{ fontSize: '12px' }}>{record.buybackRate}:1</span>
          )}
        </Space>
      ),
    },
    {
      title: 'C2C费率',
      dataIndex: 'c2cFeeRate',
      key: 'c2cFeeRate',
      width: 100,
      render: (rate: number) => `${rate}%`,
    },
    {
      title: '返水比例',
      dataIndex: 'rakebackRate',
      key: 'rakebackRate',
      width: 100,
      render: (rate: number) => `${rate}%`,
    },
    {
      title: '提现权益',
      key: 'withdrawal',
      width: 150,
      render: (record: VipConfigV2) => (
        <Space direction="vertical" size="small">
          {record.withdrawalFeeRate && (
            <span style={{ fontSize: '12px' }}>费率: {record.withdrawalFeeRate}%</span>
          )}
          {record.dailyWithdrawalLimit ? (
            <span style={{ fontSize: '12px' }}>限额: ₹{record.dailyWithdrawalLimit.toLocaleString()}</span>
          ) : (
            <span style={{ fontSize: '12px' }}>限额: 无上限</span>
          )}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: number) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: VipConfigV2) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentConfig(record);
              form.setFieldsValue(record);
              setEditModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              // TODO: 查看详情
            }}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  const handleEdit = async (values: any) => {
    if (!currentConfig) return;
    try {
      const response = await fetchUpdateVipConfigV2(currentConfig.id, values);
      if (response.code === 200) {
        message.success('更新成功');
        setEditModalVisible(false);
        fetchConfigs();
      } else {
        message.error(response.message || '更新失败');
      }
    } catch (error) {
      message.error('更新失败');
    }
  };

  const handleCreate = async (values: any) => {
    try {
      const response = await fetchCreateVipConfigV2(values);
      if (response.code === 200) {
        message.success('创建成功');
        setCreateModalVisible(false);
        createForm.resetFields();
        fetchConfigs();
      } else {
        message.error(response.message || '创建失败');
      }
    } catch (error) {
      message.error('创建失败');
    }
  };

  const recalculateAllLevels = async () => {
    try {
      const response = await fetchRecalculateAllVipV2();
      if (response.code === 200) {
        message.success(`重新计算完成！处理用户: ${response.result.processed}, 升级: ${response.result.upgraded}, 降级: ${response.result.downgraded}`);
        fetchConfigs();
      } else {
        message.error(response.message || '重新计算失败');
      }
    } catch (error) {
      message.error('重新计算失败');
    }
  };

  return (
    <BasicContent>
      <Card
        title={
          <Space>
            <TrophyOutlined />
            VIP经济权益配置管理 V12.0
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              新增配置
            </Button>
            <Button
              icon={<EyeOutlined />}
              onClick={() => {
                fetchComparisonData();
                setComparisonModalVisible(true);
              }}
            >
              权益对比
            </Button>
            <Button
              type="primary"
              icon={<CalculatorOutlined />}
              onClick={recalculateAllLevels}
            >
              重新计算等级
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchConfigs}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: 16 }}>
          <Descriptions column={2} bordered size="small">
            <Descriptions.Item label="EXP计算规则">
              真金充值(1 INR = 10 EXP) + 真金流水(1 INR = 1 EXP) + 金币付费(1 INR = 5 EXP) + 金币流水(1000金币 = 1 EXP) + 活跃任务(10-50 EXP)
            </Descriptions.Item>
            <Descriptions.Item label="权益体系">
              包含回购价格、C2C手续费、返水比例、提现权限等完整经济权益
            </Descriptions.Item>
          </Descriptions>
        </div>

        <Table
          columns={columns}
          dataSource={configs}
          rowKey="id"
          loading={loading}
          pagination={false}
          size="middle"
          scroll={{ x: 1500 }}
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑VIP配置"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleEdit}
        >
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基础信息" key="basic">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="等级名称"
                    name="levelName"
                    rules={[{ required: true, message: '请输入等级名称' }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="所需EXP"
                    name="requiredExp"
                    rules={[{ required: true, message: '请输入所需EXP' }]}
                  >
                    <InputNumber min={0} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item
                label="战略定位"
                name="strategicPosition"
              >
                <Input.TextArea rows={2} />
              </Form.Item>
              <Form.Item
                label="每日金币奖励"
                name="dailyGoldReward"
                rules={[{ required: true, message: '请输入每日金币奖励' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </TabPane>
            
            <TabPane tab="回购权益" key="buyback">
              <Form.Item
                label="开启回购"
                name="buybackEnabled"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Form.Item
                label="回购价格 (金币:1 INR)"
                name="buybackRate"
                tooltip="如140表示140金币兑换1 INR"
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </TabPane>

            <TabPane tab="交易权益" key="trading">
              <Form.Item
                label="C2C手续费率 (%)"
                name="c2cFeeRate"
                rules={[{ required: true, message: '请输入C2C手续费率' }]}
              >
                <InputNumber min={0} max={100} precision={2} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item
                label="周返水比例 (%)"
                name="rakebackRate"
                rules={[{ required: true, message: '请输入返水比例' }]}
              >
                <InputNumber min={0} max={100} precision={2} style={{ width: '100%' }} />
              </Form.Item>
            </TabPane>

            <TabPane tab="提现权益" key="withdrawal">
              <Form.Item
                label="提现手续费率 (%)"
                name="withdrawalFeeRate"
              >
                <InputNumber min={0} max={100} precision={2} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item
                label="每日提现额度 (INR)"
                name="dailyWithdrawalLimit"
                tooltip="留空表示无上限"
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item
                label="提现优先级"
                name="withdrawalPriority"
                rules={[{ required: true, message: '请选择提现优先级' }]}
              >
                <Select style={{ width: '100%' }}>
                  <Option value={1}>普通</Option>
                  <Option value={2}>优先</Option>
                  <Option value={3}>VIP专享</Option>
                </Select>
              </Form.Item>
            </TabPane>
          </Tabs>
        </Form>
      </Modal>

      {/* 权益对比模态框 */}
      <Modal
        title="VIP等级权益对比"
        open={comparisonModalVisible}
        onCancel={() => setComparisonModalVisible(false)}
        footer={null}
        width={1200}
      >
        <Table
          dataSource={comparisonData}
          rowKey="vipLevel"
          pagination={false}
          size="small"
          scroll={{ x: 1000 }}
          columns={[
            { title: 'VIP等级', dataIndex: 'vipLevel', key: 'vipLevel', width: 80 },
            { title: '等级名称', dataIndex: 'levelName', key: 'levelName', width: 100 },
            { title: '所需EXP', dataIndex: 'requiredExp', key: 'requiredExp', width: 100 },
            { title: '每日金币', dataIndex: 'dailyGoldReward', key: 'dailyGoldReward', width: 100 },
            { title: '回购价格', dataIndex: 'buybackRateDisplay', key: 'buybackRateDisplay', width: 100 },
            { title: 'C2C费率', dataIndex: 'c2cFeeRate', key: 'c2cFeeRate', width: 100 },
            { title: '返水比例', dataIndex: 'rakebackRate', key: 'rakebackRate', width: 100 },
            { title: '提现费率', dataIndex: 'withdrawalFeeRate', key: 'withdrawalFeeRate', width: 100 },
            { title: '提现额度', dataIndex: 'withdrawalLimitDisplay', key: 'withdrawalLimitDisplay', width: 120 },
          ]}
        />
      </Modal>
    </BasicContent>
  );
};

export default VipConfigV2Page;
