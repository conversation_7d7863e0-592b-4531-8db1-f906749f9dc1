"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipExpAddDto = exports.VipConfigListDto = exports.VipConfigQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class VipConfigQueryDto {
    page = 1;
    pageSize = 10;
    vipLevel;
    status;
    levelName;
    buybackEnabled;
}
exports.VipConfigQueryDto = VipConfigQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '页码必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '页码不能小于1' }),
    __metadata("design:type", Number)
], VipConfigQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '每页数量必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '每页数量不能小于1' }),
    (0, class_validator_1.Max)(100, { message: '每页数量不能大于100' }),
    __metadata("design:type", Number)
], VipConfigQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP等级筛选', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: 'VIP等级必须是数字' }),
    (0, class_validator_1.Min)(0, { message: 'VIP等级不能小于0' }),
    (0, class_validator_1.Max)(99, { message: 'VIP等级不能大于99' }),
    __metadata("design:type", Number)
], VipConfigQueryDto.prototype, "vipLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态筛选：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], VipConfigQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级名称搜索', example: '活跃', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '等级名称必须是字符串' }),
    __metadata("design:type", String)
], VipConfigQueryDto.prototype, "levelName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否开启回购筛选', example: true, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], VipConfigQueryDto.prototype, "buybackEnabled", void 0);
class VipConfigListDto {
    id;
    vipLevel;
    levelName;
    strategicPosition;
    requiredExp;
    realMoneyRechargeExpRatio;
    realMoneyFlowExpRatio;
    goldPaymentExpRatio;
    goldFlowExpRatio;
    dailyTaskExpMin;
    dailyTaskExpMax;
    dailyGoldReward;
    buybackEnabled;
    buybackRate;
    c2cFeeRate;
    rakebackRate;
    withdrawalFeeRate;
    dailyWithdrawalLimit;
    withdrawalPriority;
    taskUnlockRequired;
    taskUnlockGamesRequired;
    status;
    remark;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
}
exports.VipConfigListDto = VipConfigListDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP配置ID' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP等级' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "vipLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级名称' }),
    __metadata("design:type", String)
], VipConfigListDto.prototype, "levelName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '核心战略定位' }),
    __metadata("design:type", String)
], VipConfigListDto.prototype, "strategicPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所需EXP经验值' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "requiredExp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '真金充值EXP比例' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "realMoneyRechargeExpRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '真金流水EXP比例' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "realMoneyFlowExpRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币付费EXP比例' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "goldPaymentExpRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币流水EXP比例' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "goldFlowExpRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活跃任务最小EXP' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "dailyTaskExpMin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活跃任务最大EXP' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "dailyTaskExpMax", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日可领取金币数量' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "dailyGoldReward", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否开启官方回购' }),
    __metadata("design:type", Boolean)
], VipConfigListDto.prototype, "buybackEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '官方回购价格' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "buybackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'C2C手续费率' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "c2cFeeRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '周返水比例' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "rakebackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '提现手续费率' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "withdrawalFeeRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日提现额度上限' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "dailyWithdrawalLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '提现优先级' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "withdrawalPriority", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务是否需要解锁' }),
    __metadata("design:type", Boolean)
], VipConfigListDto.prototype, "taskUnlockRequired", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '解锁所需游戏局数' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "taskUnlockGamesRequired", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注说明' }),
    __metadata("design:type", String)
], VipConfigListDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人ID' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新人ID' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], VipConfigListDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], VipConfigListDto.prototype, "updateTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人信息', required: false }),
    __metadata("design:type", Object)
], VipConfigListDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新人信息', required: false }),
    __metadata("design:type", Object)
], VipConfigListDto.prototype, "updater", void 0);
class VipExpAddDto {
    expType;
    amount;
    description;
}
exports.VipExpAddDto = VipExpAddDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'EXP类型', enum: ['real_money_recharge', 'real_money_flow', 'gold_payment', 'gold_flow', 'daily_task'] }),
    (0, class_validator_1.IsString)({ message: 'EXP类型必须是字符串' }),
    __metadata("design:type", String)
], VipExpAddDto.prototype, "expType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金额或数量', example: 100 }),
    (0, class_validator_1.IsNumber)({}, { message: '金额必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '金额不能小于0' }),
    __metadata("design:type", Number)
], VipExpAddDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '描述', example: '充值获得EXP', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '描述必须是字符串' }),
    __metadata("design:type", String)
], VipExpAddDto.prototype, "description", void 0);
//# sourceMappingURL=vip-config-query-v2.dto.js.map