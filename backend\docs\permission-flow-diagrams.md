# 权限系统流程图

## 1. 用户登录认证流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant Backend as 后端
    participant DB as 数据库
    participant JWT as JWT服务

    User->>Frontend: 输入用户名密码
    Frontend->>Backend: POST /api/system/auth/login
    Backend->>DB: 验证用户凭据
    
    alt 用户不存在或密码错误
        DB-->>Backend: 验证失败
        Backend-->>Frontend: 401 认证失败
        Frontend-->>User: 显示错误信息
    else 验证成功
        DB-->>Backend: 返回用户信息
        Backend->>JWT: 生成JWT Token
        JWT-->>Backend: 返回Token
        Backend-->>Frontend: 返回Token和基本信息
        Frontend->>Frontend: 存储Token到localStorage
        Frontend-->>User: 跳转到首页
    end
```

## 2. 权限获取和路由生成流程

```mermaid
sequenceDiagram
    participant Frontend as 前端
    participant AuthGuard as 权限守卫
    participant Backend as 后端
    participant DB as 数据库
    participant RouteGen as 路由生成器

    Frontend->>AuthGuard: 页面访问
    AuthGuard->>AuthGuard: 检查登录状态
    
    alt 未登录
        AuthGuard-->>Frontend: 重定向到登录页
    else 已登录但权限未获取
        AuthGuard->>Backend: GET /api/system/user/info
        Backend->>DB: 查询用户信息
        DB-->>Backend: 返回用户、角色信息
        
        alt 是超级管理员
            Backend->>DB: 获取所有权限
            Backend->>DB: 获取所有菜单
        else 普通用户
            Backend->>DB: 根据角色获取权限
            Backend->>DB: 根据权限获取菜单
        end
        
        DB-->>Backend: 返回权限和菜单数据
        Backend-->>AuthGuard: 返回完整权限信息
        AuthGuard->>RouteGen: 生成动态路由
        RouteGen-->>AuthGuard: 返回路由配置
        AuthGuard->>Frontend: 设置权限状态和路由
        Frontend-->>Frontend: 渲染页面
    else 已登录且权限已获取
        AuthGuard->>AuthGuard: 验证页面权限
        alt 有权限
            AuthGuard-->>Frontend: 渲染页面
        else 无权限
            AuthGuard-->>Frontend: 重定向到403页面
        end
    end
```

## 3. 菜单权限过滤流程

```mermaid
flowchart TD
    A[开始获取菜单] --> B{是否为超级管理员?}
    
    B -->|是| C[获取所有菜单]
    B -->|否| D[获取用户角色]
    
    D --> E[根据角色获取权限列表]
    E --> F[查询菜单表]
    F --> G{菜单是否有权限要求?}
    
    G -->|无权限要求| H[包含该菜单]
    G -->|有权限要求| I{用户是否有对应权限?}
    
    I -->|有| H
    I -->|无| J[排除该菜单]
    
    C --> K[构建菜单树]
    H --> K
    J --> L{还有其他菜单?}
    L -->|是| F
    L -->|否| K
    
    K --> M[返回菜单树]
    M --> N[结束]
```

## 4. 前端路由权限验证流程

```mermaid
flowchart TD
    A[用户访问页面] --> B[AuthGuard拦截]
    B --> C{是否在白名单?}
    
    C -->|是| D[直接渲染页面]
    C -->|否| E{是否已登录?}
    
    E -->|否| F[重定向到登录页]
    E -->|是| G{权限是否已获取?}
    
    G -->|否| H[获取用户权限信息]
    H --> I[生成动态路由]
    I --> J[设置权限状态]
    J --> K[验证当前页面权限]
    
    G -->|是| K
    
    K --> L{是否为超级管理员?}
    L -->|是| D
    L -->|否| M{路由是否需要权限?}
    
    M -->|否| D
    M -->|是| N{用户是否有对应权限?}
    
    N -->|是| D
    N -->|否| O[重定向到403页面]
    
    D --> P[结束]
    F --> P
    O --> P
```

## 5. 后端API权限验证流程

```mermaid
flowchart TD
    A[API请求] --> B[JWT认证守卫]
    B --> C{Token是否有效?}
    
    C -->|否| D[返回401未认证]
    C -->|是| E[解析用户信息]
    E --> F[权限守卫检查]
    F --> G{接口是否需要权限?}
    
    G -->|否| H[执行业务逻辑]
    G -->|是| I{是否为超级管理员?}
    
    I -->|是| H
    I -->|否| J[获取用户权限列表]
    J --> K{用户是否有所需权限?}
    
    K -->|是| H
    K -->|否| L[返回403无权限]
    
    H --> M[返回响应结果]
    
    D --> N[结束]
    L --> N
    M --> N
```

## 6. 动态路由生成流程

```mermaid
flowchart TD
    A[开始生成路由] --> B[获取后端菜单数据]
    B --> C[初始化页面模块映射]
    C --> D[遍历菜单项]
    
    D --> E{是否有component字段?}
    E -->|否| F{是否有子菜单?}
    E -->|是| G[标准化组件路径]
    
    G --> H[构建模块路径]
    H --> I{模块是否存在?}
    
    I -->|是| J[创建懒加载组件]
    I -->|否| K[设置为未知组件]
    
    J --> L[设置路由组件]
    K --> L
    
    F -->|是| M[递归处理子菜单]
    F -->|否| N[跳过该菜单项]
    
    M --> O{还有其他菜单?}
    L --> O
    N --> O
    
    O -->|是| D
    O -->|否| P[添加路由ID]
    P --> Q[返回路由配置]
    Q --> R[结束]
```

## 7. 权限按钮控制流程

```mermaid
flowchart TD
    A[页面渲染按钮] --> B[AccessControl组件]
    B --> C[useAccess Hook]
    C --> D{是否为超级管理员?}
    
    D -->|是| E[显示按钮]
    D -->|否| F[获取当前路由权限配置]
    F --> G{路由是否有权限配置?}
    
    G -->|否| H[隐藏按钮]
    G -->|是| I[检查用户权限]
    I --> J{用户是否有所需权限?}
    
    J -->|是| E
    J -->|否| H
    
    E --> K[结束]
    H --> K
```

## 8. 超级管理员权限流程

```mermaid
flowchart TD
    A[权限检查开始] --> B{检查is_super_admin字段}
    
    B -->|true| C[绕过所有权限检查]
    B -->|false| D[进入正常权限验证流程]
    
    C --> E[获取所有权限]
    C --> F[获取所有菜单]
    C --> G[允许所有操作]
    
    D --> H[根据角色获取权限]
    D --> I[根据权限过滤菜单]
    D --> J[验证具体操作权限]
    
    E --> K[返回结果]
    F --> K
    G --> K
    H --> K
    I --> K
    J --> K
```

## 9. 用户权限变更流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Backend as 后端
    participant DB as 数据库
    participant Cache as 缓存
    participant User as 目标用户

    Admin->>Backend: 修改用户权限
    Backend->>DB: 更新用户角色关系
    DB-->>Backend: 确认更新成功
    Backend->>Cache: 清除用户权限缓存
    Backend-->>Admin: 返回操作成功
    
    Note over User: 用户下次请求时
    User->>Backend: 访问需要权限的接口
    Backend->>DB: 重新查询用户权限
    DB-->>Backend: 返回最新权限
    Backend->>Cache: 更新权限缓存
    Backend-->>User: 根据新权限返回结果
```

## 10. 权限异常处理流程

```mermaid
flowchart TD
    A[权限验证异常] --> B{异常类型}
    
    B -->|Token无效| C[清除本地Token]
    B -->|Token过期| D[尝试刷新Token]
    B -->|无权限| E[显示403页面]
    B -->|网络错误| F[显示网络错误提示]
    
    C --> G[重定向到登录页]
    D --> H{刷新成功?}
    
    H -->|是| I[重新发起请求]
    H -->|否| G
    
    E --> J[提供返回首页选项]
    F --> K[提供重试选项]
    
    G --> L[结束]
    I --> L
    J --> L
    K --> L
```

这些流程图详细描述了InApp2权限系统的各个关键流程，包括用户认证、权限获取、路由生成、权限验证等核心功能的执行逻辑。通过这些图表，开发人员可以更好地理解系统的工作原理，便于维护和扩展。
