import{u as j,j as o}from"./index-DD0gcXtR.js";import{a as f,b as y}from"./index-Cb-TAT0P.js";import{a as v}from"./react-BUTTOX-3.js";import{B as g}from"./index-Bo3BoCEt.js";import{u as p}from"./useMutation-Dm45oaMR.js";import{av as s,s as n,I as u,p as m,q as F,S as I}from"./antd-FyCAPQKa.js";function E({title:b,open:c,onCloseChange:i,detailData:t,refreshTable:d}){const{t:e}=j(),[r]=s.useForm(),k=p({mutationFn:f,onSuccess:()=>{n.success(e("common.addSuccess")),i(!1),d(),r.resetFields()},onError:a=>{n.error(a.message||e("common.addFailed"))}}),h=p({mutationFn:({id:a,data:l})=>y(a,l),onSuccess:()=>{n.success(e("common.updateSuccess")),i(!1),d(),r.resetFields()},onError:a=>{n.error(a.message||e("common.updateFailed"))}}),x=async a=>{try{const l={...a,status:a.status?1:0};t!=null&&t.id?await h.mutateAsync({id:t.id,data:l}):await k.mutateAsync(l)}catch(l){console.error("Form submission error:",l)}},w=()=>{r.resetFields(),i(!1)};return v.useEffect(()=>{c&&(t?r.setFieldsValue({...t,status:t.status===1}):(r.resetFields(),r.setFieldsValue({valueType:"text",status:!0,order:0})))},[t,c,r]),o.jsx(g,{title:b,open:c,onCancel:w,onOk:()=>r.submit(),confirmLoading:k.isPending||h.isPending,width:600,children:o.jsxs(s,{form:r,layout:"vertical",onFinish:x,autoComplete:"off",children:[o.jsx(s.Item,{label:e("workbook.type"),name:"type",rules:[{required:!0,message:e("workbook.typeRequired")},{max:50,message:e("workbook.typeMaxLength")}],children:o.jsx(u,{placeholder:e("workbook.typePlaceholder")})}),o.jsx(s.Item,{label:e("workbook.text"),name:"text",rules:[{required:!0,message:e("workbook.textRequired")},{max:200,message:e("workbook.textMaxLength")}],children:o.jsx(u,{placeholder:e("workbook.textPlaceholder")})}),o.jsx(s.Item,{label:e("workbook.value"),name:"value",rules:[{required:!0,message:e("workbook.valueRequired")},{max:500,message:e("workbook.valueMaxLength")}],children:o.jsx(u,{placeholder:e("workbook.valuePlaceholder")})}),o.jsx(s.Item,{label:e("workbook.valueType"),name:"valueType",rules:[{required:!0,message:e("workbook.valueTypeRequired")}],children:o.jsxs(m,{placeholder:e("workbook.valueTypePlaceholder"),children:[o.jsx(m.Option,{value:"text",children:e("workbook.valueTypeOptions.text")}),o.jsx(m.Option,{value:"number",children:e("workbook.valueTypeOptions.number")}),o.jsx(m.Option,{value:"json",children:e("workbook.valueTypeOptions.json")})]})}),o.jsx(s.Item,{label:e("workbook.remark"),name:"remark",rules:[{max:500,message:e("workbook.remarkMaxLength")}],children:o.jsx(u.TextArea,{placeholder:e("workbook.remarkPlaceholder"),rows:3})}),o.jsx(s.Item,{label:e("workbook.order"),name:"order",children:o.jsx(F,{placeholder:e("workbook.orderPlaceholder"),min:0,style:{width:"100%"}})}),o.jsx(s.Item,{label:e("common.status"),name:"status",valuePropName:"checked",children:o.jsx(I,{checkedChildren:e("common.enabled"),unCheckedChildren:e("common.disabled")})})]})})}export{E as Detail};
