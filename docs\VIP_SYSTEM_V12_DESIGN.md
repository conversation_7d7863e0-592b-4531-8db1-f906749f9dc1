# VIP经济权益系统 V12.0 设计文档

## 概述

VIP经济权益系统 V12.0 采用"Stable Value & Progressive Trust"（稳定价值与渐进信任）经济模型，通过EXP经验值系统替代传统积分制度，构建完整的用户经济权益体系。

## 核心理念

### 1. 稳定价值 (Stable Value)
- **官方回购体系**：为高等级用户提供固定汇率的金币回购服务
- **透明费率结构**：所有手续费率公开透明，随VIP等级递减
- **可预期收益**：用户可清晰计算各项活动的EXP收益和权益提升

### 2. 渐进信任 (Progressive Trust)
- **EXP累积机制**：通过多种活动累积经验值，体现用户价值贡献
- **权益递进解锁**：随着信任等级提升，逐步解锁更多经济权益
- **风险分层管理**：不同等级用户享受差异化的风险控制和服务优先级

## 系统架构

### 1. EXP经验值系统

#### 1.1 EXP获取规则（基于表1.2）
```
真金充值：1 INR = 10 EXP
真金流水：1 INR = 1 EXP  
金币付费：1 INR = 5 EXP
金币流水：1000 金币 = 1 EXP
活跃任务：+10 到 +50 EXP（随机）
```

#### 1.2 EXP计算公式
```typescript
totalEXP = (realMoneyRecharge * 10) + 
           (realMoneyFlow * 1) + 
           (goldPayment * 5) + 
           (goldFlow * 0.001) + 
           (dailyTaskCount * randomBetween(10, 50))
```

### 2. VIP等级体系（基于表1.1）

| VIP等级 | 等级名称 | 所需EXP | 战略定位 | 每日金币奖励 |
|---------|----------|---------|----------|--------------|
| 0 | 体验用户 | 0 | 体验用户/流量基础 | 0 |
| 1 | 活跃认证 | 500 | 活跃认证 | 50 |
| 2 | 信任用户 | 2,000 | 信任用户 | 100 |
| 3 | 忠诚用户 | 10,000 | 忠诚用户 | 200 |
| 4 | 核心用户 | 50,000 | 核心用户 | 500 |
| 5 | 高级用户 | 200,000 | 高级用户 | 1,000 |
| 6 | 超级用户 | 1,000,000 | 超级用户 | 2,000 |
| 7 | 至尊用户 | 5,000,000 | 至尊用户 | 5,000 |

### 3. 经济权益体系

#### 3.1 官方回购系统
- **VIP 0-2**：无回购资格
- **VIP 3**：140:1 回购价格
- **VIP 4**：135:1 回购价格  
- **VIP 5**：130:1 回购价格
- **VIP 6**：128:1 回购价格
- **VIP 7**：125:1 回购价格

#### 3.2 C2C交易费率
- **VIP 0-1**：15% 手续费
- **VIP 2**：12% 手续费
- **VIP 3**：10% 手续费
- **VIP 4**：8% 手续费
- **VIP 5**：6% 手续费
- **VIP 6**：4% 手续费
- **VIP 7**：2% 手续费

#### 3.3 真金系统权益（基于表3.1）

##### 周返水比例
- **VIP 0-1**：0% 返水
- **VIP 2**：0.25% 返水
- **VIP 3**：0.50% 返水
- **VIP 4**：1.00% 返水
- **VIP 5**：1.50% 返水
- **VIP 6**：2.00% 返水
- **VIP 7**：2.50% 返水

##### 提现权益
| VIP等级 | 手续费率 | 每日限额 | 优先级 |
|---------|----------|----------|--------|
| 0 | 无资格 | 无资格 | 普通 |
| 1 | 5.0% | ₹2,000 | 普通 |
| 2 | 4.5% | ₹5,000 | 普通 |
| 3 | 4.0% | ₹10,000 | 优先 |
| 4 | 3.5% | ₹25,000 | 优先 |
| 5 | 3.0% | ₹50,000 | 优先 |
| 6 | 2.5% | ₹100,000 | VIP专享 |
| 7 | 2.0% | 无上限 | VIP专享 |

## 技术实现

### 1. 数据库设计

#### 1.1 VIP配置表 (vip_configs)
```sql
CREATE TABLE vip_configs (
    id SERIAL PRIMARY KEY,
    vip_level INTEGER UNIQUE NOT NULL,
    level_name VARCHAR(50) NOT NULL,
    strategic_position VARCHAR(200),
    required_exp BIGINT NOT NULL,
    
    -- EXP获取规则配置
    real_money_recharge_exp_ratio DECIMAL(10,4) DEFAULT 10.0000,
    real_money_flow_exp_ratio DECIMAL(10,4) DEFAULT 1.0000,
    gold_payment_exp_ratio DECIMAL(10,4) DEFAULT 5.0000,
    gold_flow_exp_ratio DECIMAL(10,6) DEFAULT 0.001000,
    daily_task_exp_min INTEGER DEFAULT 10,
    daily_task_exp_max INTEGER DEFAULT 50,
    
    -- 每日奖励系统
    daily_gold_reward INTEGER DEFAULT 0,
    
    -- 官方回购系统权益
    buyback_enabled BOOLEAN DEFAULT FALSE,
    buyback_rate INTEGER DEFAULT NULL,
    
    -- C2C交易系统权益
    c2c_fee_rate DECIMAL(5,2) DEFAULT 15.00,
    
    -- 真金系统权益
    rakeback_rate DECIMAL(5,2) DEFAULT 0.00,
    withdrawal_fee_rate DECIMAL(5,2) DEFAULT NULL,
    daily_withdrawal_limit BIGINT DEFAULT NULL,
    withdrawal_priority INTEGER DEFAULT 1,
    
    -- 解锁机制配置
    task_unlock_required BOOLEAN DEFAULT FALSE,
    task_unlock_games_required INTEGER DEFAULT 0,
    
    -- 状态和管理字段
    status INTEGER DEFAULT 1,
    remark TEXT,
    created_by INTEGER,
    updated_by INTEGER,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 1.2 用户表扩展 (app_users)
```sql
ALTER TABLE app_users 
ADD COLUMN total_exp BIGINT DEFAULT 0,
ADD COLUMN last_exp_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
```

### 2. 核心服务

#### 2.1 VipExpService - EXP计算服务
```typescript
@Injectable()
export class VipExpService {
  // EXP计算
  async calculateExpFromActivity(expType: string, amount: number): Promise<number>
  
  // 添加用户EXP
  async addExp(userId: number, expType: string, amount: number, description?: string): Promise<number>
  
  // 计算VIP等级
  async calculateVipLevelFromExp(totalExp: number): Promise<number>
  
  // 获取用户VIP权益
  async getUserVipBenefits(userId: number): Promise<UserVipBenefits>
  
  // 重新计算所有用户等级
  async recalculateAllUserVipLevels(): Promise<RecalculationResult>
}
```

#### 2.2 VipConfigService - VIP配置管理服务
```typescript
@Injectable()
export class VipConfigService {
  // CRUD操作
  async create(createDto: CreateVipConfigDto, userId: number): Promise<VipConfig>
  async findAll(query: VipConfigQueryDto): Promise<PaginatedResult<VipConfig>>
  async update(id: number, updateDto: UpdateVipConfigDto, userId: number): Promise<VipConfig>
  async remove(id: number): Promise<{ message: string }>
  
  // 权益查询
  async getVipLevelComparison(): Promise<VipLevelComparison[]>
  async getUserVipBenefits(userId: number): Promise<UserVipBenefits>
  
  // 权益验证
  async checkUserBuybackPermission(userId: number): Promise<BuybackPermission>
  async checkUserWithdrawalPermission(userId: number): Promise<WithdrawalPermission>
  async checkUserC2CPermission(userId: number): Promise<C2CPermission>
}
```

### 3. API接口

#### 3.1 VIP配置管理
```
GET    /config/vip-v2              # 分页查询VIP配置
POST   /config/vip-v2              # 创建VIP配置
GET    /config/vip-v2/:id          # 查询VIP配置详情
PATCH  /config/vip-v2/:id          # 更新VIP配置
DELETE /config/vip-v2/:id          # 删除VIP配置
GET    /config/vip-v2/comparison   # 获取VIP等级权益对比
```

#### 3.2 用户VIP相关
```
GET  /config/vip-v2/user/:userId/benefits     # 获取用户VIP权益详情
POST /config/vip-v2/user/:userId/add-exp      # 为用户添加EXP
POST /config/vip-v2/recalculate-all           # 重新计算所有用户VIP等级
```

#### 3.3 权益验证
```
GET /config/vip-v2/user/:userId/check-buyback     # 检查回购权限
GET /config/vip-v2/user/:userId/check-withdrawal  # 检查提现权限  
GET /config/vip-v2/user/:userId/check-c2c         # 检查C2C权限
```

## 数据迁移

### 1. 迁移策略
- 备份现有VIP配置数据
- 根据新EXP规则重新计算用户经验值
- 基于新等级要求重新分配用户VIP等级
- 记录迁移日志便于审计

### 2. 迁移脚本
执行 `backend/database/migrations/006-migrate-vip-data-to-v2.sql`

### 3. 迁移验证
- 验证用户等级分布合理性
- 检查EXP计算准确性
- 确认权益配置正确性

## 测试策略

### 1. 单元测试
- VipExpService 测试：EXP计算逻辑
- VipConfigService 测试：配置管理逻辑
- Entity 测试：业务规则验证

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 权益验证测试

### 3. 性能测试
- 大量用户等级重新计算性能
- EXP计算服务并发性能
- 数据库查询优化验证

## 运维监控

### 1. 关键指标
- 各VIP等级用户分布
- EXP获取活跃度统计
- 权益使用情况监控
- 系统性能指标

### 2. 告警机制
- 异常等级变动告警
- EXP计算错误告警
- 权益验证失败告警

## 安全考虑

### 1. 数据安全
- EXP变更记录审计
- 权益配置变更日志
- 敏感操作权限控制

### 2. 业务安全
- EXP计算防刷机制
- 等级变动合理性检查
- 权益使用频率限制

## 未来扩展

### 1. 功能扩展
- 季度/年度VIP特权
- 个性化权益定制
- VIP专属活动系统

### 2. 技术优化
- 缓存策略优化
- 实时计算引擎
- 分布式架构支持

---

**文档版本**: V12.0  
**创建时间**: 2025-06-29  
**最后更新**: 2025-06-29  
**维护团队**: 后端开发团队
