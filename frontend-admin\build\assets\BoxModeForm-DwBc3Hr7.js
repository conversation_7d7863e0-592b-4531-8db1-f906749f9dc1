import{j as e}from"./index-DD0gcXtR.js";import{a as B}from"./react-BUTTOX-3.js";import E from"./GameCategoryConfig-BAVKjy8l.js";import{au as O,a6 as P,aq as u,ao as o,ap as n,av as a,I as V,p as i,T as N,q as F,x as G,aK as K,an as S,aP as M,n as _,aS as k,am as U}from"./antd-FyCAPQKa.js";import"./react-beautiful-dnd.esm-CIUISnkR.js";const{Option:t}=i,{TextArea:C}=V,{Title:re,Text:D}=N,{TabPane:m}=P,H=[{value:"box",label:"BOX风格",description:"盒子布局风格，支持4宫格、浮点内容、游戏分类组等"},{value:"classic",label:"经典风格",description:"传统列表布局风格，简洁明了"},{value:"card",label:"卡片风格",description:"卡片式布局，现代化设计风格"},{value:"grid",label:"网格风格",description:"网格布局，整齐排列"}],J=[{value:1,label:"启用"},{value:0,label:"禁用"}],ae=({form:j,adOptions:X=[],applications:R=[],initialValues:Q,disabled:r=!1})=>{const[x,q]=B.useState("box"),[L,$]=B.useState("basic"),c=s=>X.filter(l=>l.adType===s),g=c(1),f=c(2),I=c(3),y=c(5),v=c(7),z=s=>{q(s),j.setFieldValue("templateType",s)},d=(()=>{const s=j.getFieldsValue(),l=[],p=[];if(s.configName||l.push("配置名称不能为空"),x==="box"){s.homeGridAdId||l.push("BOX模式必须选择4宫格广告");const b=s.gameCategories||[];b.length<4&&l.push("BOX模式至少需要4个游戏分类组"),b.forEach((h,T)=>{var w,A;(!((w=h.categoryTitle)!=null&&w.zh)||!((A=h.categoryTitle)!=null&&A.en))&&l.push(`第${T+1}个分类组标题不完整`),(!h.games||h.games.length<4)&&l.push(`第${T+1}个分类组至少需要4个游戏`)}),s.topBannerAdId||p.push("建议配置顶部Banner广告以提升用户体验"),s.carouselAdId||p.push("建议配置轮播广告以展示重要内容")}return{isValid:l.length===0,errors:l,warnings:p}})();return e.jsxs("div",{children:[!d.isValid&&e.jsx(O,{message:"配置验证失败",description:e.jsx("ul",{style:{margin:"8px 0 0 20px"},children:d.errors.map((s,l)=>e.jsx("li",{style:{color:"#ff4d4f"},children:s},l))}),type:"error",showIcon:!0,style:{marginBottom:16}}),d.warnings.length>0&&e.jsx(O,{message:"配置建议",description:e.jsx("ul",{style:{margin:"8px 0 0 20px"},children:d.warnings.map((s,l)=>e.jsx("li",{style:{color:"#faad14"},children:s},l))}),type:"warning",showIcon:!0,style:{marginBottom:16}}),e.jsxs(P,{activeKey:L,onChange:$,type:"card",children:[e.jsx(m,{tab:e.jsxs("span",{children:[e.jsx(G,{}),"基本配置",!d.isValid&&e.jsx(K,{style:{color:"#ff4d4f",marginLeft:4}})]}),children:e.jsxs(u,{size:"small",title:"基本信息",children:[e.jsxs(o,{gutter:16,children:[e.jsx(n,{span:12,children:e.jsx(a.Item,{name:"configName",label:"配置名称",rules:[{required:!0,message:"请输入配置名称"}],children:e.jsx(V,{placeholder:"请输入配置名称",disabled:r})})}),e.jsx(n,{span:12,children:e.jsx(a.Item,{name:"templateType",label:"模板样式",rules:[{required:!0,message:"请选择模板样式"}],children:e.jsx(i,{placeholder:"请选择模板样式",onChange:z,disabled:r,children:H.map(s=>e.jsx(t,{value:s.value,children:e.jsxs("div",{children:[e.jsx("div",{children:s.label}),e.jsx(D,{type:"secondary",style:{fontSize:12},children:s.description})]})},s.value))})})})]}),e.jsxs(o,{gutter:16,children:[e.jsx(n,{span:12,children:e.jsx(a.Item,{name:"status",label:"状态",rules:[{required:!0,message:"请选择状态"}],children:e.jsx(i,{placeholder:"请选择状态",disabled:r,children:J.map(s=>e.jsx(t,{value:s.value,children:s.label},s.value))})})}),e.jsx(n,{span:12,children:e.jsx(a.Item,{name:"sortOrder",label:"排序",children:e.jsx(F,{placeholder:"请输入排序值",min:0,max:9999,style:{width:"100%"},disabled:r})})})]}),e.jsx(a.Item,{name:"description",label:"配置描述",children:e.jsx(C,{placeholder:"请输入配置描述",rows:3,maxLength:500,showCount:!0,disabled:r})}),e.jsx(a.Item,{name:"remark",label:"备注",children:e.jsx(C,{placeholder:"请输入备注",rows:2,maxLength:500,showCount:!0,disabled:r})})]})},"basic"),e.jsx(m,{tab:e.jsxs("span",{children:[e.jsx(M,{}),"广告配置"]}),children:e.jsxs(u,{size:"small",title:"广告位配置",children:[e.jsxs(o,{gutter:16,children:[e.jsx(n,{span:12,children:e.jsx(a.Item,{name:"topBannerAdId",label:"顶部Banner广告",extra:"显示在页面顶部的Banner广告",children:e.jsx(i,{placeholder:"请选择Banner广告",allowClear:!0,showSearch:!0,disabled:r,children:y.length>0?y.map(s=>e.jsxs(t,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id)):e.jsx(t,{value:"",disabled:!0,children:"暂无Banner广告，请先创建"})})})}),e.jsx(n,{span:12,children:e.jsx(a.Item,{name:"carouselAdId",label:"轮播广告",extra:"首页顶部轮播展示的广告",children:e.jsx(i,{placeholder:"请选择轮播广告",allowClear:!0,showSearch:!0,disabled:r,children:g.length>0?g.map(s=>e.jsxs(t,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id)):e.jsx(t,{value:"",disabled:!0,children:"暂无轮播广告，请先创建"})})})})]}),x==="box"&&e.jsx(o,{gutter:16,children:e.jsx(n,{span:24,children:e.jsx(a.Item,{name:"homeGridAdId",label:e.jsxs("span",{children:["4宫格广告",e.jsx(S,{color:"red",style:{marginLeft:8},children:"BOX模式必需"})]}),rules:[{required:!0,message:"BOX模式必须选择4宫格广告"}],extra:"BOX模式的核心广告位，展示4个重要功能入口",children:e.jsx(i,{placeholder:"请选择4宫格广告",showSearch:!0,disabled:r,children:v.length>0?v.map(s=>e.jsxs(t,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id)):e.jsx(t,{value:"",disabled:!0,children:"暂无4宫格广告，请先创建"})})})})}),e.jsxs(o,{gutter:16,children:[e.jsx(n,{span:12,children:e.jsx(a.Item,{name:"splashPopupAdId",label:"开屏弹窗广告",extra:"APP启动时显示的弹窗广告",children:e.jsx(i,{placeholder:"请选择弹窗广告",allowClear:!0,showSearch:!0,disabled:r,children:f.length>0?f.map(s=>e.jsxs(t,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id)):e.jsx(t,{value:"",disabled:!0,children:"暂无弹窗广告，请先创建"})})})}),e.jsx(n,{span:12,children:e.jsx(a.Item,{name:"floatAdId",label:"浮点弹窗广告",extra:"页面浮动显示的弹窗广告",children:e.jsx(i,{placeholder:"请选择浮点广告",allowClear:!0,showSearch:!0,disabled:r,children:I.length>0?I.map(s=>e.jsxs(t,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id)):e.jsx(t,{value:"",disabled:!0,children:"暂无浮点广告，请先创建"})})})})]})]})},"ads"),x==="box"&&e.jsx(m,{tab:e.jsxs("span",{children:[e.jsx(U,{}),"游戏分类配置",e.jsx(S,{color:"green",style:{marginLeft:4},children:"BOX"})]}),children:e.jsx(u,{size:"small",title:"游戏分类组配置",extra:e.jsx(_,{title:"BOX模式至少需要4个游戏分类组，每个分类组至少4个游戏",children:e.jsx(k,{})}),children:e.jsx(a.Item,{name:"gameCategories",noStyle:!0,children:e.jsx(E,{applications:R,disabled:r})})})},"categories")]})]})};export{ae as default};
