// 修复超级管理员角色和权限分配的脚本
const { Client } = require('pg');

async function fixAdminRolePermissions() {
  const client = new Client({
    host: 'db.ytrftwscazjboxbwnrxp.supabase.co',
    port: 5432,
    database: 'postgres',
    user: 'postgres',
    password: 'inapp2backend2024!',
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('🚀 开始修复超级管理员角色和权限...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 检查现有角色
    console.log('\n📋 检查现有角色...');
    const existingRoles = await client.query(`
      SELECT id, name, code, status 
      FROM sys_roles 
      ORDER BY id;
    `);
    
    console.log('现有角色:');
    existingRoles.rows.forEach(role => {
      console.log(`  ID: ${role.id}, 名称: ${role.name}, 代码: ${role.code}, 状态: ${role.status}`);
    });

    // 2. 创建或更新超级管理员角色
    console.log('\n👑 创建/更新超级管理员角色...');
    let superAdminRoleId;
    
    const superAdminRole = await client.query(`
      SELECT id FROM sys_roles WHERE code = 'super_admin' OR name = '超级管理员';
    `);
    
    if (superAdminRole.rows.length === 0) {
      // 创建超级管理员角色
      const createResult = await client.query(`
        INSERT INTO sys_roles (name, code, remark, status)
        VALUES ('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 1)
        RETURNING id;
      `);
      superAdminRoleId = createResult.rows[0].id;
      console.log('✅ 超级管理员角色创建成功，ID:', superAdminRoleId);
    } else {
      superAdminRoleId = superAdminRole.rows[0].id;
      // 更新角色信息
      await client.query(`
        UPDATE sys_roles 
        SET name = '超级管理员', code = 'super_admin', remark = '系统超级管理员，拥有所有权限', status = 1
        WHERE id = $1;
      `, [superAdminRoleId]);
      console.log('✅ 超级管理员角色更新成功，ID:', superAdminRoleId);
    }

    // 3. 检查admin用户的角色分配
    console.log('\n👤 检查admin用户角色分配...');
    const adminUser = await client.query(`
      SELECT id, username, is_super_admin 
      FROM sys_users 
      WHERE username = 'admin';
    `);
    
    if (adminUser.rows.length === 0) {
      console.log('❌ 未找到admin用户');
      return;
    }
    
    const adminUserId = adminUser.rows[0].id;
    const isSuper = adminUser.rows[0].is_super_admin;
    console.log(`admin用户ID: ${adminUserId}, 超级管理员状态: ${isSuper}`);

    // 4. 分配超级管理员角色给admin用户
    console.log('\n🔗 分配超级管理员角色给admin用户...');
    await client.query(`
      INSERT INTO sys_user_roles (user_id, role_id)
      VALUES ($1, $2)
      ON CONFLICT (user_id, role_id) DO NOTHING;
    `, [adminUserId, superAdminRoleId]);
    console.log('✅ admin用户角色分配成功');

    // 5. 为超级管理员角色分配所有权限
    console.log('\n🔐 为超级管理员角色分配所有权限...');
    
    // 获取所有权限
    const allPermissions = await client.query(`
      SELECT id, code, name 
      FROM sys_permissions 
      WHERE status = 1
      ORDER BY code;
    `);
    
    console.log(`找到 ${allPermissions.rows.length} 个权限`);
    
    // 清除现有权限分配
    await client.query(`
      DELETE FROM sys_role_permissions WHERE role_id = $1;
    `, [superAdminRoleId]);
    
    // 分配所有权限
    for (const perm of allPermissions.rows) {
      await client.query(`
        INSERT INTO sys_role_permissions (role_id, permission_id)
        VALUES ($1, $2);
      `, [superAdminRoleId, perm.id]);
    }
    
    console.log('✅ 超级管理员权限分配完成');

    // 6. 验证配置
    console.log('\n📊 验证配置结果...');
    
    // 检查admin用户角色
    const adminRoles = await client.query(`
      SELECT r.name, r.code
      FROM sys_users u
      JOIN sys_user_roles ur ON u.id = ur.user_id
      JOIN sys_roles r ON ur.role_id = r.id
      WHERE u.username = 'admin';
    `);
    
    console.log('admin用户角色:');
    adminRoles.rows.forEach(role => {
      console.log(`  - ${role.name} (${role.code})`);
    });
    
    // 检查超级管理员权限数量
    const adminPermCount = await client.query(`
      SELECT COUNT(*) as count
      FROM sys_users u
      JOIN sys_user_roles ur ON u.id = ur.user_id
      JOIN sys_role_permissions rp ON ur.role_id = rp.role_id
      JOIN sys_permissions p ON rp.permission_id = p.id
      WHERE u.username = 'admin' AND p.status = 1;
    `);
    
    console.log(`admin用户权限数量: ${adminPermCount.rows[0].count}`);
    
    // 检查VIP权限
    const vipPermissions = await client.query(`
      SELECT p.code, p.name
      FROM sys_users u
      JOIN sys_user_roles ur ON u.id = ur.user_id
      JOIN sys_role_permissions rp ON ur.role_id = rp.role_id
      JOIN sys_permissions p ON rp.permission_id = p.id
      WHERE u.username = 'admin' AND p.code LIKE '%vip%' AND p.status = 1
      ORDER BY p.code;
    `);
    
    console.log('admin用户VIP权限:');
    vipPermissions.rows.forEach(perm => {
      console.log(`  - ${perm.code}: ${perm.name}`);
    });

    await client.end();
    console.log('\n🎉 超级管理员角色和权限修复完成！');

  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    console.error(error.stack);
    await client.end();
    throw error;
  }
}

// 执行修复
if (require.main === module) {
  fixAdminRolePermissions()
    .then(() => {
      console.log('✅ 修复完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 修复失败:', error);
      process.exit(1);
    });
}

module.exports = { fixAdminRolePermissions };
