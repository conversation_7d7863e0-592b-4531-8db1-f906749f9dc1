import { VipConfigService } from './vip-config.service';
export declare class TestVipConfigController {
    private readonly vipConfigService;
    constructor(vipConfigService: VipConfigService);
    findAll(): Promise<{
        code: number;
        message: string;
        result: any;
        debug: {
            timestamp: string;
            note: string;
            stack?: undefined;
        };
        error?: undefined;
    } | {
        code: number;
        message: string;
        error: any;
        debug: {
            timestamp: string;
            stack: any;
            note?: undefined;
        };
        result?: undefined;
    }>;
    findAllRaw(): Promise<{
        code: number;
        message: string;
        result: any;
        debug: {
            timestamp: string;
            count: any;
            note: string;
            stack?: undefined;
        };
        error?: undefined;
    } | {
        code: number;
        message: string;
        error: any;
        debug: {
            timestamp: string;
            stack: any;
            count?: undefined;
            note?: undefined;
        };
        result?: undefined;
    }>;
}
