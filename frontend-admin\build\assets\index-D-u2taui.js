import{u as y,j as s,H as m}from"./index-DD0gcXtR.js";import{c as C,d as T,e as w}from"./index-Cb-TAT0P.js";import{a}from"./react-BUTTOX-3.js";import{B}from"./index-C1Do55qr.js";import{B as W}from"./index-DUsnaVFw.js";import{u as f}from"./useMutation-Dm45oaMR.js";import{Detail as D}from"./detail-BNPkxWAr.js";import{getConstantColumns as R}from"./constants-2iLWPyJk.js";import{aD as E,d as F,bH as M,s as i}from"./antd-FyCAPQKa.js";import"./Table-Blekjt7O.js";import"./index-DwiaqWIr.js";import"./index-BDysGMcU.js";import"./BaseForm-CUW86psc.js";import"./index-DhoWj0AL.js";import"./index-BhoZk0D4.js";import"./index-Bo3BoCEt.js";function Y(){const{t}=y(),u=a.useRef(),[h,r]=a.useState(!1),[b,d]=a.useState(""),[x,l]=a.useState(null),p=f({mutationFn:w,onSuccess:()=>{i.success(t("common.deleteSuccess")),c()},onError:o=>{i.error(o.message||t("common.deleteFailed"))}}),k=f({mutationFn:({id:o,status:e})=>T(o,e),onSuccess:()=>{i.success(t("common.updateSuccess")),c()},onError:o=>{i.error(o.message||t("common.updateFailed"))}}),c=()=>{var o;(o=u.current)==null||o.reload()},g=o=>{r(o),o||l(null)},j=[...R(t),{title:t("common.action"),valueType:"option",key:"option",width:250,fixed:"right",render:(o,e,z,P)=>{const n=[];return n.push(s.jsx(m,{type:"link",size:"small",onClick:()=>{l(e),r(!0),d(t("workbook.editWorkbook"))},children:t("common.edit")},"edit")),n.push(s.jsx(m,{type:"link",size:"small",onClick:()=>{const S=e.status===1?0:1;k.mutate({id:e.id,status:S})},loading:k.isPending,children:e.status===1?t("common.disable"):t("common.enable")},"toggle-status")),n.push(s.jsx(E,{title:t("common.deleteConfirm"),onConfirm:()=>p.mutate(e.id),okText:t("common.confirm"),cancelText:t("common.cancel"),children:s.jsx(m,{type:"link",size:"small",danger:!0,loading:p.isPending,children:t("common.delete")})},"delete")),n}}];return s.jsxs(B,{className:"h-full",children:[s.jsx(W,{columns:j,actionRef:u,request:async o=>{const e=await C(o);return{...e,data:e.result.list,total:e.result.total}},headerTitle:t("workbook.title"),toolBarRender:()=>[s.jsx(F,{icon:s.jsx(M,{}),type:"primary",onClick:()=>{l(null),r(!0),d(t("workbook.addWorkbook"))},children:t("common.add")},"add-workbook")],rowKey:"id",search:{labelWidth:"auto"}}),s.jsx(D,{title:b,open:h,onCloseChange:g,detailData:x,refreshTable:c})]})}export{Y as default};
