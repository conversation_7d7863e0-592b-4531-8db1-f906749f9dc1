async function testVipV2Api() {
  try {
    console.log('🧪 测试VIP V2 API...');

    // 测试获取VIP V2配置列表
    console.log('📋 测试获取VIP V2配置列表...');
    const response = await fetch('http://localhost:3000/api/config/vip-v2', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ API响应状态:', response.status);
    const data = await response.json();
    console.log('📊 返回数据:', JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
  }
}

// 执行测试
if (require.main === module) {
  testVipV2Api()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testVipV2Api };
