// 测试TypeORM关系查询的脚本
const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testTypeORMRelations() {
  try {
    console.log('🚀 启动NestJS应用...');
    const app = await NestFactory.createApplicationContext(AppModule);
    console.log('✅ 应用启动成功');

    // 获取UserRepository
    const { DataSource } = require('typeorm');
    const { SysUser } = require('../dist/system/entities/sys-user.entity');

    console.log('\n🔍 测试TypeORM用户角色查询...');

    // 获取数据源
    const dataSource = app.get(DataSource);

    // 方法1：使用findOne with relations
    console.log('\n📋 方法1: findOne with relations');
    const userRepository = dataSource.getRepository(SysUser);
    const user1 = await userRepository.findOne({
      where: { username: 'admin' },
      relations: ['roles'],
      select: ['id', 'username', 'email', 'status', 'isSuperAdmin']
    });
    
    console.log('findOne结果:');
    if (user1) {
      console.log(`  用户: ${user1.username} (ID: ${user1.id})`);
      console.log(`  超级管理员: ${user1.isSuperAdmin}`);
      console.log(`  角色数量: ${user1.roles ? user1.roles.length : 0}`);
      if (user1.roles && user1.roles.length > 0) {
        user1.roles.forEach(role => {
          console.log(`    角色: ${role.name} (${role.code})`);
        });
      } else {
        console.log('    ❌ 无角色数据');
      }
    } else {
      console.log('  ❌ 未找到用户');
    }

    // 方法2：使用createQueryBuilder
    console.log('\n📋 方法2: createQueryBuilder');
    const user2 = await userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where('user.username = :username', { username: 'admin' })
      .select([
        'user.id',
        'user.username',
        'user.email',
        'user.status',
        'user.isSuperAdmin',
        'role.id',
        'role.name',
        'role.code'
      ])
      .getOne();
    
    console.log('createQueryBuilder结果:');
    if (user2) {
      console.log(`  用户: ${user2.username} (ID: ${user2.id})`);
      console.log(`  超级管理员: ${user2.isSuperAdmin}`);
      console.log(`  角色数量: ${user2.roles ? user2.roles.length : 0}`);
      if (user2.roles && user2.roles.length > 0) {
        user2.roles.forEach(role => {
          console.log(`    角色: ${role.name} (${role.code})`);
        });
      } else {
        console.log('    ❌ 无角色数据');
      }
    } else {
      console.log('  ❌ 未找到用户');
    }

    // 方法3：使用AuthService的方法
    console.log('\n📋 方法3: 使用AuthService');
    const authService = app.get('SystemAuthService');
    const user3 = await authService.validateUser('admin', 'admin123');
    
    console.log('AuthService.validateUser结果:');
    if (user3) {
      console.log(`  用户: ${user3.username} (ID: ${user3.id})`);
      console.log(`  超级管理员: ${user3.isSuperAdmin}`);
      console.log(`  角色数量: ${user3.roles ? user3.roles.length : 0}`);
      if (user3.roles && user3.roles.length > 0) {
        user3.roles.forEach(role => {
          console.log(`    角色: ${role.name} (${role.code})`);
        });
      } else {
        console.log('    ❌ 无角色数据');
      }
    } else {
      console.log('  ❌ 验证失败');
    }

    // 方法4：直接查询关联表
    console.log('\n📋 方法4: 直接查询关联表');
    const rawResult = await dataSource.query(`
      SELECT 
        u.id, u.username, u.is_super_admin,
        r.id as role_id, r.name as role_name, r.code as role_code
      FROM sys_users u
      LEFT JOIN sys_user_roles ur ON u.id = ur.user_id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE u.username = $1
    `, ['admin']);
    
    console.log('原生SQL查询结果:');
    rawResult.forEach(row => {
      console.log(`  用户: ${row.username} (ID: ${row.id}, 超级管理员: ${row.is_super_admin})`);
      if (row.role_id) {
        console.log(`    角色: ${row.role_name} (ID: ${row.role_id}, 代码: ${row.role_code})`);
      }
    });

    await app.close();
    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 执行测试
if (require.main === module) {
  testTypeORMRelations()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testTypeORMRelations };
