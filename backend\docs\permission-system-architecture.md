# InApp2 权限系统架构文档

## 概述

InApp2 游戏管理后台采用基于角色的访问控制（RBAC）模型，结合超级管理员机制，实现了完整的权限管理体系。系统支持前后端分离的权限验证，提供灵活的菜单管理和动态路由生成功能。

## 系统架构

### 权限模型设计

```
用户 (sys_users) 
    ↓ (多对多关系)
角色 (sys_roles)
    ↓ (多对多关系)  
权限 (sys_permissions)
    ↓ (关联关系)
菜单 (sys_menus)
```

### 核心特性

1. **超级管理员机制**: 通过 `is_super_admin` 字段标识，拥有所有权限，绕过角色权限检查
2. **动态路由生成**: 支持前端和后端两种路由生成模式
3. **细粒度权限控制**: 支持菜单级、按钮级、API级权限控制
4. **权限继承**: 子菜单可继承父菜单权限
5. **实时权限验证**: 前后端双重权限验证机制

## 数据库设计

### 1. 用户表 (sys_users)

```sql
CREATE TABLE sys_users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL,
  password VARCHAR(255) NOT NULL,
  phone_number VARCHAR(20),
  avatar VARCHAR(500),
  description VARCHAR(200),
  status INTEGER DEFAULT 1,                    -- 0:禁用, 1:启用
  is_super_admin BOOLEAN DEFAULT false,        -- 超级管理员标识
  last_login_time TIMESTAMPTZ,
  create_time TIMESTAMPTZ DEFAULT NOW(),
  update_time TIMESTAMPTZ DEFAULT NOW()
);
```

**关键字段说明**:
- `is_super_admin`: 超级管理员标识，为 true 时拥有所有权限
- `status`: 用户状态，0-禁用，1-启用

### 2. 角色表 (sys_roles)

```sql
CREATE TABLE sys_roles (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL,                   -- 角色名称
  code VARCHAR(50) NOT NULL UNIQUE,           -- 角色代码
  remark VARCHAR(200),                         -- 备注
  status INTEGER DEFAULT 1,                   -- 0:禁用, 1:启用
  create_time TIMESTAMPTZ DEFAULT NOW(),
  update_time TIMESTAMPTZ DEFAULT NOW()
);
```

### 3. 权限表 (sys_permissions)

```sql
CREATE TABLE sys_permissions (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,                 -- 权限名称
  code VARCHAR(100) NOT NULL UNIQUE,         -- 权限代码
  description VARCHAR(200),                   -- 权限描述
  type VARCHAR(20) DEFAULT 'button',         -- 权限类型: menu, button, api
  status INTEGER DEFAULT 1,                  -- 0:禁用, 1:启用
  create_time TIMESTAMPTZ DEFAULT NOW(),
  update_time TIMESTAMPTZ DEFAULT NOW()
);
```

**权限类型说明**:
- `menu`: 菜单权限，控制菜单显示
- `button`: 按钮权限，控制页面内按钮显示
- `api`: API权限，控制接口访问

### 4. 菜单表 (sys_menus)

```sql
CREATE TABLE sys_menus (
  id SERIAL PRIMARY KEY,
  title VARCHAR(100) NOT NULL,               -- 菜单标题
  path VARCHAR(200) NOT NULL,                -- 路由路径
  icon VARCHAR(100),                         -- 菜单图标
  parent_id INTEGER REFERENCES sys_menus(id), -- 父菜单ID
  "order" INTEGER DEFAULT 0,                 -- 排序
  type INTEGER DEFAULT 1,                    -- 0:父菜单, 1:功能菜单, 2:按钮
  status INTEGER DEFAULT 1,                  -- 0:禁用, 1:启用
  component VARCHAR(200),                    -- 前端组件路径
  meta JSONB,                               -- 元数据
  permission_code VARCHAR(100),             -- 关联权限代码
  button_permissions JSONB,                 -- 按钮权限数组
  create_time TIMESTAMPTZ DEFAULT NOW(),
  update_time TIMESTAMPTZ DEFAULT NOW()
);
```

**关键字段说明**:
- `component`: 前端组件路径，如 `config/vip-v2`
- `permission_code`: 关联的权限代码，如 `config:vip:view`
- `button_permissions`: 该菜单关联的按钮权限代码数组

### 5. 关联表

```sql
-- 用户角色关联表
CREATE TABLE sys_user_roles (
  user_id INTEGER NOT NULL REFERENCES sys_users(id) ON DELETE CASCADE,
  role_id INTEGER NOT NULL REFERENCES sys_roles(id) ON DELETE CASCADE,
  create_time TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE sys_role_permissions (
  role_id INTEGER NOT NULL REFERENCES sys_roles(id) ON DELETE CASCADE,
  permission_id INTEGER NOT NULL REFERENCES sys_permissions(id) ON DELETE CASCADE,
  create_time TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (role_id, permission_id)
);
```

## 后端权限实现

### 1. 权限守卫 (Guards)

#### JWT认证守卫
```typescript
@Injectable()
export class SystemJwtAuthGuard extends AuthGuard('system-jwt') {
  // JWT token验证
}
```

#### 超级管理员守卫
```typescript
@Injectable()
export class SuperAdminGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    // 查询用户是否为超级管理员
    const sysUser = await this.userRepository.findOne({
      where: { id: user.userId },
      select: ['id', 'isSuperAdmin'],
    });
    
    return sysUser?.isSuperAdmin || false;
  }
}
```

### 2. 权限装饰器

```typescript
// 权限装饰器
export const Permissions = (...permissions: string[]) => 
  SetMetadata('permissions', permissions);

// 使用示例
@Permissions('system:user:view')
@Get('list')
async findAll() {
  // 实现逻辑
}
```

### 3. 菜单服务权限逻辑

```typescript
// 根据用户角色获取菜单
async getMenusByRoles(roleCodes: string[], isSuperAdmin: boolean = false, userPermissions: string[] = []) {
  const queryBuilder = this.menuRepository
    .createQueryBuilder('menu')
    .where('menu.status = :status', { status: 1 })
    .orderBy('menu.order', 'ASC');

  // 超级管理员可以看到所有菜单
  if (isSuperAdmin) {
    const menus = await queryBuilder.getMany();
    return this.buildMenuTree(menus);
  }

  // 非超级管理员需要根据权限过滤菜单
  if (userPermissions.length > 0) {
    queryBuilder.andWhere(
      '(menu.permissionCode IS NULL OR menu.permissionCode = :emptyString OR menu.permissionCode IN (:...permissions))',
      { emptyString: '', permissions: userPermissions }
    );
  }

  const menus = await queryBuilder.getMany();
  return this.buildMenuTree(menus);
}
```

### 4. 权限获取逻辑

```typescript
// 获取用户权限列表
async getUserPermissions(userRoles: string[], isSuperAdmin: boolean = false): Promise<string[]> {
  // 超级管理员返回所有权限
  if (isSuperAdmin) {
    const allPermissions = await this.permissionRepository.find({
      where: { status: 1 },
      select: ['code'],
    });
    return allPermissions.map(p => p.code);
  }

  // 普通用户根据角色获取权限
  const permissions = await this.permissionRepository
    .createQueryBuilder('permission')
    .innerJoin('sys_role_permissions', 'rp', 'rp.permission_id = permission.id')
    .innerJoin('sys_roles', 'role', 'role.id = rp.role_id')
    .where('role.code IN (:...roleCodes)', { roleCodes: userRoles })
    .andWhere('permission.status = :status', { status: 1 })
    .select('permission.code')
    .getMany();

  return permissions.map(p => p.code);
}
```

## 前端权限实现

### 1. 权限守卫组件 (AuthGuard)

AuthGuard 是前端权限系统的核心，负责：
- 用户登录状态检查
- 权限信息获取
- 动态路由生成
- 路由权限验证

```typescript
export function AuthGuard({ children }: { children: ReactNode }) {
  const { pathname } = useLocation();
  const { isLogin, token } = useAuthStore();
  const { isAuthorized, isSuperAdmin, userRoles } = useUserStore();
  const { setAccessStore, isAccessChecked, routeList } = useAccessStore();

  // 核心权限获取逻辑
  const fetchUserInfoAndRoutes = useCallback(async () => {
    const promises = [];

    // 获取用户信息
    promises.push(fetchUserInfo());

    // 获取后端路由（如果启用）
    if (enableBackendAccess && isSendRoutingRequest) {
      promises.push(fetchAsyncRoutes());
    }

    const results = await Promise.allSettled(promises);

    // 生成路由
    const routes = [];
    if (enableBackendAccess) {
      routes.push(...await generateRoutesFromBackend(menuData));
    }
    if (enableFrontendAccess) {
      routes.push(...generateRoutesByFrontend(accessRoutes, userRoles));
    }

    // 设置权限路由
    setAccessStore(routes);
  }, []);

  // 路由权限验证
  const routeRoles = currentRoute?.handle?.roles;
  const hasRoutePermission = isSuperAdmin || userRoles.some(role => routeRoles?.includes(role));

  if (routeRoles && routeRoles.length && !hasRoutePermission) {
    return <Navigate to={exception403Path} replace />;
  }

  return children;
}
```

### 2. 权限Hook (useAccess)

提供权限判断功能：

```typescript
export function useAccess() {
  const { roles: userRoles, isSuperAdmin } = useUserStore();
  const currentRoute = useMatches()[useMatches().length - 1];

  // 基于权限代码的权限判断
  const hasAccessByCodes = (permission?: string | Array<string>) => {
    // 超级管理员拥有所有权限
    if (isSuperAdmin) return true;

    if (!permission) return false;

    // 从当前路由获取权限配置
    const metaAuth = currentRoute?.handle?.permissions;
    if (!metaAuth || !Array.isArray(metaAuth)) return false;

    permission = isString(permission) ? [permission] : permission;
    return permission.some(code => metaAuth.includes(code));
  };

  // 基于角色的权限判断
  const hasAccessByRoles = (roles?: string | Array<string>) => {
    // 超级管理员拥有所有角色权限
    if (isSuperAdmin) return true;

    if (!roles || !userRoles) return false;

    roles = isString(roles) ? [roles] : roles;
    return userRoles.some(item => roles.includes(item.toLowerCase()));
  };

  return { hasAccessByCodes, hasAccessByRoles };
}
```

### 3. 权限控制组件 (AccessControl)

用于页面元素的权限控制：

```typescript
interface AccessControlProps {
  type?: "code" | "role";
  codes?: string | string[];
  children?: ReactNode;
  fallback?: ReactNode;
}

export function AccessControl({ type = "code", codes, children, fallback }: AccessControlProps) {
  const { hasAccessByCodes, hasAccessByRoles } = useAccess();

  if (!children) return null;

  if (type === "code") {
    return hasAccessByCodes(codes) ? children : fallback;
  }

  if (type === "role") {
    return hasAccessByRoles(codes) ? children : fallback;
  }

  return fallback;
}
```

### 4. 动态路由生成

#### 后端路由生成
```typescript
export async function generateRoutesFromBackend(backendRoutes: Array<AppRouteRecordRaw>) {
  const pageModules = import.meta.glob([
    "/src/pages/**/*.tsx",
    "!/src/pages/exception/**/*.tsx",
  ]);

  const loadRouteComponent = async (route: AppRouteRecordRaw, componentPath: string) => {
    const normalizedPath = componentPath.startsWith('/') ? componentPath : `/${componentPath}`;
    const modulePath = `/src/pages${normalizedPath}/index.tsx`;
    const moduleIndex = pageModulePaths.findIndex(path => path === modulePath);

    if (moduleIndex !== -1) {
      const lazyComponent = pageModules[pageModulePaths[moduleIndex]];
      route.Component = lazy(lazyComponent as any);
    } else {
      route.Component = ExceptionUnknownComponent;
    }
  };

  // 递归处理路由
  const processRoutes = async (routes: AppRouteRecordRaw[]) => {
    for (const route of routes) {
      if (route.component) {
        await loadRouteComponent(route, route.component);
      }
      if (route.children) {
        await processRoutes(route.children);
      }
    }
  };

  await processRoutes(backendRoutes);
  return addRouteIdByPath(backendRoutes);
}
```

#### 前端路由生成
```typescript
export function generateRoutesByFrontend(routes: AppRouteRecordRaw[], roles: string[]) {
  // 根据角色过滤路由
  const finalRoutes = filterTree(routes, (route) => {
    return hasAuthority(route, roles);
  });

  return finalRoutes;
}

function hasAuthority(route: AppRouteRecordRaw, accesses: string[]) {
  const authority = route.handle?.roles;
  if (!authority) return true;
  return accesses.some(value => authority.includes(value));
}
```

## 权限验证流程

### 1. 用户登录流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库

    U->>F: 输入用户名密码
    F->>B: POST /api/system/auth/login
    B->>D: 验证用户凭据
    D-->>B: 返回用户信息
    B->>B: 生成JWT Token
    B-->>F: 返回Token和基本信息
    F->>F: 存储Token到localStorage
    F->>F: 设置登录状态
```

### 2. 权限获取流程

```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    participant D as 数据库

    F->>B: GET /api/system/user/info (携带Token)
    B->>B: 验证JWT Token
    B->>D: 查询用户信息和角色
    D-->>B: 返回用户、角色信息
    B->>D: 根据角色查询权限
    D-->>B: 返回权限列表
    B->>D: 根据权限查询菜单
    D-->>B: 返回菜单树
    B-->>F: 返回完整用户权限信息
    F->>F: 生成动态路由
    F->>F: 设置权限状态
```

### 3. 页面访问验证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant AG as AuthGuard
    participant AS as AccessStore
    participant US as UserStore

    U->>AG: 访问页面
    AG->>AG: 检查是否在白名单
    alt 在白名单
        AG-->>U: 直接渲染页面
    else 不在白名单
        AG->>US: 检查登录状态
        alt 未登录
            AG-->>U: 重定向到登录页
        else 已登录
            AG->>AS: 检查权限是否已获取
            alt 权限未获取
                AG->>AG: 获取用户信息和权限
                AG->>AS: 生成动态路由
            end
            AG->>AG: 验证路由权限
            alt 有权限
                AG-->>U: 渲染页面
            else 无权限
                AG-->>U: 重定向到403页面
            end
        end
    end
```

## 权限配置示例

### 1. 菜单权限配置

```sql
-- VIP配置菜单
INSERT INTO sys_menus (title, path, icon, parent_id, "order", type, status, component, permission_code, button_permissions)
VALUES (
  'VIP配置',
  '/config/vip',
  'crown',
  (SELECT id FROM sys_menus WHERE path = '/config'),
  1,
  1,
  1,
  'config/vip-v2',
  'config:vip:view',
  '["config:vip:add", "config:vip:edit", "config:vip:delete"]'
);
```

### 2. 权限代码规范

权限代码采用三级结构：`模块:功能:操作`

```
system:user:view     # 系统管理-用户管理-查看
system:user:add      # 系统管理-用户管理-新增
system:user:edit     # 系统管理-用户管理-编辑
system:user:delete   # 系统管理-用户管理-删除
config:vip:view      # 配置管理-VIP配置-查看
config:vip:edit      # 配置管理-VIP配置-编辑
```

### 3. 前端路由权限配置

```typescript
const routes: AppRouteRecordRaw[] = [
  {
    path: '/config/vip',
    Component: lazy(() => import('#src/pages/config/vip-v2')),
    handle: {
      title: 'VIP配置',
      permissions: ['config:vip:view'],
      roles: ['admin', 'config_manager']
    }
  }
];
```

### 4. 页面按钮权限控制

```typescript
// 使用AccessControl组件
<AccessControl codes="config:vip:add">
  <Button type="primary">新增VIP等级</Button>
</AccessControl>

// 使用useAccess Hook
const { hasAccessByCodes } = useAccess();

{hasAccessByCodes('config:vip:edit') && (
  <Button>编辑</Button>
)}
```

## 超级管理员机制

### 1. 超级管理员特权

- **绕过所有权限检查**: 前后端都会检查 `is_super_admin` 字段
- **查看所有菜单**: 不受权限限制，可以访问所有功能
- **管理所有用户**: 包括其他管理员用户
- **系统配置权限**: 可以修改系统核心配置

### 2. 超级管理员判断逻辑

#### 后端判断
```typescript
// 菜单服务中的超级管理员判断
if (isSuperAdmin) {
  console.log(`[MENU_SERVICE] 超级管理员，返回所有菜单`);
  const menus = await queryBuilder.getMany();
  return this.buildMenuTree(menus);
}

// 权限服务中的超级管理员判断
if (isSuperAdmin) {
  const allPermissions = await this.permissionRepository.find({
    where: { status: 1 },
    select: ['code'],
  });
  return allPermissions.map(p => p.code);
}
```

#### 前端判断
```typescript
// useAccess Hook中的超级管理员判断
const hasAccessByCodes = (permission?: string | Array<string>) => {
  // 超级管理员拥有所有权限
  if (isSuperAdmin) return true;
  // ... 其他权限判断逻辑
};

// AuthGuard中的超级管理员判断
const hasRoutePermission = isSuperAdmin || userRoles.some(role => routeRoles?.includes(role));
```

## 安全考虑

### 1. 前后端双重验证

- **前端权限控制**: 主要用于UI显示控制，提升用户体验
- **后端权限验证**: 真正的安全边界，所有API都必须进行权限验证

### 2. Token安全

- **JWT过期机制**: 设置合理的过期时间
- **Token刷新**: 支持无感知的Token刷新
- **安全存储**: Token存储在localStorage中，注意XSS防护

### 3. 权限最小化原则

- **默认无权限**: 新用户默认没有任何权限
- **显式授权**: 所有权限都需要显式分配
- **定期审计**: 定期检查用户权限分配情况

## 常见问题排查

### 1. 用户无法访问某个页面

检查步骤：
1. 确认用户是否有对应角色
2. 确认角色是否有对应权限
3. 确认菜单是否配置了正确的权限代码
4. 确认前端路由是否配置了正确的权限

### 2. 菜单不显示

检查步骤：
1. 确认菜单状态是否为启用
2. 确认用户是否有菜单对应的权限
3. 确认菜单的父子关系是否正确
4. 确认前端组件路径是否正确

### 3. 超级管理员权限异常

检查步骤：
1. 确认数据库中 `is_super_admin` 字段值
2. 确认前端UserStore中的 `isSuperAdmin` 状态
3. 确认JWT Token中是否包含正确的用户信息

## 总结

InApp2权限系统通过RBAC模型和超级管理员机制，实现了灵活、安全、易维护的权限管理体系。系统支持细粒度的权限控制，能够满足复杂业务场景的权限需求。通过前后端双重验证，确保了系统的安全性，同时提供了良好的用户体验。
```
