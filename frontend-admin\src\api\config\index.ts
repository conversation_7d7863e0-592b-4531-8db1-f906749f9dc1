import { request } from "#src/utils";

// VIP配置相关类型
export interface VipConfig {
  id: number;
  vipLevel: number;
  levelName: string;
  requiredPoints: number;
  balanceRatio: number;
  cashRatio: number;
  goldRatio: number;
  dailyGoldReward: number;
  status: number;
  createTime: string;
  updateTime: string;
  creator?: {
    id: number;
    username: string;
  };
  updater?: {
    id: number;
    username: string;
  };
}

// 会员卡配置相关类型
export interface MembershipCardConfig {
  id: number;
  cardType: string;
  cardName: string;
  price: number;
  description: string;
  dailyGoldBase: number;
  dailyGoldActivityRatio: number;
  cashDiscountBase: number;
  cashDiscountActivity: number;
  activityStartTime: string;
  activityEndTime: string;
  status: number;
  createTime: string;
  updateTime: string;
  creator?: {
    id: number;
    username: string;
  };
  updater?: {
    id: number;
    username: string;
  };
}

// 金币充值配置相关类型
export interface GoldRechargeConfig {
  id: number;
  tierName: string;
  goldAmount: number;
  price: number;
  activityBonusGold: number;
  activityStartTime: string;
  activityEndTime: string;
  sortOrder: number;
  status: number;
  createTime: string;
  updateTime: string;
}

// 余额充值配置相关类型
export interface BalanceRechargeConfig {
  id: number;
  tierName: string;
  rechargeAmount: number;
  activityBonusAmount: number;
  activityStartTime: string;
  activityEndTime: string;
  sortOrder: number;
  status: number;
  createTime: string;
  updateTime: string;
}

// 余额充值限制类型
export interface BalanceRechargeLimit {
  id: number;
  minAmount: number;
  maxAmount: number;
  status: number;
  limitDescription: string;
}

// 图片跳转项类型
export interface ImageItem {
  imageUrl: string;
  jumpType: number;
  jumpTarget: string;
  title?: string;
  description?: string;
}

// 广告配置相关类型
export interface AdConfig {
  id: number;
  adIdentifier: string;
  adType: number;
  adTypeName: string;
  title: string;
  imageItems: ImageItem[];
  sortOrder: number;
  status: number;
  remark?: string;
  createTime: string;
  updateTime: string;
  creator?: {
    id: number;
    username: string;
  };
  updater?: {
    id: number;
    username: string;
  };
}

// 广告配置查询参数
export interface AdConfigQueryParams {
  page?: number;
  pageSize?: number;
  adType?: number;
  jumpType?: number;
  status?: number;
  title?: string;
  adIdentifier?: string;
}

// 广告配置创建/更新参数
export interface AdConfigFormData {
  adIdentifier: string;
  adType: number;
  title: string;
  imageItems: ImageItem[];
  sortOrder?: number;
  status?: number;
  remark?: string;
}

// VIP配置分页响应类型
export interface VipConfigListResponse {
  list: VipConfig[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// VIP配置API
export function fetchVipConfigs() {
  return request
    .get("config/vip")
    .json<ApiResponse<VipConfigListResponse>>();
}

export function fetchUpdateVipConfig(id: number, data: Partial<VipConfig>) {
  return request
    .patch(`config/vip/${id}`, {
      json: data,
    })
    .json<ApiResponse<VipConfig>>();
}

export function fetchRecalculateAllVip() {
  return request
    .post("config/vip/recalculate-all")
    .json<ApiResponse<{ processed: number; upgraded: number }>>();
}

// ==================== VIP配置V2 API (经济权益系统) ====================

// VIP配置V2类型定义
export interface VipConfigV2 {
  id: number;
  vipLevel: number;
  levelName: string;
  strategicPosition: string;
  requiredExp: number;
  realMoneyRechargeExpRatio: number;
  realMoneyFlowExpRatio: number;
  goldPaymentExpRatio: number;
  goldFlowExpRatio: number;
  dailyTaskExpMin: number;
  dailyTaskExpMax: number;
  dailyGoldReward: number;
  buybackEnabled: boolean;
  buybackRate: number;
  c2cFeeRate: number;
  rakebackRate: number;
  withdrawalFeeRate: number;
  dailyWithdrawalLimit: number;
  withdrawalPriority: number;
  taskUnlockRequired: boolean;
  taskUnlockGamesRequired: number;
  status: number;
  remark: string;
  createTime: string;
  updateTime: string;
  creator?: {
    id: number;
    username: string;
  };
  updater?: {
    id: number;
    username: string;
  };
}

// VIP配置V2分页响应类型
export interface VipConfigV2ListResponse {
  list: VipConfigV2[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// VIP配置V2查询参数
export interface VipConfigV2QueryParams {
  page?: number;
  pageSize?: number;
  vipLevel?: number;
  status?: number;
  levelName?: string;
  buybackEnabled?: boolean;
}

// VIP配置V2 API
export function fetchVipConfigsV2(params?: VipConfigV2QueryParams) {
  return request
    .get("config/vip-v2", { searchParams: params })
    .json<ApiResponse<VipConfigV2ListResponse>>();
}

export function fetchVipConfigV2ById(id: number) {
  return request
    .get(`config/vip-v2/${id}`)
    .json<ApiResponse<VipConfigV2>>();
}

export function fetchVipConfigV2ByLevel(vipLevel: number) {
  return request
    .get(`config/vip-v2/level/${vipLevel}`)
    .json<ApiResponse<VipConfigV2>>();
}

export function createVipConfigV2(data: Partial<VipConfigV2>) {
  return request
    .post("config/vip-v2", { json: data })
    .json<ApiResponse<VipConfigV2>>();
}

export function updateVipConfigV2(id: number, data: Partial<VipConfigV2>) {
  return request
    .patch(`config/vip-v2/${id}`, { json: data })
    .json<ApiResponse<VipConfigV2>>();
}

export function deleteVipConfigV2(id: number) {
  return request
    .delete(`config/vip-v2/${id}`)
    .json<ApiResponse<{ message: string }>>();
}

export function fetchVipLevelComparison() {
  return request
    .get("config/vip-v2/comparison")
    .json<ApiResponse<any[]>>();
}

export function fetchRecalculateAllVipV2() {
  return request
    .post("config/vip-v2/recalculate-all")
    .json<ApiResponse<{ processed: number; upgraded: number; downgraded: number }>>();
}

// 用户VIP相关API
export function fetchUserVipBenefits(userId: number) {
  return request
    .get(`config/vip-v2/user/${userId}/benefits`)
    .json<ApiResponse<any>>();
}

export function addUserExp(userId: number, data: {
  expType: 'real_money_recharge' | 'real_money_flow' | 'gold_payment' | 'gold_flow' | 'daily_task';
  amount: number;
  description?: string;
}) {
  return request
    .post(`config/vip-v2/user/${userId}/add-exp`, { json: data })
    .json<ApiResponse<{ expGained: number; expType: string; amount: number }>>();
}

// 权益验证API
export function checkUserBuybackPermission(userId: number) {
  return request
    .get(`config/vip-v2/user/${userId}/check-buyback`)
    .json<ApiResponse<{ hasPermission: boolean; buybackRate: number; vipLevel: number }>>();
}

export function checkUserWithdrawalPermission(userId: number) {
  return request
    .get(`config/vip-v2/user/${userId}/check-withdrawal`)
    .json<ApiResponse<{ feeRate: number; dailyLimit: number; priority: number; vipLevel: number }>>();
}

export function checkUserC2CPermission(userId: number) {
  return request
    .get(`config/vip-v2/user/${userId}/check-c2c`)
    .json<ApiResponse<{ feeRate: number; vipLevel: number }>>();
}

// 会员卡配置分页响应类型
export interface MembershipCardConfigListResponse {
  list: MembershipCardConfig[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 会员卡配置API
export function fetchMembershipCardConfigs() {
  return request
    .get("config/membership-cards")
    .json<ApiResponse<MembershipCardConfigListResponse>>();
}

export function fetchUpdateMembershipCardConfig(id: number, data: Partial<MembershipCardConfig>) {
  return request
    .patch(`config/membership-cards/${id}`, {
      json: data,
    })
    .json<ApiResponse<MembershipCardConfig>>();
}

// 金币充值配置分页响应类型
export interface GoldRechargeConfigListResponse {
  list: GoldRechargeConfig[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 金币充值配置API
export function fetchGoldRechargeConfigs() {
  return request
    .get("config/recharge/gold")
    .json<ApiResponse<GoldRechargeConfigListResponse>>();
}

export function fetchUpdateGoldRechargeConfig(id: number, data: Partial<GoldRechargeConfig>) {
  return request
    .patch(`config/recharge/gold/${id}`, {
      json: data,
    })
    .json<ApiResponse<GoldRechargeConfig>>();
}

// 余额充值配置分页响应类型
export interface BalanceRechargeConfigListResponse {
  list: BalanceRechargeConfig[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 余额充值配置API
export function fetchBalanceRechargeConfigs() {
  return request
    .get("config/recharge/balance")
    .json<ApiResponse<BalanceRechargeConfigListResponse>>();
}

export function fetchUpdateBalanceRechargeConfig(id: number, data: Partial<BalanceRechargeConfig>) {
  return request
    .patch(`config/recharge/balance/${id}`, {
      json: data,
    })
    .json<ApiResponse<BalanceRechargeConfig>>();
}

// 余额充值限制API
export function fetchBalanceRechargeLimit() {
  return request
    .get("config/recharge/balance/limits")
    .json<ApiResponse<BalanceRechargeLimit>>();
}

export function fetchUpdateBalanceRechargeLimit(data: Partial<BalanceRechargeLimit>) {
  return request
    .patch("config/recharge/balance/limits", {
      json: data,
    })
    .json<ApiResponse<BalanceRechargeLimit>>();
}

// 广告配置分页响应类型
export interface AdConfigListResponse {
  list: AdConfig[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 广告配置API
export function fetchAdConfigs(params?: AdConfigQueryParams) {
  return request
    .get("config/ads", {
      searchParams: params as Record<string, string | number | boolean>,
    })
    .json<ApiResponse<AdConfigListResponse>>();
}

export function fetchAdConfig(id: number) {
  return request
    .get(`config/ads/${id}`)
    .json<ApiResponse<AdConfig>>();
}

export function fetchCreateAdConfig(data: AdConfigFormData) {
  return request
    .post("config/ads", {
      json: data,
    })
    .json<ApiResponse<AdConfig>>();
}

export function fetchUpdateAdConfig(id: number, data: Partial<AdConfigFormData>) {
  return request
    .patch(`config/ads/${id}`, {
      json: data,
    })
    .json<ApiResponse<AdConfig>>();
}

export function fetchDeleteAdConfig(id: number) {
  return request
    .delete(`config/ads/${id}`)
    .json<ApiResponse<{ message: string }>>();
}

export function fetchToggleAdConfigStatus(id: number) {
  return request
    .patch(`config/ads/${id}/toggle-status`)
    .json<ApiResponse<{ id: number; status: number; message: string }>>();
}

export function fetchUpdateAdConfigSort(updates: Array<{ id: number; sortOrder: number }>) {
  return request
    .post("config/ads/batch-sort", {
      json: updates,
    })
    .json<ApiResponse<{ message: string }>>();
}

export function fetchAdConfigStats() {
  return request
    .get("config/ads/stats")
    .json<ApiResponse<Array<{
      adType: number;
      adTypeName: string;
      total: number;
      enabled: number;
      disabled: number;
    }>>>();
}

// ==================== APP首页配置相关 ====================

// 多语言标题接口
export interface CategoryTitle {
  'zh-CN': string;
  'en-US': string;
  [key: string]: string;
}

// 游戏信息接口
export interface GameInfo {
  id: number;
  name: string;
  iconUrl: string;
  posterUrl: string;
  categories: string[];
  rtp: number;
  volatility: string;
  status: string;
}

// 广告信息接口
export interface AdInfo {
  id: number;
  adIdentifier: string;
  title: string;
  adType: number;
  images: string[];
  jumpType: number;
  jumpTarget: string;
}

// 推荐游戏接口
export interface RecommendedGame {
  id: number;
  sortOrder: number;
  game: GameInfo;
}

// 分类组游戏接口
export interface CategoryGame {
  id: number;
  sortOrder: number;
  game: GameInfo;
}

// 游戏分类组接口
export interface GameCategory {
  id: number;
  categoryTitle: CategoryTitle;
  sortOrder: number;
  status: number;
  games: CategoryGame[];
}

// 浮点内容接口
export interface FloatContent {
  id?: string;
  type: 'announcement' | 'promotion' | 'customer_service' | 'checkin_reminder' | 'recharge_bonus';
  title: string;
  content: string;
  position: {
    x: number;
    y: number;
  };
  size: {
    width: number;
    height: number;
  };
  style: {
    backgroundColor: string;
    textColor: string;
    borderRadius: number;
    opacity: number;
  };
  timeRange?: {
    startTime: string;
    endTime: string;
  };
  jumpType: 'route' | 'iframe';
  jumpTarget: string;
  isVisible: boolean;
  sortOrder: number;
}

// APP首页配置详情接口
export interface AppHomeConfig {
  id: number;
  configName: string;
  description: string;
  templateType?: string; // BOX模式字段
  status: number;
  sortOrder: number;
  remark: string;
  createTime: string;
  updateTime: string;

  // 广告配置
  topBannerAdId?: number;
  carouselAdId?: number;
  homeGridAdId?: number;
  splashPopupAdId?: number;
  floatAdId?: number;
  topBannerAd?: AdInfo;
  carouselAd?: AdInfo;
  homeGridAd?: AdInfo;
  splashPopupAd?: AdInfo;
  floatAd?: AdInfo;

  // BOX模式特有字段
  floatContents?: FloatContent[];

  // 推荐游戏和分类组
  recommendedGames: RecommendedGame[];
  gameCategories: GameCategory[];
}

// APP首页配置列表项接口
export interface AppHomeConfigListItem {
  id: number;
  configName: string;
  description: string;
  templateType?: string; // BOX模式字段
  status: number;
  sortOrder: number;
  recommendedGameCount: number;
  categoryCount: number;
  createTime: string;
  updateTime: string;
}

// 查询参数接口
export interface AppHomeConfigQueryParams {
  page?: number;
  pageSize?: number;
  configName?: string;
  status?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

// 推荐游戏表单数据
export interface RecommendedGameFormData {
  applicationId: number;
  sortOrder?: number;
}

// 分类组游戏表单数据
export interface CategoryGameFormData {
  applicationId: number;
  sortOrder?: number;
}

// 游戏分类组表单数据
export interface GameCategoryFormData {
  categoryTitle: CategoryTitle;
  sortOrder?: number;
  games: CategoryGameFormData[];
}

// APP首页配置表单数据
export interface AppHomeConfigFormData {
  configName: string;
  description?: string;
  templateType?: string; // BOX模式字段
  topBannerAdId?: number; // 改为可选，BOX模式下不是必需的
  carouselAdId?: number; // 改为可选，BOX模式下不是必需的
  homeGridAdId?: number;
  splashPopupAdId?: number;
  floatAdId?: number;
  floatContents?: FloatContent[]; // BOX模式特有字段
  recommendedGames?: RecommendedGameFormData[]; // 改为可选，BOX模式可能不需要
  gameCategories?: GameCategoryFormData[]; // 改为可选，但BOX模式通常需要
  sortOrder?: number;
  status?: number;
  remark?: string;
}

// 获取APP首页配置列表
export function fetchAppHomeConfigs(params?: AppHomeConfigQueryParams) {
  return request
    .get('config/app-home', {
      searchParams: params as Record<string, string | number | boolean>
    })
    .json<ApiResponse<{
      list: AppHomeConfigListItem[];
      total: number;
      page: number;
      pageSize: number;
      totalPages: number;
    }>>();
}

// 获取APP首页配置详情
export function fetchAppHomeConfig(id: number) {
  return request
    .get(`config/app-home/${id}`)
    .json<ApiResponse<AppHomeConfig>>();
}

// 创建APP首页配置
export function fetchCreateAppHomeConfig(data: AppHomeConfigFormData) {
  return request
    .post('config/app-home', { json: data })
    .json<ApiResponse<AppHomeConfig>>();
}

// 更新APP首页配置
export function fetchUpdateAppHomeConfig(id: number, data: Partial<AppHomeConfigFormData>) {
  return request
    .patch(`config/app-home/${id}`, { json: data })
    .json<ApiResponse<AppHomeConfig>>();
}

// 删除APP首页配置
export function fetchDeleteAppHomeConfig(id: number) {
  return request
    .delete(`config/app-home/${id}`)
    .json<ApiResponse<{ message: string }>>();
}

// 切换APP首页配置状态
export function fetchToggleAppHomeConfigStatus(id: number) {
  return request
    .patch(`config/app-home/${id}/toggle-status`)
    .json<ApiResponse<{ status: number }>>();
}
