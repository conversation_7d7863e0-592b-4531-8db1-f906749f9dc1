import { SysUser } from '../../../system/entities/sys-user.entity';
export declare class VipConfig {
    id: number;
    vipLevel: number;
    levelName: string;
    strategicPosition: string;
    requiredExp: number;
    realMoneyRechargeExpRatio: number;
    realMoneyFlowExpRatio: number;
    goldPaymentExpRatio: number;
    goldFlowExpRatio: number;
    dailyTaskExpMin: number;
    dailyTaskExpMax: number;
    dailyGoldReward: number;
    buybackEnabled: boolean;
    buybackRate: number;
    c2cFeeRate: number;
    rakebackRate: number;
    withdrawalFeeRate: number;
    dailyWithdrawalLimit: number;
    withdrawalPriority: number;
    taskUnlockRequired: boolean;
    taskUnlockGamesRequired: number;
    status: number;
    remark: string;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator: SysUser;
    updater: SysUser;
    calculateUserExp(realMoneyRecharge?: number, realMoneyFlow?: number, goldPayment?: number, goldFlow?: number, dailyTaskCount?: number): number;
    isUserQualified(userExp: number): boolean;
    getBuybackRateDisplay(): string;
    getWithdrawalLimitDisplay(): string;
    getWithdrawalPriorityDisplay(): string;
}
