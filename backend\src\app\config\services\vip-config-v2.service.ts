import { Injectable, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { VipConfig } from '../entities/vip-config-v2.entity';
import { AppUser } from '../../entities/app-user.entity';
import { CreateVipConfigDto } from '../dto/create-vip-config-v2.dto';
import { UpdateVipConfigDto } from '../dto/update-vip-config-v2.dto';
import { VipConfigQueryDto } from '../dto/vip-config-query-v2.dto';
import { VipExpService } from './vip-exp.service';

/**
 * VIP配置服务 - V12.0 经济权益系统
 * 负责VIP等级配置的CRUD操作和权益管理
 */
@Injectable()
export class VipConfigService {
  constructor(
    @InjectRepository(VipConfig)
    private readonly vipConfigRepository: Repository<VipConfig>,
    @InjectRepository(AppUser)
    private readonly appUserRepository: Repository<AppUser>,
    private readonly vipExpService: VipExpService,
  ) {}

  /**
   * 创建VIP配置
   */
  async create(createVipConfigDto: CreateVipConfigDto, userId: number) {
    // 检查VIP等级是否已存在
    const existingConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: createVipConfigDto.vipLevel },
    });
    if (existingConfig) {
      throw new ConflictException(`VIP等级 ${createVipConfigDto.vipLevel} 已存在`);
    }

    // 验证EXP递增规则
    await this.validateExpIncrement(createVipConfigDto.vipLevel, createVipConfigDto.requiredExp);

    // 验证业务规则
    this.validateBusinessRules(createVipConfigDto);

    const vipConfig = this.vipConfigRepository.create({
      ...createVipConfigDto,
      createdBy: userId,
      updatedBy: userId,
    });

    return await this.vipConfigRepository.save(vipConfig);
  }

  /**
   * 分页查询VIP配置列表
   */
  async findAll(query: VipConfigQueryDto) {
    const { page = 1, pageSize = 10, vipLevel, status, levelName, buybackEnabled } = query;
    
    const queryBuilder = this.vipConfigRepository.createQueryBuilder('vip')
      .leftJoinAndSelect('vip.creator', 'creator')
      .leftJoinAndSelect('vip.updater', 'updater');

    // 添加筛选条件
    if (vipLevel !== undefined) {
      queryBuilder.andWhere('vip.vipLevel = :vipLevel', { vipLevel });
    }
    if (status !== undefined) {
      queryBuilder.andWhere('vip.status = :status', { status });
    }
    if (levelName) {
      queryBuilder.andWhere('vip.levelName LIKE :levelName', { levelName: `%${levelName}%` });
    }
    if (buybackEnabled !== undefined) {
      queryBuilder.andWhere('vip.buybackEnabled = :buybackEnabled', { buybackEnabled });
    }

    // 排序
    queryBuilder.orderBy('vip.vipLevel', 'ASC');

    // 分页
    const skip = (page - 1) * pageSize;
    queryBuilder.skip(skip).take(pageSize);

    const [list, total] = await queryBuilder.getManyAndCount();

    return {
      list,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 根据ID查询VIP配置
   */
  async findOne(id: number) {
    const vipConfig = await this.vipConfigRepository.findOne({
      where: { id },
      relations: ['creator', 'updater'],
    });
    if (!vipConfig) {
      throw new NotFoundException('VIP配置不存在');
    }
    return vipConfig;
  }

  /**
   * 根据VIP等级查询配置
   */
  async findByLevel(vipLevel: number) {
    const vipConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel, status: 1 },
    });
    if (!vipConfig) {
      throw new NotFoundException(`VIP等级 ${vipLevel} 配置不存在`);
    }
    return vipConfig;
  }

  /**
   * 更新VIP配置
   */
  async update(id: number, updateVipConfigDto: UpdateVipConfigDto, userId: number) {
    const existingConfig = await this.findOne(id);

    // 如果更新VIP等级，检查是否与其他配置冲突
    if (updateVipConfigDto.vipLevel !== undefined && updateVipConfigDto.vipLevel !== existingConfig.vipLevel) {
      const conflictConfig = await this.vipConfigRepository.findOne({
        where: { vipLevel: updateVipConfigDto.vipLevel },
      });
      if (conflictConfig) {
        throw new ConflictException(`VIP等级 ${updateVipConfigDto.vipLevel} 已存在`);
      }
    }

    // 如果更新所需EXP，验证递增规则
    if (updateVipConfigDto.requiredExp !== undefined) {
      await this.validateExpIncrement(
        updateVipConfigDto.vipLevel || existingConfig.vipLevel,
        updateVipConfigDto.requiredExp,
        id
      );
    }

    // 验证业务规则
    this.validateBusinessRules({ ...existingConfig, ...updateVipConfigDto });

    await this.vipConfigRepository.update(id, {
      ...updateVipConfigDto,
      updatedBy: userId,
    });

    return await this.findOne(id);
  }

  /**
   * 删除VIP配置
   */
  async remove(id: number) {
    const vipConfig = await this.findOne(id);
    
    // 检查是否有用户使用此VIP等级
    const userCount = await this.appUserRepository.count({
      where: { vipLevel: vipConfig.vipLevel },
    });
    if (userCount > 0) {
      throw new BadRequestException(`无法删除VIP等级 ${vipConfig.vipLevel}，还有 ${userCount} 个用户正在使用此等级`);
    }

    await this.vipConfigRepository.remove(vipConfig);
    return { message: '删除成功' };
  }

  /**
   * 获取用户VIP权益详情
   */
  async getUserVipBenefits(userId: number) {
    return await this.vipExpService.getUserVipBenefits(userId);
  }

  /**
   * 添加用户EXP
   */
  async addUserExp(userId: number, expType: string, amount: number, description?: string) {
    return await this.vipExpService.addExp(userId, expType as any, amount, description);
  }

  /**
   * 重新计算所有用户VIP等级
   */
  async recalculateAllUserVipLevels() {
    return await this.vipExpService.recalculateAllUserVipLevels();
  }

  /**
   * 获取VIP等级权益对比
   */
  async getVipLevelComparison() {
    const configs = await this.vipConfigRepository.find({
      where: { status: 1 },
      order: { vipLevel: 'ASC' },
    });

    return configs.map(config => ({
      vipLevel: config.vipLevel,
      levelName: config.levelName,
      strategicPosition: config.strategicPosition,
      requiredExp: config.requiredExp,
      dailyGoldReward: config.dailyGoldReward,
      buybackRateDisplay: config.getBuybackRateDisplay(),
      c2cFeeRate: `${config.c2cFeeRate}%`,
      rakebackRate: `${config.rakebackRate}%`,
      withdrawalFeeRate: `${config.withdrawalFeeRate}%`,
      withdrawalLimitDisplay: config.getWithdrawalLimitDisplay(),
      withdrawalPriorityDisplay: config.getWithdrawalPriorityDisplay(),
    }));
  }

  /**
   * 验证EXP递增规则
   */
  private async validateExpIncrement(vipLevel: number, requiredExp: number, excludeId?: number) {
    // 获取相邻等级的配置
    const lowerLevelConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: vipLevel - 1 },
    });
    const higherLevelConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: vipLevel + 1 },
    });

    // 检查是否满足递增规则
    if (lowerLevelConfig && requiredExp <= lowerLevelConfig.requiredExp) {
      throw new BadRequestException(
        `VIP等级 ${vipLevel} 所需EXP (${requiredExp}) 必须大于等级 ${vipLevel - 1} 的EXP (${lowerLevelConfig.requiredExp})`
      );
    }
    if (higherLevelConfig && requiredExp >= higherLevelConfig.requiredExp) {
      throw new BadRequestException(
        `VIP等级 ${vipLevel} 所需EXP (${requiredExp}) 必须小于等级 ${vipLevel + 1} 的EXP (${higherLevelConfig.requiredExp})`
      );
    }
  }

  /**
   * 验证业务规则
   */
  private validateBusinessRules(config: any) {
    // 验证活跃任务EXP范围
    if (config.dailyTaskExpMin > config.dailyTaskExpMax) {
      throw new BadRequestException('活跃任务最小EXP不能大于最大EXP');
    }

    // 验证回购配置
    if (config.buybackEnabled && !config.buybackRate) {
      throw new BadRequestException('开启回购功能时必须设置回购价格');
    }
    if (!config.buybackEnabled && config.buybackRate) {
      throw new BadRequestException('未开启回购功能时不应设置回购价格');
    }

    // 验证提现优先级
    if (config.withdrawalPriority < 1 || config.withdrawalPriority > 3) {
      throw new BadRequestException('提现优先级必须在1-3之间');
    }

    // 验证解锁机制
    if (config.taskUnlockRequired && config.taskUnlockGamesRequired <= 0) {
      throw new BadRequestException('需要解锁时必须设置大于0的游戏局数');
    }
  }
}
