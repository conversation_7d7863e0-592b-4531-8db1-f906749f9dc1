# 权限系统快速参考

## 权限代码规范

### 命名规则
```
模块:功能:操作
```

### 系统管理模块
```
system:user:view         # 用户管理-查看
system:user:add          # 用户管理-新增
system:user:edit         # 用户管理-编辑
system:user:delete       # 用户管理-删除
system:user:reset        # 用户管理-重置密码

system:role:view         # 角色管理-查看
system:role:add          # 角色管理-新增
system:role:edit         # 角色管理-编辑
system:role:delete       # 角色管理-删除

system:permission:view   # 权限管理-查看
system:permission:add    # 权限管理-新增
system:permission:edit   # 权限管理-编辑
system:permission:delete # 权限管理-删除

system:menu:view         # 菜单管理-查看
system:menu:add          # 菜单管理-新增
system:menu:edit         # 菜单管理-编辑
system:menu:delete       # 菜单管理-删除

system:workbook:view     # 数据字典-查看
system:workbook:add      # 数据字典-新增
system:workbook:edit     # 数据字典-编辑
system:workbook:delete   # 数据字典-删除
```

### 配置管理模块
```
config:vip:view          # VIP配置-查看
config:vip:edit          # VIP配置-编辑

config:charge:view       # 充值配置-查看
config:charge:add        # 充值配置-新增
config:charge:edit       # 充值配置-编辑
config:charge:delete     # 充值配置-删除

config:ad:view           # 广告配置-查看
config:ad:add            # 广告配置-新增
config:ad:edit           # 广告配置-编辑
config:ad:delete         # 广告配置-删除

config:homepage:view     # 首页配置-查看
config:homepage:edit     # 首页配置-编辑
```

### 用户管理模块
```
user:app:view            # APP用户-查看
user:app:edit            # APP用户-编辑
user:app:ban             # APP用户-封禁
user:app:tag             # APP用户-标签管理
```

### 渠道管理模块
```
channel:list:view        # 渠道列表-查看
channel:list:add         # 渠道列表-新增
channel:list:edit        # 渠道列表-编辑
channel:list:disable     # 渠道列表-禁用

channel:ad:view          # 广告列表-查看
channel:ad:add           # 广告列表-新增
channel:ad:edit          # 广告列表-编辑
channel:ad:disable       # 广告列表-禁用
```

## 前端权限使用

### 1. 页面级权限控制

```typescript
// 路由配置
const routes: AppRouteRecordRaw[] = [
  {
    path: '/system/user',
    Component: lazy(() => import('#src/pages/system/user')),
    handle: {
      title: '用户管理',
      permissions: ['system:user:view'],
      roles: ['admin', 'user_manager']
    }
  }
];
```

### 2. 组件级权限控制

```typescript
import { AccessControl } from '#src/components/access-control';

// 使用AccessControl组件
<AccessControl codes="system:user:add">
  <Button type="primary">新增用户</Button>
</AccessControl>

// 多个权限（满足其一即可）
<AccessControl codes={["system:user:edit", "system:user:delete"]}>
  <Button>操作</Button>
</AccessControl>
```

### 3. Hook方式权限控制

```typescript
import { useAccess } from '#src/hooks';

function UserManagement() {
  const { hasAccessByCodes, hasAccessByRoles } = useAccess();

  return (
    <div>
      {hasAccessByCodes('system:user:add') && (
        <Button type="primary">新增用户</Button>
      )}
      
      {hasAccessByCodes(['system:user:edit', 'system:user:delete']) && (
        <Button>批量操作</Button>
      )}
      
      {hasAccessByRoles('admin') && (
        <Button danger>危险操作</Button>
      )}
    </div>
  );
}
```

### 4. 超级管理员判断

```typescript
import { useUserStore } from '#src/store';

function AdminPanel() {
  const { isSuperAdmin } = useUserStore();

  if (isSuperAdmin) {
    return <SuperAdminDashboard />;
  }

  return <RegularUserDashboard />;
}
```

## 后端权限使用

### 1. 控制器权限装饰器

```typescript
import { Permissions } from '#src/common/decorators';
import { UseGuards } from '@nestjs/common';
import { SystemJwtAuthGuard } from '#src/system/auth/guards';

@Controller('system/user')
@UseGuards(SystemJwtAuthGuard)
export class SystemUserController {
  
  @Permissions('system:user:view')
  @Get('list')
  async findAll() {
    // 查看用户列表
  }

  @Permissions('system:user:add')
  @Post()
  async create(@Body() createUserDto: CreateUserDto) {
    // 创建用户
  }

  @Permissions('system:user:edit')
  @Put(':id')
  async update(@Param('id') id: number, @Body() updateUserDto: UpdateUserDto) {
    // 更新用户
  }

  @Permissions('system:user:delete')
  @Delete(':id')
  async remove(@Param('id') id: number) {
    // 删除用户
  }
}
```

### 2. 超级管理员专用接口

```typescript
import { SuperAdminGuard } from '#src/system/auth/guards';

@Controller('system/admin')
@UseGuards(SystemJwtAuthGuard, SuperAdminGuard)
export class SuperAdminController {
  
  @Post('reset-password/:userId')
  async resetUserPassword(@Param('userId') userId: number) {
    // 只有超级管理员可以重置任意用户密码
  }

  @Post('system-config')
  async updateSystemConfig(@Body() config: any) {
    // 只有超级管理员可以修改系统配置
  }
}
```

### 3. 服务层权限检查

```typescript
@Injectable()
export class UserService {
  
  async updateUser(userId: number, updateData: any, currentUser: any) {
    // 检查是否为超级管理员
    if (currentUser.isSuperAdmin) {
      // 超级管理员可以修改任何用户
      return this.userRepository.update(userId, updateData);
    }

    // 普通用户只能修改自己的信息
    if (currentUser.userId !== userId) {
      throw new ForbiddenException('无权限修改其他用户信息');
    }

    return this.userRepository.update(userId, updateData);
  }
}
```

## 数据库操作

### 1. 创建权限

```sql
-- 插入新权限
INSERT INTO sys_permissions (name, code, description, type, status) 
VALUES ('VIP配置查看', 'config:vip:view', 'VIP配置页面查看权限', 'menu', 1);

INSERT INTO sys_permissions (name, code, description, type, status) 
VALUES ('VIP配置编辑', 'config:vip:edit', 'VIP配置编辑权限', 'button', 1);
```

### 2. 创建菜单

```sql
-- 插入父菜单
INSERT INTO sys_menus (title, path, icon, parent_id, "order", type, status, component, permission_code) 
VALUES ('配置管理', '/config', 'setting', NULL, 3, 0, 1, NULL, NULL);

-- 插入子菜单
INSERT INTO sys_menus (title, path, icon, parent_id, "order", type, status, component, permission_code, button_permissions) 
VALUES (
  'VIP配置', 
  '/config/vip', 
  'crown', 
  (SELECT id FROM sys_menus WHERE path = '/config'), 
  1, 
  1, 
  1, 
  'config/vip-v2', 
  'config:vip:view',
  '["config:vip:edit"]'::jsonb
);
```

### 3. 分配权限给角色

```sql
-- 给角色分配权限
INSERT INTO sys_role_permissions (role_id, permission_id) 
SELECT 
  r.id as role_id,
  p.id as permission_id
FROM sys_roles r, sys_permissions p 
WHERE r.code = 'admin' 
  AND p.code IN ('config:vip:view', 'config:vip:edit');
```

### 4. 分配角色给用户

```sql
-- 给用户分配角色
INSERT INTO sys_user_roles (user_id, role_id) 
SELECT 
  u.id as user_id,
  r.id as role_id
FROM sys_users u, sys_roles r 
WHERE u.username = 'admin' 
  AND r.code = 'admin';
```

### 5. 设置超级管理员

```sql
-- 设置用户为超级管理员
UPDATE sys_users 
SET is_super_admin = true 
WHERE username = 'admin';
```

## 常用查询

### 1. 查看用户权限

```sql
-- 查看用户的所有权限
SELECT DISTINCT p.code, p.name, p.description
FROM sys_users u
JOIN sys_user_roles ur ON u.id = ur.user_id
JOIN sys_roles r ON ur.role_id = r.id
JOIN sys_role_permissions rp ON r.id = rp.role_id
JOIN sys_permissions p ON rp.permission_id = p.id
WHERE u.username = 'admin' AND p.status = 1;
```

### 2. 查看角色权限

```sql
-- 查看角色的所有权限
SELECT p.code, p.name, p.description
FROM sys_roles r
JOIN sys_role_permissions rp ON r.id = rp.role_id
JOIN sys_permissions p ON rp.permission_id = p.id
WHERE r.code = 'admin' AND p.status = 1;
```

### 3. 查看用户菜单

```sql
-- 查看用户可访问的菜单
SELECT DISTINCT m.title, m.path, m.component, m.permission_code
FROM sys_users u
JOIN sys_user_roles ur ON u.id = ur.user_id
JOIN sys_roles r ON ur.role_id = r.id
JOIN sys_role_permissions rp ON r.id = rp.role_id
JOIN sys_permissions p ON rp.permission_id = p.id
JOIN sys_menus m ON p.code = m.permission_code
WHERE u.username = 'admin' 
  AND m.status = 1 
  AND p.status = 1
ORDER BY m."order";
```

## 故障排查

### 1. 用户无法登录
- 检查用户状态：`SELECT status FROM sys_users WHERE username = 'xxx'`
- 检查密码：确认密码是否正确加密

### 2. 用户无法访问页面
- 检查用户角色：`SELECT r.code FROM sys_users u JOIN sys_user_roles ur ON u.id = ur.user_id JOIN sys_roles r ON ur.role_id = r.id WHERE u.username = 'xxx'`
- 检查角色权限：查看角色是否有对应权限
- 检查菜单配置：确认菜单的permission_code是否正确

### 3. 菜单不显示
- 检查菜单状态：`SELECT status FROM sys_menus WHERE path = 'xxx'`
- 检查权限代码：确认permission_code是否存在且正确
- 检查组件路径：确认component字段是否正确

### 4. 超级管理员权限异常
- 检查超级管理员标识：`SELECT is_super_admin FROM sys_users WHERE username = 'xxx'`
- 检查前端状态：确认UserStore中的isSuperAdmin状态

## 最佳实践

1. **权限最小化原则**：只给用户必需的权限
2. **定期权限审计**：定期检查用户权限分配
3. **权限代码规范**：严格按照命名规范创建权限代码
4. **前后端双重验证**：前端控制UI，后端保证安全
5. **超级管理员谨慎使用**：超级管理员权限过大，需谨慎分配
