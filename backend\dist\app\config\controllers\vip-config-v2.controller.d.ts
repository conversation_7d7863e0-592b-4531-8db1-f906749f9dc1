import { VipConfigV2Service } from '../services/vip-config-v2.service';
import { CreateVipConfigDto } from '../dto/create-vip-config-v2.dto';
import { UpdateVipConfigDto } from '../dto/update-vip-config-v2.dto';
import { VipConfigQueryDto, VipExpAddDto } from '../dto/vip-config-query-v2.dto';
export declare class VipConfigV2Controller {
    private readonly vipConfigService;
    constructor(vipConfigService: VipConfigV2Service);
    create(createVipConfigDto: CreateVipConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("../entities/vip-config-v2.entity").VipConfigV2;
    }>;
    findAll(query: VipConfigQueryDto): Promise<{
        code: number;
        message: string;
        result: {
            list: import("../entities/vip-config-v2.entity").VipConfigV2[];
            total: number;
            page: number;
            pageSize: number;
            totalPages: number;
        };
    }>;
    getVipLevelComparison(): Promise<{
        code: number;
        message: string;
        result: {
            vipLevel: number;
            levelName: string;
            strategicPosition: string;
            requiredExp: number;
            dailyGoldReward: number;
            buybackRateDisplay: string;
            c2cFeeRate: string;
            rakebackRate: string;
            withdrawalFeeRate: string;
            withdrawalLimitDisplay: string;
            withdrawalPriorityDisplay: string;
        }[];
    }>;
    findOne(id: number): Promise<{
        code: number;
        message: string;
        result: import("../entities/vip-config-v2.entity").VipConfigV2;
    }>;
    findByLevel(vipLevel: number): Promise<{
        code: number;
        message: string;
        result: import("../entities/vip-config-v2.entity").VipConfigV2;
    }>;
    update(id: number, updateVipConfigDto: UpdateVipConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("../entities/vip-config-v2.entity").VipConfigV2;
    }>;
    remove(id: number): Promise<{
        code: number;
        message: string;
        result: {
            message: string;
        };
    }>;
    getUserVipBenefits(userId: number): Promise<{
        code: number;
        message: string;
        result: {
            vipLevel: number;
            levelName: string;
            strategicPosition: string;
            currentExp: number;
            requiredExp: number;
            nextLevelExp: number;
            progressPercent: number;
            benefits: {
                dailyGoldReward: number;
                buybackEnabled: boolean;
                buybackRate: number;
                c2cFeeRate: number;
                rakebackRate: number;
                withdrawalFeeRate: number;
                dailyWithdrawalLimit: number;
                withdrawalPriority: number;
            };
        };
    }>;
    addUserExp(userId: number, addExpDto: VipExpAddDto): Promise<{
        code: number;
        message: string;
        result: {
            expGained: number;
            expType: "real_money_recharge" | "real_money_flow" | "gold_payment" | "gold_flow" | "daily_task";
            amount: number;
        };
    }>;
    recalculateAllUserVipLevels(): Promise<{
        code: number;
        message: string;
        result: {
            processed: number;
            upgraded: number;
            downgraded: number;
        };
    }>;
    getVipStatsOverview(): Promise<{
        code: number;
        message: string;
        result: {
            totalConfigs: number;
            totalUsers: number;
            levelDistribution: never[];
            expDistribution: never[];
        };
    }>;
    getVipLevelDistribution(): Promise<{
        code: number;
        message: string;
        result: never[];
    }>;
    checkUserBuybackPermission(userId: number): Promise<{
        code: number;
        message: string;
        result: {
            hasPermission: boolean;
            buybackRate: number;
            vipLevel: number;
        };
    }>;
    checkUserWithdrawalPermission(userId: number): Promise<{
        code: number;
        message: string;
        result: {
            feeRate: number;
            dailyLimit: number;
            priority: number;
            vipLevel: number;
        };
    }>;
    checkUserC2CPermission(userId: number): Promise<{
        code: number;
        message: string;
        result: {
            feeRate: number;
            vipLevel: number;
        };
    }>;
}
