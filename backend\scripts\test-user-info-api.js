const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testUserInfoAPI() {
  try {
    console.log('🔍 测试用户信息API返回...');
    
    // 创建应用实例
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // 获取用户服务
    const { SystemUserService } = require('../dist/system/user/user.service');
    const userService = app.get(SystemUserService);
    
    // 测试获取admin用户信息
    console.log('\n📋 获取admin用户信息:');
    const userInfo = await userService.getUserInfo(2); // admin用户ID是2
    
    console.log('用户信息:');
    console.log(`  ID: ${userInfo.id}`);
    console.log(`  用户名: ${userInfo.username}`);
    console.log(`  邮箱: ${userInfo.email}`);
    console.log(`  是否超级管理员: ${userInfo.isSuperAdmin}`);
    console.log(`  角色: ${JSON.stringify(userInfo.roles)}`);
    
    await app.close();
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testUserInfoAPI();
