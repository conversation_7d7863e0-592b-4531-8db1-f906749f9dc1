// 同步角色数据到本地数据库的脚本
const { Client } = require('pg');

async function syncRolesToLocal() {
  // Supabase数据库连接
  const supabaseClient = new Client({
    host: 'db.ytrftwscazjboxbwnrxp.supabase.co',
    port: 5432,
    database: 'postgres',
    user: 'postgres',
    password: 'inapp2backend2024!',
    ssl: {
      rejectUnauthorized: false
    }
  });

  // 本地数据库连接
  const localClient = new Client({
    host: '**************',
    port: 5435,
    database: 'inapp2',
    user: 'user_jJSpPW',
    password: 'password_DmrhYX'
  });

  try {
    console.log('🔄 开始同步角色数据到本地数据库...');
    
    // 连接两个数据库
    await supabaseClient.connect();
    await localClient.connect();
    console.log('✅ 数据库连接成功');

    // 1. 从Supabase获取角色数据
    console.log('\n📥 从Supabase获取角色数据...');
    const supabaseRoles = await supabaseClient.query(`
      SELECT id, name, code, remark, status, create_time, update_time
      FROM sys_roles 
      ORDER BY id;
    `);
    
    console.log(`Supabase角色数据 (${supabaseRoles.rows.length} 条):`);
    supabaseRoles.rows.forEach(role => {
      console.log(`  ID: ${role.id}, 名称: ${role.name}, 代码: ${role.code}`);
    });

    // 2. 从Supabase获取用户角色关联数据
    console.log('\n📥 从Supabase获取用户角色关联数据...');
    const supabaseUserRoles = await supabaseClient.query(`
      SELECT ur.user_id, ur.role_id, ur.create_time,
             u.username, r.name as role_name, r.code as role_code
      FROM sys_user_roles ur
      LEFT JOIN sys_users u ON ur.user_id = u.id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      ORDER BY ur.user_id, ur.role_id;
    `);
    
    console.log(`Supabase用户角色关联数据 (${supabaseUserRoles.rows.length} 条):`);
    supabaseUserRoles.rows.forEach(ur => {
      console.log(`  用户: ${ur.username} (ID: ${ur.user_id}) -> 角色: ${ur.role_name} (ID: ${ur.role_id})`);
    });

    // 3. 清理本地角色数据（保留现有角色，只更新不存在的）
    console.log('\n🧹 检查本地角色数据...');
    const localRoles = await localClient.query(`
      SELECT id, name, code FROM sys_roles ORDER BY id;
    `);
    
    console.log(`本地现有角色 (${localRoles.rows.length} 条):`);
    localRoles.rows.forEach(role => {
      console.log(`  ID: ${role.id}, 名称: ${role.name}, 代码: ${role.code}`);
    });

    // 4. 同步角色数据
    console.log('\n📤 同步角色数据到本地...');
    for (const role of supabaseRoles.rows) {
      const existingRoleById = localRoles.rows.find(lr => lr.id === role.id);
      const existingRoleByCode = localRoles.rows.find(lr => lr.code === role.code);

      if (existingRoleById) {
        // 更新现有角色（按ID匹配）
        await localClient.query(`
          UPDATE sys_roles
          SET name = $1, code = $2, remark = $3, status = $4, update_time = $5
          WHERE id = $6
        `, [role.name, role.code, role.remark, role.status, role.update_time, role.id]);
        console.log(`  ✅ 更新角色: ${role.name} (ID: ${role.id})`);
      } else if (existingRoleByCode) {
        // 如果按代码匹配到了，更新该角色的ID和其他信息
        console.log(`  🔄 发现代码冲突: ${role.code}, 本地ID: ${existingRoleByCode.id}, Supabase ID: ${role.id}`);

        // 先更新现有角色的信息
        await localClient.query(`
          UPDATE sys_roles
          SET name = $1, remark = $2, status = $3, update_time = $4
          WHERE code = $5
        `, [role.name, role.remark, role.status, role.update_time, role.code]);
        console.log(`  ✅ 更新现有角色: ${role.name} (保持本地ID: ${existingRoleByCode.id})`);

        // 记录ID映射，用于后续用户角色关联的转换
        if (!global.roleIdMapping) global.roleIdMapping = {};
        global.roleIdMapping[role.id] = existingRoleByCode.id;
        console.log(`  📝 记录ID映射: Supabase ${role.id} -> 本地 ${existingRoleByCode.id}`);
      } else {
        // 插入新角色
        await localClient.query(`
          INSERT INTO sys_roles (id, name, code, remark, status, create_time, update_time)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [role.id, role.name, role.code, role.remark, role.status, role.create_time, role.update_time]);
        console.log(`  ➕ 插入新角色: ${role.name} (ID: ${role.id})`);
      }
    }

    // 5. 清理本地用户角色关联数据
    console.log('\n🧹 清理本地用户角色关联数据...');
    await localClient.query('DELETE FROM sys_user_roles');
    console.log('  ✅ 已清理本地用户角色关联数据');

    // 6. 同步用户角色关联数据
    console.log('\n📤 同步用户角色关联数据到本地...');
    for (const ur of supabaseUserRoles.rows) {
      // 检查是否需要转换角色ID
      let localRoleId = ur.role_id;
      if (global.roleIdMapping && global.roleIdMapping[ur.role_id]) {
        localRoleId = global.roleIdMapping[ur.role_id];
        console.log(`  🔄 转换角色ID: ${ur.role_id} -> ${localRoleId}`);
      }

      await localClient.query(`
        INSERT INTO sys_user_roles (user_id, role_id, create_time)
        VALUES ($1, $2, $3)
      `, [ur.user_id, localRoleId, ur.create_time]);
      console.log(`  ➕ 插入关联: 用户${ur.user_id} -> 角色${localRoleId} (${ur.role_name})`);
    }

    // 7. 验证同步结果
    console.log('\n✅ 验证同步结果...');
    const localUserRolesAfter = await localClient.query(`
      SELECT ur.user_id, ur.role_id, u.username, r.name as role_name, r.code as role_code
      FROM sys_user_roles ur
      LEFT JOIN sys_users u ON ur.user_id = u.id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      ORDER BY ur.user_id, ur.role_id;
    `);
    
    console.log(`同步后本地用户角色关联 (${localUserRolesAfter.rows.length} 条):`);
    localUserRolesAfter.rows.forEach(ur => {
      console.log(`  用户: ${ur.username} (ID: ${ur.user_id}) -> 角色: ${ur.role_name} (ID: ${ur.role_id}, 代码: ${ur.role_code})`);
    });

    // 8. 特别检查admin用户
    console.log('\n👤 检查admin用户角色关联...');
    const adminRoles = await localClient.query(`
      SELECT ur.user_id, ur.role_id, u.username, r.name as role_name, r.code as role_code
      FROM sys_user_roles ur
      LEFT JOIN sys_users u ON ur.user_id = u.id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE u.username = 'admin'
      ORDER BY ur.role_id;
    `);
    
    console.log(`admin用户角色关联 (${adminRoles.rows.length} 条):`);
    adminRoles.rows.forEach(ur => {
      console.log(`  角色: ${ur.role_name} (ID: ${ur.role_id}, 代码: ${ur.role_code})`);
    });

    await supabaseClient.end();
    await localClient.end();
    console.log('\n🎉 角色数据同步完成！');

  } catch (error) {
    console.error('❌ 同步失败:', error.message);
    console.error(error.stack);
    await supabaseClient.end();
    await localClient.end();
    throw error;
  }
}

// 执行同步
if (require.main === module) {
  syncRolesToLocal()
    .then(() => {
      console.log('✅ 同步完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 同步失败:', error);
      process.exit(1);
    });
}

module.exports = { syncRolesToLocal };
