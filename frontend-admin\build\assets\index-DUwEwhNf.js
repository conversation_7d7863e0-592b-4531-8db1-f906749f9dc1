import{b6 as x,aW as a,b5 as h,br as g,bs as T,av as F,P as C}from"./antd-FyCAPQKa.js";import{a as j}from"./react-BUTTOX-3.js";import{P as c}from"./BaseForm-CUW86psc.js";import{j as n}from"./index-DD0gcXtR.js";var y=["fieldProps","proFieldProps"],S=["fieldProps","proFieldProps"],d="text",_=function(r){var e=r.fieldProps,s=r.proFieldProps,t=x(r,y);return n.jsx(c,a({valueType:d,fieldProps:e,filedConfig:{valueType:d},proFieldProps:s},t))},R=function(r){var e=T(r.open||!1,{value:r.open,onChange:r.onOpenChange}),s=h(e,2),t=s[0],p=s[1];return n.jsx(<PERSON><PERSON>,{shouldUpdate:!0,noStyle:!0,children:function(v){var l,P=v.getFieldValue(r.name||[]);return n.jsx(C,a(a({getPopupContainer:function(o){return o&&o.parentNode?o.parentNode:o},onOpenChange:function(o){return p(o)},content:n.jsxs("div",{style:{padding:"4px 0"},children:[(l=r.statusRender)===null||l===void 0?void 0:l.call(r,P),r.strengthText?n.jsx("div",{style:{marginTop:10},children:n.jsx("span",{children:r.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},r.popoverProps),{},{open:t,children:r.children}))}})},b=function(r){var e=r.fieldProps,s=r.proFieldProps,t=x(r,S),p=j.useState(!1),u=h(p,2),v=u[0],l=u[1];return e!=null&&e.statusRender&&t.name?n.jsx(R,{name:t.name,statusRender:e==null?void 0:e.statusRender,popoverProps:e==null?void 0:e.popoverProps,strengthText:e==null?void 0:e.strengthText,open:v,onOpenChange:l,children:n.jsx("div",{children:n.jsx(c,a({valueType:"password",fieldProps:a(a({},g(e,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(i){var o;e==null||(o=e.onBlur)===null||o===void 0||o.call(e,i),l(!1)},onClick:function(i){var o;e==null||(o=e.onClick)===null||o===void 0||o.call(e,i),l(!0)}}),proFieldProps:s,filedConfig:{valueType:d}},t))})}):n.jsx(c,a({valueType:"password",fieldProps:e,proFieldProps:s,filedConfig:{valueType:d}},t))},m=_;m.Password=b;m.displayName="ProFormComponent";export{m as W};
