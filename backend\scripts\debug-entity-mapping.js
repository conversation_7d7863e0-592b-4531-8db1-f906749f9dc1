// 调试实体映射的脚本
const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function debugEntityMapping() {
  try {
    console.log('🚀 启动NestJS应用...');
    const app = await NestFactory.createApplicationContext(AppModule);
    console.log('✅ 应用启动成功');

    const { DataSource } = require('typeorm');
    const dataSource = app.get(DataSource);

    console.log('\n🔍 检查实体元数据...');
    
    // 获取所有实体元数据
    const entityMetadatas = dataSource.entityMetadatas;
    
    console.log(`找到 ${entityMetadatas.length} 个实体:`);
    entityMetadatas.forEach(metadata => {
      console.log(`  - ${metadata.name} -> 表名: ${metadata.tableName}`);
    });

    // 检查SysUser实体
    const userMetadata = entityMetadatas.find(m => m.name === 'SysUser');
    if (userMetadata) {
      console.log('\n👤 SysUser实体详情:');
      console.log(`  表名: ${userMetadata.tableName}`);
      console.log(`  列数: ${userMetadata.columns.length}`);
      console.log(`  关系数: ${userMetadata.relations.length}`);
      
      console.log('\n  关系详情:');
      userMetadata.relations.forEach(relation => {
        console.log(`    - ${relation.propertyName}: ${relation.relationType}`);
        console.log(`      目标实体: ${relation.type}`);
        console.log(`      连接表: ${relation.joinTableName || '无'}`);
        if (relation.joinColumns) {
          console.log(`      连接列: ${JSON.stringify(relation.joinColumns.map(c => c.databaseName))}`);
        }
        if (relation.inverseJoinColumns) {
          console.log(`      反向连接列: ${JSON.stringify(relation.inverseJoinColumns.map(c => c.databaseName))}`);
        }
      });
    }

    // 检查SysRole实体
    const roleMetadata = entityMetadatas.find(m => m.name === 'SysRole');
    if (roleMetadata) {
      console.log('\n🎭 SysRole实体详情:');
      console.log(`  表名: ${roleMetadata.tableName}`);
      console.log(`  列数: ${roleMetadata.columns.length}`);
      console.log(`  关系数: ${roleMetadata.relations.length}`);
      
      console.log('\n  关系详情:');
      roleMetadata.relations.forEach(relation => {
        console.log(`    - ${relation.propertyName}: ${relation.relationType}`);
        console.log(`      目标实体: ${relation.type}`);
        console.log(`      连接表: ${relation.joinTableName || '无'}`);
      });
    }

    // 测试原生SQL查询
    console.log('\n🧪 测试原生SQL查询:');
    const rawResult = await dataSource.query(`
      SELECT 
        u.id, u.username, u.is_super_admin,
        r.id as role_id, r.name as role_name, r.code as role_code
      FROM sys_users u
      LEFT JOIN sys_user_roles ur ON u.id = ur.user_id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE u.username = $1
    `, ['admin']);
    
    console.log('原生SQL查询结果:');
    rawResult.forEach(row => {
      console.log(`  用户: ${row.username} (ID: ${row.id}, 超级管理员: ${row.is_super_admin})`);
      if (row.role_id) {
        console.log(`    角色: ${row.role_name} (ID: ${row.role_id}, 代码: ${row.role_code})`);
      }
    });

    // 测试TypeORM查询构建器的SQL
    console.log('\n🔧 测试TypeORM查询构建器生成的SQL:');
    const { SysUser } = require('../dist/system/entities/sys-user.entity');
    const userRepository = dataSource.getRepository(SysUser);
    
    const queryBuilder = userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where('user.username = :username', { username: 'admin' });
    
    console.log('生成的SQL:');
    console.log(queryBuilder.getSql());
    console.log('参数:', queryBuilder.getParameters());

    // 执行查询并查看结果
    const result = await queryBuilder.getOne();
    console.log('\n查询结果:');
    if (result) {
      console.log(`  用户: ${result.username} (ID: ${result.id})`);
      console.log(`  超级管理员: ${result.isSuperAdmin}`);
      console.log(`  角色数量: ${result.roles ? result.roles.length : 0}`);
      if (result.roles && result.roles.length > 0) {
        result.roles.forEach(role => {
          console.log(`    角色: ${role.name} (${role.code})`);
        });
      }
    }

    await app.close();
    console.log('\n🎉 调试完成！');

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 执行调试
if (require.main === module) {
  debugEntityMapping()
    .then(() => {
      console.log('✅ 调试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 调试失败:', error);
      process.exit(1);
    });
}

module.exports = { debugEntityMapping };
