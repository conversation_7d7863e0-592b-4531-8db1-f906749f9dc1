-- VIP配置数据迁移脚本 V12.0 (修正版)
-- 将现有VIP配置数据迁移到新的经济权益系统
-- 执行时间：2025-06-29

-- 1. 备份现有用户VIP数据
CREATE TEMPORARY TABLE user_vip_backup AS
SELECT 
    id,
    vip_level,
    vip_exp,
    recharge_balance,
    withdrawable_balance,
    gold_balance
FROM app_users 
WHERE vip_level > 0 OR vip_exp > 0;

-- 2. 为用户添加新的EXP字段（如果不存在）
ALTER TABLE app_users 
ADD COLUMN IF NOT EXISTS total_exp BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_exp_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 3. 将现有的vip_exp迁移到total_exp字段
UPDATE app_users 
SET total_exp = COALESCE(vip_exp, 0),
    last_exp_update = CURRENT_TIMESTAMP
WHERE id > 0;

-- 4. 根据新的EXP要求重新计算用户VIP等级
UPDATE app_users 
SET vip_level = (
    SELECT COALESCE(MAX(vip_level), 0)
    FROM vip_configs 
    WHERE required_exp <= app_users.total_exp 
    AND status = 1
)
WHERE id > 0;

-- 5. 创建EXP变更记录表（用于审计）
CREATE TABLE IF NOT EXISTS vip_exp_migration_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    old_vip_level INTEGER,
    new_vip_level INTEGER,
    old_vip_exp BIGINT,
    new_total_exp BIGINT,
    migration_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    migration_reason VARCHAR(255) DEFAULT 'V12.0 System Migration'
);

-- 6. 记录迁移日志
INSERT INTO vip_exp_migration_log (
    user_id, old_vip_level, new_vip_level, old_vip_exp, new_total_exp
)
SELECT 
    u.id,
    b.vip_level as old_vip_level,
    u.vip_level as new_vip_level,
    b.vip_exp as old_vip_exp,
    u.total_exp as new_total_exp
FROM app_users u
LEFT JOIN user_vip_backup b ON u.id = b.id
WHERE u.id > 0;

-- 7. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_app_users_total_exp ON app_users(total_exp);
CREATE INDEX IF NOT EXISTS idx_app_users_vip_level_exp ON app_users(vip_level, total_exp);
CREATE INDEX IF NOT EXISTS idx_vip_configs_required_exp ON vip_configs(required_exp);
CREATE INDEX IF NOT EXISTS idx_vip_configs_level_status ON vip_configs(vip_level, status);

-- 8. 更新统计信息
ANALYZE app_users;
ANALYZE vip_configs;

-- 9. 验证迁移结果
SELECT 
    'Migration Summary' as report_type,
    COUNT(*) as total_users,
    COUNT(CASE WHEN total_exp > 0 THEN 1 END) as users_with_exp,
    AVG(total_exp) as avg_exp,
    MAX(total_exp) as max_exp,
    COUNT(DISTINCT vip_level) as vip_levels_in_use
FROM app_users;

-- 显示各VIP等级用户分布
SELECT 
    v.vip_level,
    v.level_name,
    v.required_exp,
    COUNT(u.id) as user_count,
    ROUND(COUNT(u.id) * 100.0 / (SELECT COUNT(*) FROM app_users), 2) as percentage
FROM vip_configs v
LEFT JOIN app_users u ON u.vip_level = v.vip_level
WHERE v.status = 1
GROUP BY v.vip_level, v.level_name, v.required_exp
ORDER BY v.vip_level;

-- 显示迁移前后对比
SELECT 
    'Before Migration' as period,
    old_vip_level as vip_level,
    COUNT(*) as user_count
FROM vip_exp_migration_log
WHERE old_vip_level IS NOT NULL
GROUP BY old_vip_level
UNION ALL
SELECT 
    'After Migration' as period,
    new_vip_level as vip_level,
    COUNT(*) as user_count
FROM vip_exp_migration_log
WHERE new_vip_level IS NOT NULL
GROUP BY new_vip_level
ORDER BY period, vip_level;
