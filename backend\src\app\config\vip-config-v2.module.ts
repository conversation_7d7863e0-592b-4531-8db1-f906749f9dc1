import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VipConfigV2Service } from './services/vip-config-v2.service';
import { VipExpService } from './services/vip-exp.service';
import { VipConfigController } from './controllers/vip-config-v2.controller';
import { VipConfigV2 } from './entities/vip-config-v2.entity';
import { AppUser } from '../entities/app-user.entity';
import { CashTransaction } from '../entities/cash-transaction.entity';
import { GoldTransaction } from '../entities/gold-transaction.entity';

/**
 * VIP配置模块 V12.0 - 经济权益系统
 * 支持完整的VIP经济权益配置和EXP经验值系统
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      VipConfigV2,
      AppUser,
      CashTransaction,
      GoldTransaction,
    ]),
  ],
  controllers: [VipConfigController],
  providers: [VipConfigV2Service, VipExpService],
  exports: [VipConfigV2Service, VipExpService],
})
export class VipConfigV2Module {}
