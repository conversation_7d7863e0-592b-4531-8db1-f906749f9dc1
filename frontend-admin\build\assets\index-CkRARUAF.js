import{j as r}from"./index-DD0gcXtR.js";import"./react-BUTTOX-3.js";import{B as c}from"./index-C1Do55qr.js";import{u as m}from"./index-BpqjFj86.js";import p from"./bar-chart-N0HwQqFz.js";import l from"./card-list-B95J1Fzm.js";import x from"./line-chart-CLTPE3YQ.js";import j from"./pie-chart-CN3xEhuh.js";import{ao as e,ap as t,d,s as o}from"./antd-FyCAPQKa.js";import"./index-ct7iarNQ.js";import"./home-C9CLb8Ik.js";const i={xs:24,sm:24,md:12,lg:12,xl:12,xxl:12};function L(){const n=async()=>{try{console.log("🔍 测试用户管理API...");const s=await m.getUserList({page:1,pageSize:10});console.log("✅ API调用成功:",s),o.success("用户管理API调用成功！")}catch(s){console.error("❌ API调用失败:",s);const a=s instanceof Error?s.message:String(s);o.error(`API调用失败: ${a}`)}};return r.jsx(c,{children:r.jsxs(e,{gutter:[20,20],children:[r.jsx(t,{span:24,children:r.jsx(d,{type:"primary",onClick:n,style:{marginBottom:16},children:"测试用户管理API"})}),r.jsx(t,{span:24,children:r.jsx(l,{})}),r.jsx(t,{span:24,children:r.jsx(x,{})}),r.jsx(t,{span:24,children:r.jsxs(e,{justify:"space-between",gutter:[20,20],children:[r.jsx(t,{...i,children:r.jsx(p,{})}),r.jsx(t,{...i,children:r.jsx(j,{})})]})})]})})}export{L as default};
