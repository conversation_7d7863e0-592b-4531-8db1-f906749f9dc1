import{u as nt,j as V}from"./index-DD0gcXtR.js";import{n as rt,c as at,g as it,a as st}from"./react-BUTTOX-3.js";import{ac as ot,b1 as ut,b2 as lt,b3 as ct,ao as ft,ap as dt,aq as ht,d as mt}from"./antd-FyCAPQKa.js";var G={},H=function(){return H=Object.assign||function(s){for(var i,u=1,l=arguments.length;u<l;u++)for(var n in i=arguments[u])Object.prototype.hasOwnProperty.call(i,n)&&(s[n]=i[n]);return s},H.apply(this,arguments)},pt=function(){function s(i,u,l){var n=this;this.endVal=u,this.options=l,this.version="2.8.0",this.defaults={startVal:0,decimalPlaces:0,duration:2,useEasing:!0,useGrouping:!0,useIndianSeparators:!1,smartEasingThreshold:999,smartEasingAmount:333,separator:",",decimal:".",prefix:"",suffix:"",enableScrollSpy:!1,scrollSpyDelay:200,scrollSpyOnce:!1},this.finalEndVal=null,this.useEasing=!0,this.countDown=!1,this.error="",this.startVal=0,this.paused=!0,this.once=!1,this.count=function(f){n.startTime||(n.startTime=f);var g=f-n.startTime;n.remaining=n.duration-g,n.useEasing?n.countDown?n.frameVal=n.startVal-n.easingFn(g,0,n.startVal-n.endVal,n.duration):n.frameVal=n.easingFn(g,n.startVal,n.endVal-n.startVal,n.duration):n.frameVal=n.startVal+(n.endVal-n.startVal)*(g/n.duration);var h=n.countDown?n.frameVal<n.endVal:n.frameVal>n.endVal;n.frameVal=h?n.endVal:n.frameVal,n.frameVal=Number(n.frameVal.toFixed(n.options.decimalPlaces)),n.printValue(n.frameVal),g<n.duration?n.rAF=requestAnimationFrame(n.count):n.finalEndVal!==null?n.update(n.finalEndVal):n.options.onCompleteCallback&&n.options.onCompleteCallback()},this.formatNumber=function(f){var g,h,w,C,$=f<0?"-":"";g=Math.abs(f).toFixed(n.options.decimalPlaces);var I=(g+="").split(".");if(h=I[0],w=I.length>1?n.options.decimal+I[1]:"",n.options.useGrouping){C="";for(var k=3,_=0,F=0,B=h.length;F<B;++F)n.options.useIndianSeparators&&F===4&&(k=2,_=1),F!==0&&_%k==0&&(C=n.options.separator+C),_++,C=h[B-F-1]+C;h=C}return n.options.numerals&&n.options.numerals.length&&(h=h.replace(/[0-9]/g,function(M){return n.options.numerals[+M]}),w=w.replace(/[0-9]/g,function(M){return n.options.numerals[+M]})),$+n.options.prefix+h+w+n.options.suffix},this.easeOutExpo=function(f,g,h,w){return h*(1-Math.pow(2,-10*f/w))*1024/1023+g},this.options=H(H({},this.defaults),l),this.formattingFn=this.options.formattingFn?this.options.formattingFn:this.formatNumber,this.easingFn=this.options.easingFn?this.options.easingFn:this.easeOutExpo,this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.endVal=this.validateValue(u),this.options.decimalPlaces=Math.max(this.options.decimalPlaces),this.resetDuration(),this.options.separator=String(this.options.separator),this.useEasing=this.options.useEasing,this.options.separator===""&&(this.options.useGrouping=!1),this.el=typeof i=="string"?document.getElementById(i):i,this.el?this.printValue(this.startVal):this.error="[CountUp] target is null or undefined",typeof window<"u"&&this.options.enableScrollSpy&&(this.error?console.error(this.error,i):(window.onScrollFns=window.onScrollFns||[],window.onScrollFns.push(function(){return n.handleScroll(n)}),window.onscroll=function(){window.onScrollFns.forEach(function(f){return f()})},this.handleScroll(this)))}return s.prototype.handleScroll=function(i){if(i&&window&&!i.once){var u=window.innerHeight+window.scrollY,l=i.el.getBoundingClientRect(),n=l.top+window.pageYOffset,f=l.top+l.height+window.pageYOffset;f<u&&f>window.scrollY&&i.paused?(i.paused=!1,setTimeout(function(){return i.start()},i.options.scrollSpyDelay),i.options.scrollSpyOnce&&(i.once=!0)):(window.scrollY>f||n>u)&&!i.paused&&i.reset()}},s.prototype.determineDirectionAndSmartEasing=function(){var i=this.finalEndVal?this.finalEndVal:this.endVal;this.countDown=this.startVal>i;var u=i-this.startVal;if(Math.abs(u)>this.options.smartEasingThreshold&&this.options.useEasing){this.finalEndVal=i;var l=this.countDown?1:-1;this.endVal=i+l*this.options.smartEasingAmount,this.duration=this.duration/2}else this.endVal=i,this.finalEndVal=null;this.finalEndVal!==null?this.useEasing=!1:this.useEasing=this.options.useEasing},s.prototype.start=function(i){this.error||(this.options.onStartCallback&&this.options.onStartCallback(),i&&(this.options.onCompleteCallback=i),this.duration>0?(this.determineDirectionAndSmartEasing(),this.paused=!1,this.rAF=requestAnimationFrame(this.count)):this.printValue(this.endVal))},s.prototype.pauseResume=function(){this.paused?(this.startTime=null,this.duration=this.remaining,this.startVal=this.frameVal,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count)):cancelAnimationFrame(this.rAF),this.paused=!this.paused},s.prototype.reset=function(){cancelAnimationFrame(this.rAF),this.paused=!0,this.resetDuration(),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.printValue(this.startVal)},s.prototype.update=function(i){cancelAnimationFrame(this.rAF),this.startTime=null,this.endVal=this.validateValue(i),this.endVal!==this.frameVal&&(this.startVal=this.frameVal,this.finalEndVal==null&&this.resetDuration(),this.finalEndVal=null,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count))},s.prototype.printValue=function(i){var u;if(this.el){var l=this.formattingFn(i);!((u=this.options.plugin)===null||u===void 0)&&u.render?this.options.plugin.render(this.el,l):this.el.tagName==="INPUT"?this.el.value=l:this.el.tagName==="text"||this.el.tagName==="tspan"?this.el.textContent=l:this.el.innerHTML=l}},s.prototype.ensureNumber=function(i){return typeof i=="number"&&!isNaN(i)},s.prototype.validateValue=function(i){var u=Number(i);return this.ensureNumber(u)?u:(this.error="[CountUp] invalid start or end value: ".concat(i),null)},s.prototype.resetDuration=function(){this.startTime=null,this.duration=1e3*Number(this.options.duration),this.remaining=this.duration},s}();const gt=Object.freeze(Object.defineProperty({__proto__:null,CountUp:pt},Symbol.toStringTag,{value:"Module"})),yt=rt(gt);var J;function vt(){if(J)return G;J=1,Object.defineProperty(G,"__esModule",{value:!0});var s=at(),i=yt;function u(r,t){var e=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(e!=null){var a,o,c,E,y=[],d=!0,m=!1;try{if(c=(e=e.call(r)).next,t!==0)for(;!(d=(a=c.call(e)).done)&&(y.push(a.value),y.length!==t);d=!0);}catch(p){m=!0,o=p}finally{try{if(!d&&e.return!=null&&(E=e.return(),Object(E)!==E))return}finally{if(m)throw o}}return y}}function l(r,t){var e=Object.keys(r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(r);t&&(a=a.filter(function(o){return Object.getOwnPropertyDescriptor(r,o).enumerable})),e.push.apply(e,a)}return e}function n(r){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?l(Object(e),!0).forEach(function(a){h(r,a,e[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(e)):l(Object(e)).forEach(function(a){Object.defineProperty(r,a,Object.getOwnPropertyDescriptor(e,a))})}return r}function f(r,t){if(typeof r!="object"||!r)return r;var e=r[Symbol.toPrimitive];if(e!==void 0){var a=e.call(r,t);if(typeof a!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(r)}function g(r){var t=f(r,"string");return typeof t=="symbol"?t:String(t)}function h(r,t,e){return t=g(t),t in r?Object.defineProperty(r,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):r[t]=e,r}function w(){return w=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a])}return r},w.apply(this,arguments)}function C(r,t){if(r==null)return{};var e={},a=Object.keys(r),o,c;for(c=0;c<a.length;c++)o=a[c],!(t.indexOf(o)>=0)&&(e[o]=r[o]);return e}function $(r,t){if(r==null)return{};var e=C(r,t),a,o;if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(r);for(o=0;o<c.length;o++)a=c[o],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(r,a)&&(e[a]=r[a])}return e}function I(r,t){return k(r)||u(r,t)||_(r,t)||B()}function k(r){if(Array.isArray(r))return r}function _(r,t){if(r){if(typeof r=="string")return F(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);if(e==="Object"&&r.constructor&&(e=r.constructor.name),e==="Map"||e==="Set")return Array.from(r);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return F(r,t)}}function F(r,t){(t==null||t>r.length)&&(t=r.length);for(var e=0,a=new Array(t);e<t;e++)a[e]=r[e];return a}function B(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var M=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?s.useLayoutEffect:s.useEffect;function v(r){var t=s.useRef(r);return M(function(){t.current=r}),s.useCallback(function(){for(var e=arguments.length,a=new Array(e),o=0;o<e;o++)a[o]=arguments[o];return t.current.apply(void 0,a)},[])}var Q=function(t,e){var a=e.decimal,o=e.decimals,c=e.duration,E=e.easingFn,y=e.end,d=e.formattingFn,m=e.numerals,p=e.prefix,P=e.separator,A=e.start,U=e.suffix,D=e.useEasing,O=e.useGrouping,S=e.useIndianSeparators,N=e.enableScrollSpy,b=e.scrollSpyDelay,T=e.scrollSpyOnce,j=e.plugin;return new i.CountUp(t,y,{startVal:A,duration:c,decimal:a,decimalPlaces:o,easingFn:E,formattingFn:d,numerals:m,separator:P,prefix:p,suffix:U,plugin:j,useEasing:D,useIndianSeparators:S,useGrouping:O,enableScrollSpy:N,scrollSpyDelay:b,scrollSpyOnce:T})},X=["ref","startOnMount","enableReinitialize","delay","onEnd","onStart","onPauseResume","onReset","onUpdate"],Z={decimal:".",separator:",",delay:null,prefix:"",suffix:"",duration:2,start:0,decimals:0,startOnMount:!0,enableReinitialize:!0,useEasing:!0,useGrouping:!0,useIndianSeparators:!1},K=function(t){var e=Object.fromEntries(Object.entries(t).filter(function(x){var L=I(x,2),Y=L[1];return Y!==void 0})),a=s.useMemo(function(){return n(n({},Z),e)},[t]),o=a.ref,c=a.startOnMount,E=a.enableReinitialize,y=a.delay,d=a.onEnd,m=a.onStart,p=a.onPauseResume,P=a.onReset,A=a.onUpdate,U=$(a,X),D=s.useRef(),O=s.useRef(),S=s.useRef(!1),N=v(function(){return Q(typeof o=="string"?o:o.current,U)}),b=v(function(x){var L=D.current;if(L&&!x)return L;var Y=N();return D.current=Y,Y}),T=v(function(){var x=function(){return b(!0).start(function(){d==null||d({pauseResume:j,reset:R,start:q,update:z})})};y&&y>0?O.current=setTimeout(x,y*1e3):x(),m==null||m({pauseResume:j,reset:R,update:z})}),j=v(function(){b().pauseResume(),p==null||p({reset:R,start:q,update:z})}),R=v(function(){b().el&&(O.current&&clearTimeout(O.current),b().reset(),P==null||P({pauseResume:j,start:q,update:z}))}),z=v(function(x){b().update(x),A==null||A({pauseResume:j,reset:R,start:q})}),q=v(function(){R(),T()}),W=v(function(x){c&&(x&&R(),T())});return s.useEffect(function(){S.current?E&&W(!0):(S.current=!0,W())},[E,S,W,y,t.start,t.suffix,t.prefix,t.duration,t.separator,t.decimals,t.decimal,t.formattingFn]),s.useEffect(function(){return function(){R()}},[R]),{start:q,pauseResume:j,reset:R,update:z,getCountUp:b}},tt=["className","redraw","containerProps","children","style"],et=function(t){var e=t.className,a=t.redraw,o=t.containerProps,c=t.children,E=t.style,y=$(t,tt),d=s.useRef(null),m=s.useRef(!1),p=K(n(n({},y),{},{ref:d,startOnMount:typeof c!="function"||t.delay===0,enableReinitialize:!1})),P=p.start,A=p.reset,U=p.update,D=p.pauseResume,O=p.getCountUp,S=v(function(){P()}),N=v(function(j){t.preserveValue||A(),U(j)}),b=v(function(){if(typeof t.children=="function"&&!(d.current instanceof Element)){console.error(`Couldn't find attached element to hook the CountUp instance into! Try to attach "containerRef" from the render prop to a an Element, eg. <span ref={containerRef} />.`);return}O()});s.useEffect(function(){b()},[b]),s.useEffect(function(){m.current&&N(t.end)},[t.end,N]);var T=a&&t;return s.useEffect(function(){a&&m.current&&S()},[S,a,T]),s.useEffect(function(){!a&&m.current&&S()},[S,a,t.start,t.suffix,t.prefix,t.duration,t.separator,t.decimals,t.decimal,t.className,t.formattingFn]),s.useEffect(function(){m.current=!0},[]),typeof c=="function"?c({countUpRef:d,start:P,reset:A,update:U,pauseResume:D,getCountUp:O}):s.createElement("span",w({className:e,ref:d,style:E},o),typeof t.start<"u"?O().formattingFn(t.start):"")};return G.default=et,G.useCountUp=K,G}var bt=vt();const Vt=it(bt),wt={xs:24,sm:24,md:12,lg:12,xl:12,xxl:6};function Ot(){const{t:s}=nt(),i=[{title:s("home.newVisits"),data:102400,icon:V.jsx(ot,{})},{title:s("home.messages"),data:81212,icon:V.jsx(ut,{})},{title:s("home.purchases"),data:9280,icon:V.jsx(lt,{})},{title:s("home.shoppings"),data:13600,icon:V.jsx(ct,{})}];return V.jsx(ft,{justify:"space-between",gutter:[20,20],children:i.map(u=>st.createElement(dt,{...wt,key:u.title},V.jsx(ht,{className:"",children:V.jsxs("div",{className:"flex justify-between items-center",children:[V.jsxs("div",{className:"flex flex-col",children:[V.jsx("h3",{className:"text-xl",children:u.title}),V.jsx(Vt,{end:u.data,separator:","})]}),V.jsx(mt,{className:"text-3xl",icon:u.icon,type:"text"})]})})))})}export{Ot as default};
