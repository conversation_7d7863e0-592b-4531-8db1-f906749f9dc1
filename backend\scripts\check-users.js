// 检查数据库中的用户
const { DataSource } = require('typeorm');
const { SysUser } = require('../dist/system/entities/sys-user.entity');
const { SysRole } = require('../dist/system/entities/sys-role.entity');
const { SysPermission } = require('../dist/system/entities/sys-permission.entity');
const { SysMenu } = require('../dist/system/entities/sys-menu.entity');

async function checkUsers() {
  const dataSource = new DataSource({
    type: 'postgres',
    host: '**************',
    port: 5435,
    username: 'user_jJSpPW',
    password: 'password_DmrhYX',
    database: 'inapp2',
    entities: [SysUser, SysRole, SysPermission, SysMenu],
    synchronize: false,
    logging: false,
  });

  try {
    await dataSource.initialize();
    console.log('🔗 数据库连接成功');

    const userRepository = dataSource.getRepository(SysUser);
    
    // 查询所有用户
    console.log('\n📋 查询所有用户:');
    const users = await userRepository.find({
      relations: ['roles']
    });
    
    console.log(`  找到 ${users.length} 个用户:`);
    users.forEach((user, index) => {
      console.log(`    用户${index + 1}:`);
      console.log(`      ID: ${user.id}`);
      console.log(`      用户名: ${user.username}`);
      console.log(`      邮箱: ${user.email}`);
      console.log(`      状态: ${user.status}`);
      console.log(`      超级管理员: ${user.isSuperAdmin}`);
      console.log(`      角色数量: ${user.roles ? user.roles.length : 0}`);
      if (user.roles && user.roles.length > 0) {
        user.roles.forEach((role, roleIndex) => {
          console.log(`        角色${roleIndex + 1}: ${role.name} (${role.code})`);
        });
      }
      console.log(`      密码哈希: ${user.password ? user.password.substring(0, 20) + '...' : '无密码'}`);
      console.log('');
    });

    // 特别检查admin用户
    console.log('\n📋 特别检查admin用户:');
    const adminUser = await userRepository.findOne({
      where: { username: 'admin' },
      relations: ['roles']
    });
    
    if (adminUser) {
      console.log('  ✅ 找到admin用户');
      console.log(`    ID: ${adminUser.id}`);
      console.log(`    用户名: ${adminUser.username}`);
      console.log(`    邮箱: ${adminUser.email}`);
      console.log(`    状态: ${adminUser.status}`);
      console.log(`    超级管理员: ${adminUser.isSuperAdmin}`);
      console.log(`    角色数量: ${adminUser.roles ? adminUser.roles.length : 0}`);
      console.log(`    密码哈希: ${adminUser.password || '无密码'}`);
      
      if (adminUser.roles && adminUser.roles.length > 0) {
        adminUser.roles.forEach((role, index) => {
          console.log(`      角色${index + 1}: ${role.name} (${role.code})`);
        });
      }
    } else {
      console.log('  ❌ 没有找到admin用户');
    }

    await dataSource.destroy();
    console.log('\n✅ 检查完成');

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 执行检查
if (require.main === module) {
  checkUsers()
    .then(() => {
      console.log('✅ 检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 检查失败:', error);
      process.exit(1);
    });
}

module.exports = { checkUsers };
