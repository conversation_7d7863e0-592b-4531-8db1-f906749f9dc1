const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testMenuServiceDirect() {
  try {
    console.log('🔍 直接测试菜单服务...');
    
    // 创建应用实例
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // 获取菜单服务
    const { SystemMenuService } = require('../dist/system/menu/menu.service');
    const menuService = app.get(SystemMenuService);
    
    // 测试超级管理员获取菜单
    console.log('\n📋 超级管理员获取菜单:');
    const userRoles = ['admin', 'super_admin'];
    const isSuperAdmin = true;
    const userPermissions = ['config:vip:view']; // 包含VIP权限
    
    const menus = await menuService.getUserMenus(userRoles, isSuperAdmin, userPermissions);
    
    console.log(`菜单总数: ${menus.length}`);
    
    // 详细显示每个菜单
    menus.forEach((menu, index) => {
      console.log(`\n菜单 ${index + 1}:`);
      console.log(`  路径: ${menu.path}`);
      console.log(`  标题: ${menu.handle?.title}`);
      console.log(`  组件: ${menu.component || '无'}`);
      console.log(`  权限代码: ${menu.handle?.permissionCode || '无'}`);
      console.log(`  图标: ${menu.handle?.icon || '无'}`);
      
      if (menu.children && menu.children.length > 0) {
        console.log(`  子菜单数量: ${menu.children.length}`);
        menu.children.forEach((child, childIndex) => {
          console.log(`    子菜单 ${childIndex + 1}:`);
          console.log(`      路径: ${child.path}`);
          console.log(`      标题: ${child.handle?.title}`);
          console.log(`      组件: ${child.component || '无'}`);
          console.log(`      权限代码: ${child.handle?.permissionCode || '无'}`);
        });
      }
    });
    
    // 特别检查VIP菜单
    const vipMenu = menus.find(menu => 
      menu.path === '/config' && 
      menu.children && 
      menu.children.some(child => child.path === '/config/vip')
    );
    
    if (vipMenu) {
      const vipChild = vipMenu.children.find(child => child.path === '/config/vip');
      console.log('\n🎯 找到VIP菜单:');
      console.log(`  路径: ${vipChild.path}`);
      console.log(`  标题: ${vipChild.handle?.title}`);
      console.log(`  组件: ${vipChild.component}`);
      console.log(`  权限代码: ${vipChild.handle?.permissionCode}`);
    } else {
      console.log('\n❌ 未找到VIP菜单');
    }

    await app.close();
    
  } catch (error) {
    console.error('测试失败:', error.message);
    console.error(error.stack);
  }
}

testMenuServiceDirect();
