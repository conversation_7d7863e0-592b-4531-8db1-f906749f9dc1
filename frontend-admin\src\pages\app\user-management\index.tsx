import { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Tag,
  Modal,
  Form,
  message,
  Tooltip,
  Drawer,
  Descriptions,
  Badge,
  Popconfirm,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  StopOutlined,
  CheckCircleOutlined,
  TagsOutlined,
  TeamOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { userManagementApi } from '#src/api/user-management';
import type {
  AppUserListItem,
  QueryAppUserParams,
  UpdateUserStatusParams,
  UpdateUserTagsParams,
} from '#src/api/user-management/types';
import InvitationTree from './components/InvitationTree';
import BalanceDetailModal from './components/BalanceDetailModal';

const { RangePicker } = DatePicker;
const { Option } = Select;

// 用户状态枚举
const UserStatus = {
  NORMAL: 0,
  BANNED: 1,
  DEACTIVATED: 2,
} as const;

// 账户类型枚举
const AccountType = {
  NORMAL_USER: 0,
  INTERNAL_EMPLOYEE: 1,
} as const;



const UserManagement: React.FC = () => {
  const [form] = Form.useForm();
  const [tagForm] = Form.useForm();
  const [searchParams, setSearchParams] = useState<QueryAppUserParams>({
    page: 1,
    pageSize: 20,
  });
  const [selectedUser, setSelectedUser] = useState<AppUserListItem | null>(null);
  const [userDetailVisible, setUserDetailVisible] = useState(false);
  const [tagModalVisible, setTagModalVisible] = useState(false);
  const [invitationVisible, setInvitationVisible] = useState(false);
  const [balanceDetailVisible, setBalanceDetailVisible] = useState(false);

  // 获取用户列表
  const {
    data: userListData,
    loading: userListLoading,
    run: fetchUserList,
  } = useRequest(
    () => {
      console.log('🔍 前端发起用户列表请求，参数:', searchParams);
      return userManagementApi.getUserList(searchParams).then(response => {
        console.log('📊 前端收到用户列表响应:', response);
        return response;
      });
    },
    {
      refreshDeps: [searchParams],
    },
  );

  // 获取用户详情
  const { run: fetchUserDetail, loading: userDetailLoading } = useRequest(
    (id: number) => userManagementApi.getUserDetail(id),
    {
      manual: true,
      onSuccess: (data) => {
        setSelectedUser(data);
        setUserDetailVisible(true);
      },
    },
  );

  // 更新用户状态
  const { run: updateUserStatus, loading: updateStatusLoading } = useRequest(
    (id: number, params: UpdateUserStatusParams) =>
      userManagementApi.updateUserStatus(id, params),
    {
      manual: true,
      onSuccess: () => {
        message.success('用户状态更新成功');
        fetchUserList();
      },
    },
  );

  // 更新用户标签
  const { run: updateUserTags, loading: updateTagsLoading } = useRequest(
    (id: number, params: UpdateUserTagsParams) =>
      userManagementApi.updateUserTags(id, params),
    {
      manual: true,
      onSuccess: () => {
        message.success('用户标签更新成功');
        setTagModalVisible(false);
        fetchUserList();
      },
    },
  );

  // 获取邀请关系
  const { run: fetchInvitationRelationship, loading: invitationLoading } = useRequest(
    (id: number) => {
      console.log('🔍 前端发起邀请关系请求，用户ID:', id);
      return userManagementApi.getInvitationRelationship(id).then(response => {
        console.log('📊 前端收到邀请关系响应:', response);
        return response;
      }).catch(error => {
        console.error('❌ 邀请关系请求失败:', error);
        throw error;
      });
    },
    {
      manual: true,
      onSuccess: (data) => {
        console.log('✅ 邀请关系请求成功:', data);
        setInvitationVisible(true);
      },
      onError: (error) => {
        console.error('❌ 邀请关系请求错误:', error);
        message.error('获取邀请关系失败');
      },
    },
  );

  // 搜索处理
  const handleSearch = (values: any) => {
    const params: any = {
      page: 1,
      pageSize: searchParams.pageSize,
      ...values,
    };

    // 处理日期范围
    if (values.dateRange && values.dateRange.length === 2) {
      params.startDate = values.dateRange[0].format('YYYY-MM-DD');
      params.endDate = values.dateRange[1].format('YYYY-MM-DD');
    }

    // 删除dateRange字段，因为后端不需要它
    delete params.dateRange;

    setSearchParams(params);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setSearchParams({
      page: 1,
      pageSize: 20,
    });
  };

  // 处理分页
  const handleTableChange = (pagination: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 封禁/解封用户
  const handleUpdateStatus = (user: AppUserListItem, status: number) => {
    const statusText = status === UserStatus.BANNED ? '封禁' : '解封';
    
    Modal.confirm({
      title: `确认${statusText}用户`,
      content: `确定要${statusText}用户 "${user.username}" 吗？`,
      icon: <ExclamationCircleOutlined />,
      onOk: () => {
        updateUserStatus(user.id, { status });
      },
    });
  };

  // 打开标签编辑
  const handleEditTags = (user: AppUserListItem) => {
    setSelectedUser(user);
    tagForm.setFieldsValue({
      tags: user.tags || [],
    });
    setTagModalVisible(true);
  };

  // 保存标签
  const handleSaveTags = () => {
    tagForm.validateFields().then((values) => {
      if (selectedUser) {
        updateUserTags(selectedUser.id, values);
      }
    });
  };

  // 查看邀请关系
  const handleViewInvitation = (user: AppUserListItem) => {
    setSelectedUser(user);
    fetchInvitationRelationship(user.id);
  };

  // 查看余额详情
  const handleViewBalanceDetail = (user: AppUserListItem) => {
    setSelectedUser(user);
    setBalanceDetailVisible(true);
  };

  // 状态渲染
  const renderStatus = (status: number) => {
    const statusMap: Record<number, { color: string; text: string }> = {
      [UserStatus.NORMAL]: { color: 'green', text: '正常' },
      [UserStatus.BANNED]: { color: 'red', text: '已封禁' },
      [UserStatus.DEACTIVATED]: { color: 'gray', text: '已注销' },
    };
    const config = statusMap[status] || { color: 'default', text: '未知' };
    return <Badge status={config.color as any} text={config.text} />;
  };



  // 表格列定义
  const columns: ColumnsType<AppUserListItem> = [
    {
      title: 'UID',
      dataIndex: 'uid',
      width: 100,
      fixed: 'left',
    },
    {
      title: '用户名',
      dataIndex: 'username',
      width: 120,
      fixed: 'left',
      render: (username: string, record: AppUserListItem) => {
        const isInactive = record.daysNotLoggedIn > 3;
        const color = isInactive ? '#ff4d4f' : '#1890ff';

        // 创建标签卡片内容
        const tagContent = record.tags && record.tags.length > 0 ? (
          <div style={{ maxWidth: 200 }}>
            <div style={{ marginBottom: 8, fontWeight: 'bold' }}>用户标签</div>
            <Space wrap>
              {record.tags.map((tag) => (
                <Tag key={tag} color="blue">
                  {tag}
                </Tag>
              ))}
            </Space>
          </div>
        ) : (
          <div>暂无标签</div>
        );

        return (
          <Tooltip title={tagContent} placement="right">
            <span style={{ color, cursor: 'pointer' }}>
              {username}
            </span>
          </Tooltip>
        );
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      width: 180,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: renderStatus,
    },
    {
      title: 'VIP',
      dataIndex: 'vipLevel',
      width: 80,
      render: (level: number) => <Tag color="gold">V{level}</Tag>,
    },
    {
      title: '充值余额',
      dataIndex: 'rechargeBalance',
      width: 120,
      render: (balance: any, record: AppUserListItem) => {
        const num = Number(balance) || 0;
        return (
          <Button
            type="link"
            size="small"
            onClick={() => handleViewBalanceDetail(record)}
            style={{ padding: 0, height: 'auto' }}
          >
            {num.toFixed(4)}
          </Button>
        );
      },
    },
    {
      title: '金币余额',
      dataIndex: 'goldBalance',
      width: 120,
      render: (balance: any, record: AppUserListItem) => {
        const num = Number(balance) || 0;
        return (
          <Button
            type="link"
            size="small"
            onClick={() => handleViewBalanceDetail(record)}
            style={{ padding: 0, height: 'auto' }}
          >
            {num.toLocaleString()}
          </Button>
        );
      },
    },
    {
      title: '可提现余额',
      dataIndex: 'withdrawableBalance',
      width: 120,
      render: (balance: any, record: AppUserListItem) => {
        const num = Number(balance) || 0;
        return (
          <Button
            type="link"
            size="small"
            onClick={() => handleViewBalanceDetail(record)}
            style={{ padding: 0, height: 'auto' }}
          >
            {num.toFixed(4)}
          </Button>
        );
      },
    },
    {
      title: '渠道',
      dataIndex: 'channelName',
      width: 120,
    },
    {
      title: '广告标识',
      dataIndex: 'adIdentifier',
      width: 120,
      render: (adIdentifier: string) => adIdentifier || '-',
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginTime',
      width: 160,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '从未登录',
    },
    {
      title: '未登天数',
      dataIndex: 'daysNotLoggedIn',
      width: 100,
      render: (days: number) => {
        if (days === 0) return '今天';
        if (days === 1) return '1天';
        if (days <= 7) return <Tag color="orange">{days}天</Tag>;
        if (days <= 30) return <Tag color="red">{days}天</Tag>;
        return <Tag color="volcano">{days}天</Tag>;
      },
    },
    {
      title: '注册时间',
      dataIndex: 'createTime',
      width: 160,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => fetchUserDetail(record.id)}
            />
          </Tooltip>
          
          {record.status === UserStatus.NORMAL ? (
            <Tooltip title="封禁用户">
              <Button
                type="link"
                size="small"
                danger
                icon={<StopOutlined />}
                onClick={() => handleUpdateStatus(record, UserStatus.BANNED)}
              />
            </Tooltip>
          ) : (
            <Tooltip title="解封用户">
              <Button
                type="link"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleUpdateStatus(record, UserStatus.NORMAL)}
              />
            </Tooltip>
          )}
          
          <Tooltip title="编辑标签">
            <Button
              type="link"
              size="small"
              icon={<TagsOutlined />}
              onClick={() => handleEditTags(record)}
            />
          </Tooltip>
          
          <Tooltip title="邀请关系">
            <Button
              type="link"
              size="small"
              icon={<TeamOutlined />}
              onClick={() => handleViewInvitation(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <Card title="APP用户列表" className="mb-4">
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          className="mb-4"
        >
          <Form.Item name="id">
            <Input placeholder="用户ID" type="number" />
          </Form.Item>

          <Form.Item name="username">
            <Input placeholder="用户名" />
          </Form.Item>

          <Form.Item name="email">
            <Input placeholder="邮箱" />
          </Form.Item>

          <Form.Item name="phone">
            <Input placeholder="手机号" />
          </Form.Item>
          
          <Form.Item name="status">
            <Select placeholder="用户状态" allowClear style={{ width: 120 }}>
              <Option value={UserStatus.NORMAL}>正常</Option>
              <Option value={UserStatus.BANNED}>已封禁</Option>
              <Option value={UserStatus.DEACTIVATED}>已注销</Option>
            </Select>
          </Form.Item>

          <Form.Item name="dateRange">
            <RangePicker placeholder={['开始日期', '结束日期']} />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
                loading={userListLoading}
              >
                搜索
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={userListData?.list || []}
          rowKey="id"
          loading={userListLoading}
          scroll={{ x: 2100 }}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: userListData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 用户详情抽屉 */}
      <Drawer
        title="用户详情"
        placement="right"
        width={600}
        open={userDetailVisible}
        onClose={() => setUserDetailVisible(false)}
        loading={userDetailLoading}
      >
        {selectedUser && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="UID">{selectedUser.uid}</Descriptions.Item>
            <Descriptions.Item label="用户名">{selectedUser.username}</Descriptions.Item>
            <Descriptions.Item label="邮箱">{selectedUser.email}</Descriptions.Item>
            <Descriptions.Item label="手机号">{selectedUser.phone}</Descriptions.Item>
            <Descriptions.Item label="状态">{renderStatus(selectedUser.status)}</Descriptions.Item>
            <Descriptions.Item label="账户类型">
              {selectedUser.accountType === AccountType.INTERNAL_EMPLOYEE ? '内部员工' : '普通用户'}
            </Descriptions.Item>
            <Descriptions.Item label="VIP等级">V{selectedUser.vipLevel}</Descriptions.Item>
            <Descriptions.Item label="充值余额">{(Number(selectedUser.rechargeBalance) || 0).toFixed(4)}</Descriptions.Item>
            <Descriptions.Item label="金币余额">{(Number(selectedUser.goldBalance) || 0).toLocaleString()}</Descriptions.Item>
            <Descriptions.Item label="可提现余额">{(Number(selectedUser.withdrawableBalance) || 0).toFixed(4)}</Descriptions.Item>
            <Descriptions.Item label="渠道">{selectedUser.channelName || '-'}</Descriptions.Item>
            <Descriptions.Item label="广告">{selectedUser.adName || '-'}</Descriptions.Item>
            <Descriptions.Item label="获取标签">{selectedUser.acquisitionTag || '-'}</Descriptions.Item>
            <Descriptions.Item label="用户标签">
              <Space wrap>
                {selectedUser.tags?.map((tag) => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="注册IP">{selectedUser.registerIp}</Descriptions.Item>
            <Descriptions.Item label="最后登录时间">
              {selectedUser.lastLoginTime ? dayjs(selectedUser.lastLoginTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="注册时间">
              {dayjs(selectedUser.createTime).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Drawer>

      {/* 标签编辑模态框 */}
      <Modal
        title="编辑用户标签"
        open={tagModalVisible}
        onOk={handleSaveTags}
        onCancel={() => setTagModalVisible(false)}
        confirmLoading={updateTagsLoading}
      >
        <Form form={tagForm} layout="vertical">
          <Form.Item
            name="tags"
            label="用户标签"
            help="输入标签后按回车添加"
          >
            <Select
              mode="tags"
              style={{ width: '100%' }}
              placeholder="请输入标签"
              tokenSeparators={[',']}
            />
          </Form.Item>
          
          <Form.Item name="reason" label="操作原因">
            <Input.TextArea placeholder="请输入操作原因" rows={3} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 邀请关系抽屉 */}
      <Drawer
        title={selectedUser ? `${selectedUser.username} 的邀请关系` : '邀请关系'}
        placement="right"
        width={1000}
        open={invitationVisible}
        onClose={() => setInvitationVisible(false)}
        destroyOnClose
      >
        {selectedUser && (
          <InvitationTree
            userId={selectedUser.id}
            loading={invitationLoading}
          />
        )}
      </Drawer>

      {/* 余额详情弹窗 */}
      {selectedUser && (
        <BalanceDetailModal
          visible={balanceDetailVisible}
          onCancel={() => setBalanceDetailVisible(false)}
          userId={selectedUser.id}
          userName={selectedUser.username}
        />
      )}
    </div>
  );
};

export default UserManagement;
