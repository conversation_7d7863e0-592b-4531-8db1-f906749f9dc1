/**
 * 测试模块路径解析
 */
export function testModulePaths() {
  const pageModules = import.meta.glob([
    "/src/pages/**/*.tsx",
    "!/src/pages/exception/**/*.tsx",
  ]);
  
  const pageModulePaths = Object.keys(pageModules);
  
  console.log('🔍 [TEST] 所有页面模块路径:');
  pageModulePaths.forEach(path => {
    console.log(`  ${path}`);
  });
  
  console.log('\n🔍 [TEST] 配置相关模块:');
  const configPaths = pageModulePaths.filter(path => path.includes('config'));
  configPaths.forEach(path => {
    console.log(`  ${path}`);
  });
  
  console.log('\n🔍 [TEST] VIP相关模块:');
  const vipPaths = pageModulePaths.filter(path => path.includes('vip'));
  vipPaths.forEach(path => {
    console.log(`  ${path}`);
  });
  
  // 测试特定路径
  const testPath = '/src/pages/config/vip-v2/index.tsx';
  const exists = pageModulePaths.includes(testPath);
  console.log(`\n🎯 [TEST] 测试路径 ${testPath}: ${exists ? '存在' : '不存在'}`);
  
  // 测试不同的路径变体
  const variants = [
    '/src/pages/config/vip-v2/index.tsx',
    '/src/pages/config/vip-v2.tsx',
    '/src/pages/config/vip-v2/index.ts',
    '/src/pages/config/vip/index.tsx'
  ];
  
  console.log('\n🔍 [TEST] 路径变体测试:');
  variants.forEach(variant => {
    const exists = pageModulePaths.includes(variant);
    console.log(`  ${variant}: ${exists ? '✅' : '❌'}`);
  });
  
  return {
    allPaths: pageModulePaths,
    configPaths,
    vipPaths,
    testPathExists: exists
  };
}
