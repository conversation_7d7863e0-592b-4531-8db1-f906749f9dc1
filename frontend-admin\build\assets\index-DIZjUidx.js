import{j as e}from"./index-DD0gcXtR.js";import{a as h}from"./react-BUTTOX-3.js";import{u,B as ie}from"./BalanceDetailModal-R0ABNlM9.js";import{av as l,aq as B,I as g,p as M,aF as re,Q as I,d as r,f as oe,ak as de,az as ce,al as f,z as V,ar as s,an as m,M as U,n as x,aB as he,aG as me,aH as xe,aI as ue,aJ as ge,J as je,aK as pe,s as S}from"./antd-FyCAPQKa.js";import{u as j}from"./index-BpqjFj86.js";import Ie from"./InvitationTree-bLIznmsz.js";const{RangePicker:fe}=re,{Option:T}=M,o={NORMAL:0,BANNED:1,DEACTIVATED:2},be={INTERNAL_EMPLOYEE:1},Re=()=>{var A;const[R]=l.useForm(),[b]=l.useForm(),[d,D]=h.useState({page:1,pageSize:20}),[n,p]=h.useState(null),[F,Y]=h.useState(!1),[$,w]=h.useState(!1),[O,k]=h.useState(!1),[P,y]=h.useState(!1),{data:c,loading:C,run:E}=u(()=>(console.log("🔍 前端发起用户列表请求，参数:",d),j.getUserList(d).then(t=>(console.log("📊 前端收到用户列表响应:",t),t))),{refreshDeps:[d]}),{run:H,loading:q}=u(t=>j.getUserDetail(t),{manual:!0,onSuccess:t=>{p(t),Y(!0)}}),{run:J}=u((t,a)=>j.updateUserStatus(t,a),{manual:!0,onSuccess:()=>{S.success("用户状态更新成功"),E()}}),{run:_,loading:K}=u((t,a)=>j.updateUserTags(t,a),{manual:!0,onSuccess:()=>{S.success("用户标签更新成功"),w(!1),E()}}),{run:Q,loading:W}=u(t=>(console.log("🔍 前端发起邀请关系请求，用户ID:",t),j.getInvitationRelationship(t).then(a=>(console.log("📊 前端收到邀请关系响应:",a),a)).catch(a=>{throw console.error("❌ 邀请关系请求失败:",a),a})),{manual:!0,onSuccess:t=>{console.log("✅ 邀请关系请求成功:",t),k(!0)},onError:t=>{console.error("❌ 邀请关系请求错误:",t),S.error("获取邀请关系失败")}}),G=t=>{const a={page:1,pageSize:d.pageSize,...t};t.dateRange&&t.dateRange.length===2&&(a.startDate=t.dateRange[0].format("YYYY-MM-DD"),a.endDate=t.dateRange[1].format("YYYY-MM-DD")),delete a.dateRange,D(a)},X=()=>{R.resetFields(),D({page:1,pageSize:20})},Z=t=>{D({...d,page:t.current,pageSize:t.pageSize})},L=(t,a)=>{const i=a===o.BANNED?"封禁":"解封";U.confirm({title:`确认${i}用户`,content:`确定要${i}用户 "${t.username}" 吗？`,icon:e.jsx(pe,{}),onOk:()=>{J(t.id,{status:a})}})},ee=t=>{p(t),b.setFieldsValue({tags:t.tags||[]}),w(!0)},te=()=>{b.validateFields().then(t=>{n&&_(n.id,t)})},ae=t=>{p(t),Q(t.id)},N=t=>{p(t),y(!0)},v=t=>{const i={[o.NORMAL]:{color:"green",text:"正常"},[o.BANNED]:{color:"red",text:"已封禁"},[o.DEACTIVATED]:{color:"gray",text:"已注销"}}[t]||{color:"default",text:"未知"};return e.jsx(je,{status:i.color,text:i.text})},ne=[{title:"UID",dataIndex:"uid",width:100,fixed:"left"},{title:"用户名",dataIndex:"username",width:120,fixed:"left",render:(t,a)=>{const se=a.daysNotLoggedIn>3?"#ff4d4f":"#1890ff",le=a.tags&&a.tags.length>0?e.jsxs("div",{style:{maxWidth:200},children:[e.jsx("div",{style:{marginBottom:8,fontWeight:"bold"},children:"用户标签"}),e.jsx(I,{wrap:!0,children:a.tags.map(z=>e.jsx(m,{color:"blue",children:z},z))})]}):e.jsx("div",{children:"暂无标签"});return e.jsx(x,{title:le,placement:"right",children:e.jsx("span",{style:{color:se,cursor:"pointer"},children:t})})}},{title:"邮箱",dataIndex:"email",width:180},{title:"手机号",dataIndex:"phone",width:120},{title:"状态",dataIndex:"status",width:100,render:v},{title:"VIP",dataIndex:"vipLevel",width:80,render:t=>e.jsxs(m,{color:"gold",children:["V",t]})},{title:"充值余额",dataIndex:"rechargeBalance",width:120,render:(t,a)=>{const i=Number(t)||0;return e.jsx(r,{type:"link",size:"small",onClick:()=>N(a),style:{padding:0,height:"auto"},children:i.toFixed(4)})}},{title:"金币余额",dataIndex:"goldBalance",width:120,render:(t,a)=>{const i=Number(t)||0;return e.jsx(r,{type:"link",size:"small",onClick:()=>N(a),style:{padding:0,height:"auto"},children:i.toLocaleString()})}},{title:"可提现余额",dataIndex:"withdrawableBalance",width:120,render:(t,a)=>{const i=Number(t)||0;return e.jsx(r,{type:"link",size:"small",onClick:()=>N(a),style:{padding:0,height:"auto"},children:i.toFixed(4)})}},{title:"渠道",dataIndex:"channelName",width:120},{title:"广告标识",dataIndex:"adIdentifier",width:120,render:t=>t||"-"},{title:"最后登录",dataIndex:"lastLoginTime",width:160,render:t=>t?f(t).format("YYYY-MM-DD HH:mm"):"从未登录"},{title:"未登天数",dataIndex:"daysNotLoggedIn",width:100,render:t=>t===0?"今天":t===1?"1天":t<=7?e.jsxs(m,{color:"orange",children:[t,"天"]}):t<=30?e.jsxs(m,{color:"red",children:[t,"天"]}):e.jsxs(m,{color:"volcano",children:[t,"天"]})},{title:"注册时间",dataIndex:"createTime",width:160,render:t=>f(t).format("YYYY-MM-DD HH:mm")},{title:"操作",key:"action",width:200,fixed:"right",render:(t,a)=>e.jsxs(I,{size:"small",children:[e.jsx(x,{title:"查看详情",children:e.jsx(r,{type:"link",size:"small",icon:e.jsx(he,{}),onClick:()=>H(a.id)})}),a.status===o.NORMAL?e.jsx(x,{title:"封禁用户",children:e.jsx(r,{type:"link",size:"small",danger:!0,icon:e.jsx(me,{}),onClick:()=>L(a,o.BANNED)})}):e.jsx(x,{title:"解封用户",children:e.jsx(r,{type:"link",size:"small",icon:e.jsx(xe,{}),onClick:()=>L(a,o.NORMAL)})}),e.jsx(x,{title:"编辑标签",children:e.jsx(r,{type:"link",size:"small",icon:e.jsx(ue,{}),onClick:()=>ee(a)})}),e.jsx(x,{title:"邀请关系",children:e.jsx(r,{type:"link",size:"small",icon:e.jsx(ge,{}),onClick:()=>ae(a)})})]})}];return e.jsxs("div",{className:"p-6",children:[e.jsx(B,{title:"APP用户列表",className:"mb-4",children:e.jsxs(l,{form:R,layout:"inline",onFinish:G,className:"mb-4",children:[e.jsx(l.Item,{name:"id",children:e.jsx(g,{placeholder:"用户ID",type:"number"})}),e.jsx(l.Item,{name:"username",children:e.jsx(g,{placeholder:"用户名"})}),e.jsx(l.Item,{name:"email",children:e.jsx(g,{placeholder:"邮箱"})}),e.jsx(l.Item,{name:"phone",children:e.jsx(g,{placeholder:"手机号"})}),e.jsx(l.Item,{name:"status",children:e.jsxs(M,{placeholder:"用户状态",allowClear:!0,style:{width:120},children:[e.jsx(T,{value:o.NORMAL,children:"正常"}),e.jsx(T,{value:o.BANNED,children:"已封禁"}),e.jsx(T,{value:o.DEACTIVATED,children:"已注销"})]})}),e.jsx(l.Item,{name:"dateRange",children:e.jsx(fe,{placeholder:["开始日期","结束日期"]})}),e.jsx(l.Item,{children:e.jsxs(I,{children:[e.jsx(r,{type:"primary",htmlType:"submit",icon:e.jsx(oe,{}),loading:C,children:"搜索"}),e.jsx(r,{icon:e.jsx(de,{}),onClick:X,children:"重置"})]})})]})}),e.jsx(B,{children:e.jsx(ce,{columns:ne,dataSource:(c==null?void 0:c.list)||[],rowKey:"id",loading:C,scroll:{x:2100},pagination:{current:d.page,pageSize:d.pageSize,total:(c==null?void 0:c.total)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:t=>`共 ${t} 条记录`},onChange:Z})}),e.jsx(V,{title:"用户详情",placement:"right",width:600,open:F,onClose:()=>Y(!1),loading:q,children:n&&e.jsxs(s,{column:1,bordered:!0,children:[e.jsx(s.Item,{label:"UID",children:n.uid}),e.jsx(s.Item,{label:"用户名",children:n.username}),e.jsx(s.Item,{label:"邮箱",children:n.email}),e.jsx(s.Item,{label:"手机号",children:n.phone}),e.jsx(s.Item,{label:"状态",children:v(n.status)}),e.jsx(s.Item,{label:"账户类型",children:n.accountType===be.INTERNAL_EMPLOYEE?"内部员工":"普通用户"}),e.jsxs(s.Item,{label:"VIP等级",children:["V",n.vipLevel]}),e.jsx(s.Item,{label:"充值余额",children:(Number(n.rechargeBalance)||0).toFixed(4)}),e.jsx(s.Item,{label:"金币余额",children:(Number(n.goldBalance)||0).toLocaleString()}),e.jsx(s.Item,{label:"可提现余额",children:(Number(n.withdrawableBalance)||0).toFixed(4)}),e.jsx(s.Item,{label:"渠道",children:n.channelName||"-"}),e.jsx(s.Item,{label:"广告",children:n.adName||"-"}),e.jsx(s.Item,{label:"获取标签",children:n.acquisitionTag||"-"}),e.jsx(s.Item,{label:"用户标签",children:e.jsx(I,{wrap:!0,children:(A=n.tags)==null?void 0:A.map(t=>e.jsx(m,{children:t},t))})}),e.jsx(s.Item,{label:"注册IP",children:n.registerIp}),e.jsx(s.Item,{label:"最后登录时间",children:n.lastLoginTime?f(n.lastLoginTime).format("YYYY-MM-DD HH:mm:ss"):"-"}),e.jsx(s.Item,{label:"注册时间",children:f(n.createTime).format("YYYY-MM-DD HH:mm:ss")})]})}),e.jsx(U,{title:"编辑用户标签",open:$,onOk:te,onCancel:()=>w(!1),confirmLoading:K,children:e.jsxs(l,{form:b,layout:"vertical",children:[e.jsx(l.Item,{name:"tags",label:"用户标签",help:"输入标签后按回车添加",children:e.jsx(M,{mode:"tags",style:{width:"100%"},placeholder:"请输入标签",tokenSeparators:[","]})}),e.jsx(l.Item,{name:"reason",label:"操作原因",children:e.jsx(g.TextArea,{placeholder:"请输入操作原因",rows:3})})]})}),e.jsx(V,{title:n?`${n.username} 的邀请关系`:"邀请关系",placement:"right",width:1e3,open:O,onClose:()=>k(!1),destroyOnClose:!0,children:n&&e.jsx(Ie,{userId:n.id,loading:W})}),n&&e.jsx(ie,{visible:P,onCancel:()=>y(!1),userId:n.id,userName:n.username})]})};export{Re as default};
