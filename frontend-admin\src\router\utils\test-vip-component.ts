/**
 * 测试VIP组件加载
 */
export async function testVipComponentLoading() {
  console.log('🧪 [TEST_VIP] 开始测试VIP组件加载');
  
  try {
    // 测试直接导入VIP组件
    const vipModule = await import('/src/pages/config/vip-v2/index.tsx');
    console.log('✅ [TEST_VIP] VIP组件直接导入成功:', vipModule);
    
    // 测试组件是否有默认导出
    if (vipModule.default) {
      console.log('✅ [TEST_VIP] VIP组件有默认导出');
    } else {
      console.log('❌ [TEST_VIP] VIP组件没有默认导出');
    }
    
    return true;
  } catch (error) {
    console.error('❌ [TEST_VIP] VIP组件导入失败:', error);
    return false;
  }
}

/**
 * 测试模块路径解析
 */
export function testModulePathResolution() {
  console.log('🧪 [TEST_PATH] 开始测试模块路径解析');
  
  const pageModules = import.meta.glob([
    "/src/pages/**/*.tsx",
    "!/src/pages/exception/**/*.tsx",
  ]);
  
  const pageModulePaths = Object.keys(pageModules);
  
  // 测试VIP相关路径
  const vipPaths = [
    '/src/pages/config/vip-v2/index.tsx',
    '/src/pages/config/vip/index.tsx',
    '/src/pages/config/vip-v2.tsx'
  ];
  
  console.log('🔍 [TEST_PATH] 测试VIP路径:');
  vipPaths.forEach(path => {
    const exists = pageModulePaths.includes(path);
    console.log(`  ${path}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
  });
  
  // 显示所有config相关路径
  const configPaths = pageModulePaths.filter(path => path.includes('config'));
  console.log('🔍 [TEST_PATH] 所有config路径:');
  configPaths.forEach(path => {
    console.log(`  ${path}`);
  });
  
  return {
    allPaths: pageModulePaths,
    vipPaths: vipPaths.filter(path => pageModulePaths.includes(path)),
    configPaths
  };
}
