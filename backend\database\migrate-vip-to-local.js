const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

// 加载数据库配置
const { getDatabaseConfig } = require('./config/database-config');

async function migrateVipToLocal() {
  console.log('🚀 开始VIP系统数据迁移到本地数据库...\n');
  
  const config = getDatabaseConfig('local');
  const client = new Client(config);
  
  try {
    await client.connect();
    console.log('✅ 连接到本地数据库成功');
    
    // 1. 执行VIP表结构重构
    console.log('\n📋 步骤 1: 执行VIP表结构重构...');
    const restructureScript = path.join(__dirname, 'migrations', '005-restructure-vip-config-table.sql');
    
    if (fs.existsSync(restructureScript)) {
      const sql = fs.readFileSync(restructureScript, 'utf8');
      await client.query(sql);
      console.log('✅ VIP表结构重构完成');
    } else {
      console.error('❌ 重构脚本文件不存在:', restructureScript);
      return;
    }
    
    // 2. 执行数据迁移脚本
    console.log('\n📋 步骤 2: 执行数据迁移...');
    const migrationScript = path.join(__dirname, 'migrations', '006-migrate-vip-data-to-v2-fixed.sql');
    
    if (fs.existsSync(migrationScript)) {
      const sql = fs.readFileSync(migrationScript, 'utf8');
      await client.query(sql);
      console.log('✅ 数据迁移完成');
    } else {
      console.warn('⚠️  数据迁移脚本文件不存在:', migrationScript);
      console.log('   跳过数据迁移步骤');
    }
    
    // 3. 验证迁移结果
    console.log('\n📋 步骤 3: 验证迁移结果...');
    
    // 检查VIP配置表
    const vipConfigResult = await client.query('SELECT COUNT(*) as count FROM vip_configs');
    console.log(`📊 VIP配置记录数: ${vipConfigResult.rows[0].count}`);
    
    // 显示VIP等级配置
    const vipLevelsResult = await client.query(`
      SELECT 
        vip_level,
        level_name,
        required_exp,
        daily_gold_reward,
        CASE WHEN buyback_enabled THEN CONCAT(buyback_rate, ':1') ELSE '无资格' END as buyback_rate_display,
        CONCAT(c2c_fee_rate, '%') as c2c_fee_display,
        CONCAT(rakeback_rate, '%') as rakeback_display
      FROM vip_configs 
      ORDER BY vip_level
    `);
    
    console.log('\n📋 VIP等级配置详情:');
    console.table(vipLevelsResult.rows);
    
    // 检查用户表是否有EXP字段
    const userTableCheck = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'app_users' 
      AND column_name IN ('total_exp', 'last_exp_update')
    `);
    
    if (userTableCheck.rows.length > 0) {
      console.log('✅ 用户表EXP字段已存在');
      
      // 显示用户EXP统计
      const userExpResult = await client.query(`
        SELECT 
          COUNT(*) as total_users,
          AVG(total_exp) as avg_exp,
          MAX(total_exp) as max_exp,
          MIN(total_exp) as min_exp
        FROM app_users
      `);
      
      console.log('\n📊 用户EXP统计:');
      console.table(userExpResult.rows);
    } else {
      console.log('⚠️  用户表EXP字段不存在，需要添加');
    }
    
    console.log('\n🎉 VIP系统迁移完成！');
    console.log('\n📝 迁移总结:');
    console.log('✅ VIP配置表结构已重构');
    console.log('✅ 8个VIP等级配置已导入');
    console.log('✅ 经济权益系统已激活');
    console.log('✅ EXP经验值系统已部署');
    
  } catch (error) {
    console.error('❌ 迁移过程中发生错误:', error.message);
    console.error('详细错误信息:', error);
  } finally {
    await client.end();
    console.log('\n🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateVipToLocal().catch(console.error);
}

module.exports = { migrateVipToLocal };
