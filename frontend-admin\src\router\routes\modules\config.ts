import type { AppRouteRecordRaw } from "#src/router/types";
import { ContainerLayout } from "#src/layout";
import { config } from "#src/router/extra-info";

import { lazy } from "react";

const VipConfigV2 = lazy(() => import("#src/pages/config/vip-v2"));
const MembershipCardConfig = lazy(() => import("#src/pages/config/membership-cards"));
const RechargeConfig = lazy(() => import("#src/pages/config/recharge"));
const AdConfig = lazy(() => import("#src/pages/config/ads"));
const AppHomeConfig = lazy(() => import("#src/pages/config/app-home"));

const routes: AppRouteRecordRaw[] = [
  {
    path: "/config",
    Component: ContainerLayout,
    handle: {
      icon: "SettingOutlined",
      title: "配置管理",
      order: config,
      roles: ["admin", "super_admin"],
    },
    children: [
      {
        path: "vip",
        Component: VipConfigV2,
        handle: {
          icon: "CrownOutlined",
          title: "VIP经济权益配置",
          roles: ["admin", "super_admin"],
          permissions: [
            "config:vip:query",
            "config:vip:edit",
            "config:vip:calculate",
            "config:vip:recalculate",
          ],
        },
      },
      {
        path: "membership-cards",
        Component: MembershipCardConfig,
        handle: {
          icon: "CreditCardOutlined",
          title: "会员月卡配置",
          roles: ["admin", "super_admin"],
          permissions: [
            "config:membership:query",
            "config:membership:edit",
            "config:membership:effective",
          ],
        },
      },
      {
        path: "recharge",
        Component: RechargeConfig,
        handle: {
          icon: "DollarOutlined",
          title: "充值配置",
          roles: ["admin", "super_admin"],
          permissions: [
            "config:recharge:query",
            "config:recharge:add",
            "config:recharge:edit",
            "config:recharge:delete",
            "config:recharge:effective",
            "config:recharge:limits",
          ],
        },
      },
      {
        path: "ads",
        Component: AdConfig,
        handle: {
          icon: "PictureOutlined",
          title: "广告配置",
          roles: ["admin", "super_admin"],
          permissions: [
            "config:ads:query",
            "config:ads:add",
            "config:ads:edit",
            "config:ads:delete",
            "config:ads:toggle",
            "config:ads:sort",
          ],
        },
      },
      {
        path: "app-home",
        Component: AppHomeConfig,
        handle: {
          icon: "HomeOutlined",
          title: "APP首页配置",
          roles: ["admin", "super_admin"],
          permissions: [
            "config:app-home:query",
            "config:app-home:add",
            "config:app-home:edit",
            "config:app-home:delete",
            "config:app-home:toggle",
          ],
        },
      },
    ],
  },
];

export default routes;
