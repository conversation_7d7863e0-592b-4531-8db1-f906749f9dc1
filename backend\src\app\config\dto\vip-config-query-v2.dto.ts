import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsN<PERSON>ber, IsString, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';

export class VipConfigQueryDto {
  @ApiProperty({ description: '页码', example: 1, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number = 1;

  @ApiProperty({ description: '每页数量', example: 10, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  pageSize?: number = 10;

  @ApiProperty({ description: 'VIP等级筛选', example: 1, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: 'VIP等级必须是数字' })
  @Min(0, { message: 'VIP等级不能小于0' })
  @Max(99, { message: 'VIP等级不能大于99' })
  vipLevel?: number;

  @ApiProperty({ description: '状态筛选：1-启用，0-禁用', example: 1, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: '状态必须是数字' })
  @Min(0, { message: '状态值必须是0或1' })
  @Max(1, { message: '状态值必须是0或1' })
  status?: number;

  @ApiProperty({ description: '等级名称搜索', example: '活跃', required: false })
  @IsOptional()
  @IsString({ message: '等级名称必须是字符串' })
  levelName?: string;

  @ApiProperty({ description: '是否开启回购筛选', example: true, required: false })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  buybackEnabled?: boolean;
}

export class VipConfigListDto {
  @ApiProperty({ description: 'VIP配置ID' })
  id: number;

  @ApiProperty({ description: 'VIP等级' })
  vipLevel: number;

  @ApiProperty({ description: '等级名称' })
  levelName: string;

  @ApiProperty({ description: '核心战略定位' })
  strategicPosition: string;

  @ApiProperty({ description: '所需EXP经验值' })
  requiredExp: number;

  @ApiProperty({ description: '真金充值EXP比例' })
  realMoneyRechargeExpRatio: number;

  @ApiProperty({ description: '真金流水EXP比例' })
  realMoneyFlowExpRatio: number;

  @ApiProperty({ description: '金币付费EXP比例' })
  goldPaymentExpRatio: number;

  @ApiProperty({ description: '金币流水EXP比例' })
  goldFlowExpRatio: number;

  @ApiProperty({ description: '活跃任务最小EXP' })
  dailyTaskExpMin: number;

  @ApiProperty({ description: '活跃任务最大EXP' })
  dailyTaskExpMax: number;

  @ApiProperty({ description: '每日可领取金币数量' })
  dailyGoldReward: number;

  @ApiProperty({ description: '是否开启官方回购' })
  buybackEnabled: boolean;

  @ApiProperty({ description: '官方回购价格' })
  buybackRate: number;

  @ApiProperty({ description: 'C2C手续费率' })
  c2cFeeRate: number;

  @ApiProperty({ description: '周返水比例' })
  rakebackRate: number;

  @ApiProperty({ description: '提现手续费率' })
  withdrawalFeeRate: number;

  @ApiProperty({ description: '每日提现额度上限' })
  dailyWithdrawalLimit: number;

  @ApiProperty({ description: '提现优先级' })
  withdrawalPriority: number;

  @ApiProperty({ description: '任务是否需要解锁' })
  taskUnlockRequired: boolean;

  @ApiProperty({ description: '解锁所需游戏局数' })
  taskUnlockGamesRequired: number;

  @ApiProperty({ description: '状态：1-启用，0-禁用' })
  status: number;

  @ApiProperty({ description: '备注说明' })
  remark: string;

  @ApiProperty({ description: '创建人ID' })
  createdBy: number;

  @ApiProperty({ description: '更新人ID' })
  updatedBy: number;

  @ApiProperty({ description: '创建时间' })
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  updateTime: Date;

  @ApiProperty({ description: '创建人信息', required: false })
  creator?: {
    id: number;
    username: string;
  };

  @ApiProperty({ description: '更新人信息', required: false })
  updater?: {
    id: number;
    username: string;
  };
}

export class VipExpAddDto {
  @ApiProperty({ description: 'EXP类型', enum: ['real_money_recharge', 'real_money_flow', 'gold_payment', 'gold_flow', 'daily_task'] })
  @IsString({ message: 'EXP类型必须是字符串' })
  expType: 'real_money_recharge' | 'real_money_flow' | 'gold_payment' | 'gold_flow' | 'daily_task';

  @ApiProperty({ description: '金额或数量', example: 100 })
  @IsNumber({}, { message: '金额必须是数字' })
  @Min(0, { message: '金额不能小于0' })
  amount: number;

  @ApiProperty({ description: '描述', example: '充值获得EXP', required: false })
  @IsOptional()
  @IsString({ message: '描述必须是字符串' })
  description?: string;
}
