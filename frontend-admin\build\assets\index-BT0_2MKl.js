import{j as a}from"./index-DD0gcXtR.js";import{h as U,a as r}from"./react-BUTTOX-3.js";import{a as Y,t as O,d as L}from"./index-FIiDB76q.js";import{g as q}from"./index-CmJsfaTQ.js";import{u as F,D as t,g as c}from"./useDictionary-lymihlgV.js";import{aq as $,ao as N,ap as o,I as V,p,Q as C,d as A,u as H,ak as J,az as K,s as h,aA as W,aB as X,an as d,n as E,aC as Z,aD as ee,aE as ae}from"./antd-FyCAPQKa.js";import"./index-Cb-TAT0P.js";const{Search:se}=V,{Option:x}=p;function oe(){const w=U(),[v,T]=r.useState(!1),[P,f]=r.useState([]),[I,k]=r.useState(0),[D,b]=r.useState([]),[n,g]=r.useState({page:1,pageSize:10,sortBy:"createdAt",sortOrder:"DESC"}),{dictionaries:i,loading:S}=F([t.APP_STATUS,t.GAME_STATUS,t.GAME_CATEGORY,t.GAME_TYPE,t.APP_TAG,t.GAME_FEATURE,t.DEVICE_SUPPORT,t.APP_VOLATILITY]),u=async()=>{T(!0);try{const e=await Y(n);e.code===200&&(f(e.result.list),k(e.result.total))}catch{h.error("获取数据失败")}finally{T(!1)}},R=async()=>{try{const e=await q();e.code===200&&b(e.result)}catch(e){console.error("获取供应商列表失败:",e)}};r.useEffect(()=>{u()},[n]),r.useEffect(()=>{R()},[]);const G=e=>{g(s=>({...s,search:e,page:1}))},j=(e,s)=>{g(l=>({...l,[e]:s,page:1}))},B=()=>{g({page:1,pageSize:10,sortBy:"createdAt",sortOrder:"DESC"})},M=async e=>{try{(await L(e)).code===200&&(h.success("删除成功"),u())}catch{h.error("删除失败")}},Q=async(e,s)=>{const l=s==="active"?"inactive":"active";try{(await O(e,l)).code===200&&(h.success("状态切换成功"),u())}catch{h.error("状态切换失败")}},_=[{title:"图标",dataIndex:"iconUrl",key:"iconUrl",width:80,render:(e,s)=>e?a.jsx(W,{src:e,alt:s.name,width:40,height:40,style:{borderRadius:4},fallback:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"}):a.jsx("div",{style:{width:40,height:40,backgroundColor:"#f5f5f5",borderRadius:4,display:"flex",alignItems:"center",justifyContent:"center"},children:a.jsx(X,{style:{color:"#ccc"}})})},{title:"应用名称",dataIndex:"name",key:"name",width:150,render:(e,s)=>a.jsxs("div",{children:[a.jsx("div",{style:{fontWeight:500},children:e}),a.jsx("div",{style:{fontSize:12,color:"#666"},children:s.appCode})]})},{title:"供应商",dataIndex:"provider",key:"provider",width:120,render:e=>(e==null?void 0:e.name)||"-"},{title:"分类",key:"categories",width:180,render:(e,s)=>{const l=s.categories||[],m=[...i[t.GAME_CATEGORY]||[],...i[t.GAME_TYPE]||[]];return a.jsxs("div",{children:[l.slice(0,2).map(y=>{const z=c(m,y);return a.jsx(d,{style:{marginBottom:2,fontSize:"12px",padding:"0 4px"},children:z},y)}),l.length>2&&a.jsx(E,{title:l.slice(2).map(y=>c(m,y)).join(", "),children:a.jsxs(d,{style:{fontSize:"12px",padding:"0 4px"},children:["+",l.length-2]})})]})}},{title:"标签",dataIndex:"tags",key:"tags",width:200,render:e=>a.jsxs("div",{children:[e==null?void 0:e.slice(0,3).map(s=>{const l=c(i[t.APP_TAG]||[],s);return a.jsx(d,{style:{marginBottom:2,fontSize:"12px",padding:"0 4px"},children:l},s)}),(e==null?void 0:e.length)>3&&a.jsx(E,{title:e.slice(3).map(s=>c(i[t.APP_TAG]||[],s)).join(", "),children:a.jsxs(d,{style:{fontSize:"12px",padding:"0 4px"},children:["+",e.length-3]})})]})},{title:"平台",dataIndex:"platforms",key:"platforms",width:120,render:e=>a.jsxs("div",{children:[e==null?void 0:e.slice(0,2).map(s=>{const l=c(i[t.DEVICE_SUPPORT]||[],s);return a.jsx(d,{style:{marginBottom:2,fontSize:"12px",padding:"0 4px"},children:l},s)}),(e==null?void 0:e.length)>2&&a.jsx(E,{title:e.slice(2).map(s=>c(i[t.DEVICE_SUPPORT]||[],s)).join(", "),children:a.jsxs(d,{style:{fontSize:"12px",padding:"0 4px"},children:["+",e.length-2]})})]})},{title:"RTP",dataIndex:"rtp",key:"rtp",width:80,render:e=>e?`${e}%`:"-"},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>{const s=c(i[t.APP_STATUS]||[],e),l=e==="active"?"green":e==="inactive"?"red":"orange";return a.jsx(d,{color:l,children:s})}},{title:"操作",key:"action",width:180,fixed:"right",render:(e,s)=>a.jsxs(C,{size:"small",children:[a.jsx(A,{type:"link",size:"small",icon:a.jsx(Z,{}),onClick:()=>w(`/app/application/${s.id}/edit`),children:"编辑"}),a.jsx(A,{type:"link",size:"small",onClick:()=>Q(s.id,s.status),children:s.status==="active"?"禁用":"启用"}),a.jsx(ee,{title:"确定要删除这个应用吗？",onConfirm:()=>M(s.id),okText:"确定",cancelText:"取消",children:a.jsx(A,{type:"link",size:"small",danger:!0,icon:a.jsx(ae,{}),children:"删除"})})]})}];return a.jsx("div",{className:"p-6",children:a.jsxs($,{children:[a.jsx("div",{className:"mb-4",children:a.jsxs(N,{gutter:[16,16],children:[a.jsx(o,{span:6,children:a.jsx(se,{placeholder:"搜索应用名称或代码",allowClear:!0,onSearch:G,style:{width:"100%"}})}),a.jsx(o,{span:4,children:a.jsx(p,{placeholder:"供应商",allowClear:!0,style:{width:"100%"},onChange:e=>j("providerId",e),value:n.providerId,children:D.map(e=>a.jsx(x,{value:e.id,children:e.name},e.id))})}),a.jsx(o,{span:4,children:a.jsxs(p,{mode:"multiple",placeholder:"游戏分类",allowClear:!0,style:{width:"100%"},onChange:e=>j("categories",e),value:n.categories,loading:S,maxTagCount:1,maxTagPlaceholder:e=>`+${e.length}个分类`,children:[(i[t.GAME_CATEGORY]||[]).map(e=>a.jsx(x,{value:e.value,disabled:e.disabled,children:e.label},e.value)),(i[t.GAME_TYPE]||[]).map(e=>a.jsx(x,{value:e.value,disabled:e.disabled,children:e.label},e.value))]})}),a.jsx(o,{span:4,children:a.jsx(p,{placeholder:"状态",allowClear:!0,style:{width:"100%"},onChange:e=>j("status",e),value:n.status,loading:S,children:(i[t.APP_STATUS]||[]).map(e=>a.jsx(x,{value:e.value,disabled:e.disabled,children:e.label},e.value))})}),a.jsx(o,{span:4,children:a.jsx(p,{mode:"multiple",placeholder:"游戏特性",allowClear:!0,style:{width:"100%"},onChange:e=>j("features",e),value:n.features,loading:S,maxTagCount:1,maxTagPlaceholder:e=>`+${e.length}个特性`,children:(i[t.GAME_FEATURE]||[]).map(e=>a.jsx(x,{value:e.value,disabled:e.disabled,children:e.label},e.value))})}),a.jsx(o,{span:4,children:a.jsxs(C,{children:[a.jsx(A,{type:"primary",icon:a.jsx(H,{}),onClick:()=>w("/app/application/create"),children:"新增应用"}),a.jsx(A,{icon:a.jsx(J,{}),onClick:u,children:"刷新"}),a.jsx(A,{onClick:B,children:"重置"})]})})]})}),a.jsx(K,{columns:_,dataSource:P,rowKey:"id",loading:v,scroll:{x:1400},pagination:{current:n.page,pageSize:n.pageSize,total:I,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>`第 ${s[0]}-${s[1]} 条/共 ${e} 条`,onChange:(e,s)=>{g(l=>({...l,page:e,pageSize:s}))}}})]})})}export{oe as default};
