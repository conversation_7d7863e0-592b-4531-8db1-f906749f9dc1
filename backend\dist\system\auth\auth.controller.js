"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemAuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const local_auth_guard_1 = require("./guards/local-auth.guard");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const login_dto_1 = require("./dto/login.dto");
const refresh_token_dto_1 = require("./dto/refresh-token.dto");
const change_password_dto_1 = require("./dto/change-password.dto");
let SystemAuthController = class SystemAuthController {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    async login(req, loginDto) {
        const startTime = Date.now();
        console.log(`[AUTH_CONTROLLER] 收到登录请求 - 用户名: ${loginDto.username}`);
        try {
            const result = await this.authService.login(req.user);
            const endTime = Date.now();
            console.log(`[AUTH_CONTROLLER] 登录成功 - 用户名: ${loginDto.username}, 耗时: ${endTime - startTime}ms`);
            return {
                code: 200,
                message: '登录成功',
                result,
            };
        }
        catch (error) {
            const endTime = Date.now();
            console.error(`[AUTH_CONTROLLER] 登录失败 - 用户名: ${loginDto.username}, 耗时: ${endTime - startTime}ms, 错误:`, error);
            throw error;
        }
    }
    async logout() {
        return {
            code: 200,
            message: '登出成功',
            result: {},
        };
    }
    async refreshToken(refreshTokenDto) {
        const result = await this.authService.refreshToken(refreshTokenDto.refreshToken);
        return {
            code: 200,
            message: '刷新成功',
            result,
        };
    }
    async getProfile(req) {
        const result = await this.authService.getUserInfo(req.user.userId);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async updateProfile(req, updateData) {
        const startTime = Date.now();
        console.log(`[AUTH] 开始更新用户信息 - 用户ID: ${req.user.userId}, 数据:`, updateData);
        try {
            const result = await this.authService.updateUserInfo(req.user.userId, updateData);
            const endTime = Date.now();
            console.log(`[AUTH] 更新用户信息成功 - 耗时: ${endTime - startTime}ms`);
            return {
                code: 200,
                message: '更新成功',
                result,
            };
        }
        catch (error) {
            const endTime = Date.now();
            console.error(`[AUTH] 更新用户信息失败 - 耗时: ${endTime - startTime}ms, 错误:`, error);
            throw error;
        }
    }
    async changePassword(req, changePasswordDto) {
        const { currentPassword, newPassword, confirmPassword } = changePasswordDto;
        if (newPassword !== confirmPassword) {
            return {
                code: 400,
                message: '新密码和确认密码不一致',
            };
        }
        try {
            const result = await this.authService.changePassword(req.user.userId, currentPassword, newPassword);
            return {
                code: 200,
                message: '密码修改成功',
                result,
            };
        }
        catch (error) {
            console.error(`[AUTH] 修改密码失败 - 用户ID: ${req.user.userId}, 错误:`, error);
            throw error;
        }
    }
    async resetAdminPassword(body) {
        console.log(`[AUTH_CONTROLLER] 重置admin密码请求`);
        const result = await this.authService.resetAdminPassword(body.newPassword);
        return {
            code: 200,
            message: result.message,
        };
    }
};
exports.SystemAuthController = SystemAuthController;
__decorate([
    (0, common_1.UseGuards)(local_auth_guard_1.SystemLocalAuthGuard),
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '管理员登录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '登录成功' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '用户名或密码错误' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, login_dto_1.SystemLoginDto]),
    __metadata("design:returntype", Promise)
], SystemAuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('logout'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '管理员登出' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '登出成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SystemAuthController.prototype, "logout", null);
__decorate([
    (0, common_1.Post)('refresh-token'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '刷新令牌' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '刷新成功' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '刷新令牌无效' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [refresh_token_dto_1.SystemRefreshTokenDto]),
    __metadata("design:returntype", Promise)
], SystemAuthController.prototype, "refreshToken", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Get)('profile'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取当前管理员信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SystemAuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Patch)('profile'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新当前管理员信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemAuthController.prototype, "updateProfile", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Post)('change-password'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '修改密码' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '密码修改成功' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '当前密码错误' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '密码格式不正确' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, change_password_dto_1.ChangePasswordDto]),
    __metadata("design:returntype", Promise)
], SystemAuthController.prototype, "changePassword", null);
__decorate([
    (0, common_1.Post)('reset-admin-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '重置admin密码（仅开发环境）' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SystemAuthController.prototype, "resetAdminPassword", null);
exports.SystemAuthController = SystemAuthController = __decorate([
    (0, swagger_1.ApiTags)('系统认证'),
    (0, common_1.Controller)('system-auth'),
    __metadata("design:paramtypes", [auth_service_1.SystemAuthService])
], SystemAuthController);
//# sourceMappingURL=auth.controller.js.map