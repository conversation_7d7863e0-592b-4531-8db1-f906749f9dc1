-- =====================================================
-- VIP经济系统重构 - 数据库结构升级
-- 版本: V12.0 (Stable Value & Progressive Trust)
-- 创建时间: 2025-06-29
-- =====================================================

-- 备份现有VIP配置数据
CREATE TABLE IF NOT EXISTS vip_configs_backup AS 
SELECT * FROM vip_configs;

-- 删除现有VIP配置表（保留备份）
DROP TABLE IF EXISTS vip_configs CASCADE;

-- 创建新的VIP配置表，支持完整的经济权益系统
CREATE TABLE vip_configs (
    id SERIAL PRIMARY KEY,
    
    -- 基础等级信息
    vip_level INTEGER UNIQUE NOT NULL CHECK (vip_level >= 0), -- VIP等级，从0开始
    level_name VARCHAR(50) NOT NULL, -- 等级名称，如 "体验用户"、"活跃认证"
    strategic_position VARCHAR(200), -- 核心战略定位描述
    
    -- EXP经验值系统（替代原积分系统）
    required_exp BIGINT NOT NULL CHECK (required_exp >= 0), -- 达到该等级所需的累计EXP
    
    -- EXP获取规则配置
    real_money_recharge_exp_ratio DECIMAL(10,4) DEFAULT 10.0000 CHECK (real_money_recharge_exp_ratio >= 0), -- 真金充值EXP比例 (1 INR = 10 EXP)
    real_money_flow_exp_ratio DECIMAL(10,4) DEFAULT 1.0000 CHECK (real_money_flow_exp_ratio >= 0), -- 真金流水EXP比例 (1 INR = 1 EXP)
    gold_payment_exp_ratio DECIMAL(10,4) DEFAULT 5.0000 CHECK (gold_payment_exp_ratio >= 0), -- 金币付费EXP比例 (1 INR = 5 EXP)
    gold_flow_exp_ratio DECIMAL(10,6) DEFAULT 0.001000 CHECK (gold_flow_exp_ratio >= 0), -- 金币流水EXP比例 (1000金币 = 1 EXP)
    daily_task_exp_min INTEGER DEFAULT 10 CHECK (daily_task_exp_min >= 0), -- 活跃任务最小EXP
    daily_task_exp_max INTEGER DEFAULT 50 CHECK (daily_task_exp_max >= daily_task_exp_min), -- 活跃任务最大EXP
    
    -- 每日奖励系统
    daily_gold_reward BIGINT DEFAULT 0 CHECK (daily_gold_reward >= 0), -- 每日可领取金币数量
    
    -- 官方回购系统权益
    buyback_enabled BOOLEAN DEFAULT FALSE, -- 是否开启官方回购资格
    buyback_rate INTEGER DEFAULT NULL CHECK (buyback_rate IS NULL OR buyback_rate > 0), -- 官方回购价格 (金币:1 INR)，如140表示140:1
    
    -- C2C交易系统权益  
    c2c_fee_rate DECIMAL(5,2) DEFAULT 15.00 CHECK (c2c_fee_rate >= 0 AND c2c_fee_rate <= 100), -- C2C手续费率(%)
    
    -- 真金系统权益
    rakeback_rate DECIMAL(5,2) DEFAULT 0.00 CHECK (rakeback_rate >= 0 AND rakeback_rate <= 100), -- 周返水比例(%)
    withdrawal_fee_rate DECIMAL(5,2) DEFAULT 5.00 CHECK (withdrawal_fee_rate >= 0 AND withdrawal_fee_rate <= 100), -- 提现手续费率(%)
    daily_withdrawal_limit BIGINT DEFAULT NULL CHECK (daily_withdrawal_limit IS NULL OR daily_withdrawal_limit > 0), -- 每日提现额度上限(INR)，NULL表示无上限
    withdrawal_priority INTEGER DEFAULT 1 CHECK (withdrawal_priority >= 1 AND withdrawal_priority <= 3), -- 提现优先级 1-普通 2-优先 3-VIP专享
    
    -- 解锁机制配置
    task_unlock_required BOOLEAN DEFAULT FALSE, -- 活跃任务是否需要游戏局数解锁
    task_unlock_games_required INTEGER DEFAULT 0 CHECK (task_unlock_games_required >= 0), -- 解锁所需游戏局数
    
    -- 状态和管理字段
    status INTEGER DEFAULT 1 CHECK (status IN (0, 1)), -- 状态：1-启用，0-禁用
    remark TEXT, -- 备注说明
    
    -- 审计字段
    created_by INTEGER, -- 创建人ID，关联sys_users表
    updated_by INTEGER, -- 最后更新人ID，关联sys_users表
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    
    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES sys_users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES sys_users(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX idx_vip_configs_level ON vip_configs(vip_level);
CREATE INDEX idx_vip_configs_status ON vip_configs(status);
CREATE INDEX idx_vip_configs_required_exp ON vip_configs(required_exp);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_vip_configs_updated_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_vip_configs_time
    BEFORE UPDATE ON vip_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_vip_configs_updated_time();

-- 插入新的VIP等级配置数据（基于需求文档表1.1、表1.2、表3.1）
INSERT INTO vip_configs (
    vip_level, level_name, strategic_position, required_exp, daily_gold_reward,
    buyback_enabled, buyback_rate, c2c_fee_rate, rakeback_rate, 
    withdrawal_fee_rate, daily_withdrawal_limit, withdrawal_priority,
    task_unlock_required, task_unlock_games_required, remark
) VALUES
-- VIP 0: 体验用户/流量基础
(0, '体验用户', '体验用户/流量基础', 0, 0, 
 FALSE, NULL, 15.00, 0.00, 
 NULL, NULL, 1, 
 FALSE, 0, '新用户默认等级，无提现资格'),

-- VIP 1: 活跃认证  
(1, '活跃认证', '活跃认证', 500, 50,
 FALSE, NULL, 15.00, 0.00,
 5.00, 2000, 1,
 FALSE, 0, '活跃认证用户，开启基础提现权限'),

-- VIP 2: 参与认证
(2, '参与认证', '参与认证', 3000, 150,
 FALSE, NULL, 15.00, 0.00,
 4.50, 10000, 1,
 FALSE, 0, '参与认证用户，提升提现额度'),

-- VIP 3: 付费门槛，C2C主要供给者
(3, '付费门槛', '付费门槛，C2C主要供给者', 15000, 400,
 FALSE, NULL, 15.00, 0.50,
 4.00, 50000, 1,
 TRUE, 10, '付费门槛用户，开启返水权益，任务需解锁'),

-- VIP 4: 核心付费用户，首次开启官方回购
(4, '核心付费用户', '核心付费用户，首次开启官方回购', 50000, 1000,
 TRUE, 140, 10.00, 0.80,
 3.50, 200000, 2,
 TRUE, 10, '核心付费用户，开启官方回购，享受优先提现'),

-- VIP 5: 高价值玩家 (HVP)
(5, '高价值玩家', '高价值玩家 (HVP)', 250000, 2500,
 TRUE, 135, 10.00, 1.20,
 3.00, 1000000, 2,
 TRUE, 10, '高价值玩家，更优回购价格'),

-- VIP 6: 核心利润贡献者，尊享权益
(6, '核心利润贡献者', '核心利润贡献者，尊享权益', 1200000, 8000,
 TRUE, 130, 5.00, 1.80,
 2.50, NULL, 3,
 TRUE, 10, '核心利润贡献者，无提现额度限制，VIP专享通道'),

-- VIP 7: 终身价值用户 (LTV)
(7, '终身价值用户', '终身价值用户 (LTV)', 5000000, 15000,
 TRUE, 125, 5.00, 2.50,
 2.00, NULL, 3,
 TRUE, 10, '终身价值用户，最优权益配置');

-- 添加表注释
COMMENT ON TABLE vip_configs IS 'VIP等级配置表 - 支持完整的经济权益系统 (V12.0)';
COMMENT ON COLUMN vip_configs.vip_level IS 'VIP等级，从0开始，唯一标识';
COMMENT ON COLUMN vip_configs.level_name IS '等级名称显示';
COMMENT ON COLUMN vip_configs.strategic_position IS '核心战略定位描述';
COMMENT ON COLUMN vip_configs.required_exp IS '达到该等级所需的累计EXP经验值';
COMMENT ON COLUMN vip_configs.buyback_rate IS '官方回购价格比例，如140表示140金币:1INR';
COMMENT ON COLUMN vip_configs.c2c_fee_rate IS 'C2C交易手续费率，百分比';
COMMENT ON COLUMN vip_configs.rakeback_rate IS '周返水比例，百分比';
COMMENT ON COLUMN vip_configs.withdrawal_fee_rate IS '提现手续费率，百分比';
COMMENT ON COLUMN vip_configs.daily_withdrawal_limit IS '每日提现额度上限(INR)，NULL表示无上限';
COMMENT ON COLUMN vip_configs.withdrawal_priority IS '提现优先级：1-普通，2-优先，3-VIP专享';

-- 验证数据插入
SELECT 
    vip_level,
    level_name,
    required_exp,
    daily_gold_reward,
    CASE WHEN buyback_enabled THEN CONCAT(buyback_rate, ':1') ELSE '无资格' END as buyback_rate_display,
    CONCAT(c2c_fee_rate, '%') as c2c_fee_display,
    CONCAT(rakeback_rate, '%') as rakeback_display
FROM vip_configs 
ORDER BY vip_level;
