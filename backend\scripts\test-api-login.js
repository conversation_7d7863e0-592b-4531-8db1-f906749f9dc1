// 测试API登录的脚本
const { execSync } = require('child_process');

function makeRequest(method, url, data = null, headers = {}) {
  const curlCmd = [
    'curl',
    '-s',
    '-X', method,
    '-H', 'Content-Type: application/json'
  ];

  // 添加额外的headers
  Object.entries(headers).forEach(([key, value]) => {
    curlCmd.push('-H', `${key}: ${value}`);
  });

  // 添加数据
  if (data) {
    curlCmd.push('-d', JSON.stringify(data));
  }

  curlCmd.push(url);

  try {
    const result = execSync(curlCmd.join(' '), { encoding: 'utf8' });
    return JSON.parse(result);
  } catch (error) {
    console.error('请求失败:', error.message);
    throw error;
  }
}

async function testAPILogin() {
  try {
    console.log('🔐 测试API登录流程...');

    // 1. 测试登录API
    console.log('\n📋 测试1: 登录API');
    const loginResponse = makeRequest('POST', 'http://localhost:3000/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });

    console.log(`  ✅ 登录成功`);
    console.log(`  响应内容: ${JSON.stringify(loginResponse, null, 2)}`);

    if (!loginResponse.access_token) {
      throw new Error('登录响应中没有access_token');
    }

    console.log(`  access_token长度: ${loginResponse.access_token.length}`);
    console.log(`  refresh_token长度: ${loginResponse.refresh_token ? loginResponse.refresh_token.length : 'undefined'}`);

    // 2. 检查token是否存在
    console.log('\n📋 测试2: 检查token');
    console.log(`  access_token存在: ${!!loginResponse.access_token}`);
    console.log(`  refresh_token存在: ${!!loginResponse.refresh_token}`);

    // 3. 测试获取用户信息API
    console.log('\n📋 测试3: 获取用户信息API');
    const userInfoResponse = makeRequest('GET', 'http://localhost:3000/api/users/info', null, {
      'Authorization': `Bearer ${loginResponse.access_token}`
    });

    console.log(`  ✅ 获取用户信息成功`);
    console.log(`  用户名: ${userInfoResponse.username}`);
    console.log(`  超级管理员: ${userInfoResponse.isSuperAdmin}`);
    console.log(`  roles属性存在: ${userInfoResponse.hasOwnProperty('roles')}`);
    console.log(`  roles类型: ${typeof userInfoResponse.roles}`);
    console.log(`  roles是数组: ${Array.isArray(userInfoResponse.roles)}`);
    console.log(`  roles长度: ${userInfoResponse.roles ? userInfoResponse.roles.length : 'undefined'}`);

    if (userInfoResponse.roles && userInfoResponse.roles.length > 0) {
      userInfoResponse.roles.forEach((role, index) => {
        console.log(`    角色${index + 1}: ${role.name} (${role.code})`);
      });
    }

    // 4. 测试获取路由API
    console.log('\n📋 测试4: 获取路由API');
    const routesResponse = makeRequest('GET', 'http://localhost:3000/api/routes', null, {
      'Authorization': `Bearer ${loginResponse.access_token}`
    });

    console.log(`  ✅ 获取路由成功`);
    console.log(`  路由数量: ${routesResponse.length}`);
    
    // 查找VIP相关路由
    const vipRoutes = routesResponse.filter(route =>
      route.path && (route.path.includes('vip') || route.path.includes('config'))
    );
    
    console.log(`  VIP相关路由 (${vipRoutes.length} 个):`);
    vipRoutes.forEach(route => {
      console.log(`    路径: ${route.path}, 名称: ${route.name}, 组件: ${route.component}`);
      if (route.children && route.children.length > 0) {
        route.children.forEach(child => {
          console.log(`      子路由: ${child.path}, 名称: ${child.name}, 组件: ${child.component}`);
        });
      }
    });

    // 5. 测试权限API
    console.log('\n📋 测试5: 获取权限API');
    const permissionsResponse = makeRequest('GET', 'http://localhost:3000/api/permissions/list', null, {
      'Authorization': `Bearer ${loginResponse.access_token}`
    });

    console.log(`  ✅ 获取权限成功`);
    console.log(`  权限数量: ${permissionsResponse.length}`);
    
    // 查找VIP相关权限
    const vipPermissions = permissionsResponse.filter(permission =>
      permission.code && permission.code.includes('vip')
    );
    
    console.log(`  VIP相关权限 (${vipPermissions.length} 个):`);
    vipPermissions.forEach(permission => {
      console.log(`    代码: ${permission.code}, 名称: ${permission.name}`);
    });

    console.log('\n🎉 API登录流程测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error(`  状态码: ${error.response.status}`);
      console.error(`  响应数据: ${JSON.stringify(error.response.data)}`);
    }
    console.error(error.stack);
    process.exit(1);
  }
}

// 执行测试
if (require.main === module) {
  testAPILogin()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testAPILogin };
