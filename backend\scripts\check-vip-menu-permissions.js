const { Client } = require('pg');
const { getDatabaseConfig } = require('../database/config/database-config');

async function checkVipMenuPermissions() {
  const config = getDatabaseConfig('local');
  const client = new Client(config);
  
  try {
    await client.connect();
    
    console.log('🔍 检查admin用户的VIP菜单权限:');
    
    // 检查admin用户的角色
    const userRoles = await client.query(`
      SELECT u.username, r.name as role_name, r.id as role_id
      FROM sys_users u 
      JOIN sys_user_roles ur ON u.id = ur.user_id 
      JOIN sys_roles r ON ur.role_id = r.id 
      WHERE u.username = $1
    `, ['admin']);
    
    console.log('Admin用户角色:');
    console.table(userRoles.rows);
    
    // 检查admin用户是否是超级管理员
    const adminUser = await client.query(`
      SELECT id, username, is_super_admin
      FROM sys_users
      WHERE username = $1
    `, ['admin']);

    console.log('Admin用户信息:');
    console.table(adminUser.rows);
    
    // 检查所有VIP相关菜单
    const allVipMenus = await client.query(`
      SELECT id, title, path, component, parent_id, status
      FROM sys_menus 
      WHERE path LIKE '%vip%' OR title LIKE '%VIP%'
      ORDER BY id
    `);
    
    console.log('所有VIP相关菜单:');
    console.table(allVipMenus.rows);
    
    // 检查VIP菜单的权限代码
    const vipMenuPermissions = await client.query(`
      SELECT id, title, path, permission_code, component
      FROM sys_menus
      WHERE path LIKE '%vip%' OR title LIKE '%VIP%'
      ORDER BY id
    `);

    console.log('VIP菜单的权限代码:');
    console.table(vipMenuPermissions.rows);
    
  } catch (error) {
    console.error('检查权限时出错:', error);
  } finally {
    await client.end();
  }
}

checkVipMenuPermissions();
