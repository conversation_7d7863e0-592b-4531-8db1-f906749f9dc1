import{u as k1,J as mn,j as C,L as mi,K as pi,M as gi,N as yi,T as vi,O as qi,d as pn,P as xi}from"./index-DD0gcXtR.js";import{a as c}from"./react-BUTTOX-3.js";import{U as Ei,d as Ti,t as Mi,a7 as Pi,ao as Ci,ap as gn}from"./antd-FyCAPQKa.js";import{F as Fi,a as Ai}from"./code-login-D5ixr9_g.js";import"./rules-Br-WHaJV.js";import"./index-BDysGMcU.js";function Di(t){return[{icon:C.jsx(mi,{}),label:t("authority.layout.alignLeft"),key:"layout-left"},{icon:C.jsx(pi,{}),label:t("authority.layout.alignCenter"),key:"layout-center"},{icon:C.jsx(gi,{}),label:t("authority.layout.alignRight"),key:"layout-right"}]}function Si(){var o;const{t}=k1(),e=mn(l=>l.pageLayout),n=mn(l=>l.setPreferences);function s(l){n({pageLayout:l})}const i=({key:l})=>{s(l)},r=Di(t),a=C.jsx(Ei,{menu:{items:r,selectable:!0,onClick:i,selectedKeys:[e]},trigger:["click"],arrow:!1,placement:"bottom",children:C.jsx(Ti,{type:"text",icon:(o=r.find(l=>l.key===e))==null?void 0:o.icon})});return{pageLayout:e,setPageLayout:s,layoutButtonTrigger:a}}const bi=t=>c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024",...t},c.createElement("path",{fill:"#2466F8",d:"m589.09 205.63-20.05-.58-21.9-.42q-5.8-34.6-11.8-55.2A1364 1364 0 0 0 521 103.28c-1.42-4.29-4.22-7.94-9.26-5.24-2.4 1.28-3.88 6.09-4.7 8.51a711 711 0 0 0-25.67 98.01l-24.34.62a591 591 0 0 0-18.78.5q-.9.04-.34-1.45l11.51-32.69q2.74-3.33 3.68-5.49 3.26-7.5 8-21.46a357 357 0 0 1 10-20.48 307 307 0 0 1 15.51-26.19 18 18 0 0 0 2.78-3.04 5310 5310 0 0 1 15.19-21.06q1.3-1.8 2.54-4.97l4.85-5.89a1.1 1.1 0 0 1 1.65-.05q3.51 3.84 6.63 7.83c30.86 39.42 53.82 86.63 68.84 134.89"}),c.createElement("path",{fill:"#1350CE",d:"M507.12 68.85a23 23 0 0 1-2.54 4.97 5296 5296 0 0 0-15.19 21.06 18 18 0 0 1-2.78 3.04 302 302 0 0 1 20.51-29.07m40.02 135.78-8.71-.17-22.72-.23a.75.75 0 0 1-.75-.76q.17-31.97.29-64.22a218 218 0 0 0-.11-7.6.6.6 0 0 0-.48-.53l-.33-.04a.9.9 0 0 0-.9.49 1 1 0 0 0-.07.35l.19 70.87q0 1.54-1.53 1.55l-30.65.22a711 711 0 0 1 25.67-98.01c.82-2.42 2.3-7.23 4.7-8.51 5.04-2.7 7.84.95 9.26 5.24q7.57 22.95 14.34 46.15 6 20.6 11.8 55.2"}),c.createElement("path",{fill:"#2466F8",d:"m630.36 116.34 7.13.05a.76.76 0 0 1 .76.75l.05 4.86a.8.8 0 0 1-.22.54 1 1 0 0 1-.24.17 1 1 0 0 1-.29.06l-7.17.12a.76.76 0 0 0-.75.74l-.13 7.26a.76.76 0 0 1-.76.74l-4.71-.04a.76.76 0 0 1-.76-.75l-.16-7.32a.76.76 0 0 0-.75-.74l-7.72-.13a.76.76 0 0 1-.75-.76l.04-4.58a.76.76 0 0 1 .74-.76l7.75-.18a.76.76 0 0 0 .74-.75l.13-7.47a.8.8 0 0 1 .22-.52.8.8 0 0 1 .52-.22l4.57-.08a.76.76 0 0 1 .77.74l.24 7.54a.76.76 0 0 0 .75.73"}),c.createElement("path",{fill:"#C3E1FB",d:"m321.54 115.71-.86 24.67a1.4 1.4 0 0 1-.48 1.03 1.5 1.5 0 0 1-1.08.37l-3.91-.3a1.45 1.45 0 0 1-1.33-1.61l2.8-24.49a1.45 1.45 0 0 1 1.53-1.28l1.96.11a1.45 1.45 0 0 1 1.37 1.5m-20.92 22.9q1.14 1.34-.18 2.49-.24.21-.55.3-2.12.67-3.61-.79a123 123 0 0 1-11.12-12.5q-1.12-1.43.23-2.66l.59-.52q1.7-1.54 3.18.21zm50.2-13.96-.05-.04a1.5 1.5 0 0 0-2.13.09l-13.52 14.65a1.5 1.5 0 0 0 .08 2.14l.05.04a1.5 1.5 0 0 0 2.13-.09l13.52-14.65a1.5 1.5 0 0 0-.08-2.14m187.61 79.81a177 177 0 0 0-7.06 35.2q-23.04-9.27-42.88 4.05-12.76 8.55-17.36 24.01 1.23-13.87 2.97-23.83c2.66-15.23 4.85-25.94 4.89-38.19q0-.5-.49-.49l-21.47-.03 24.34-.62 30.65-.22q1.54 0 1.53-1.55l-.19-70.87a.8.8 0 0 1 .3-.64.9.9 0 0 1 .67-.2l.33.04q.2.04.34.18.12.15.14.35a218 218 0 0 1 .11 7.6q-.12 32.25-.29 64.22a.8.8 0 0 0 .22.54.7.7 0 0 0 .53.22zm-23.42 1.81a.6.6 0 0 0-.17-.42.6.6 0 0 0-.43-.18h-.04a.6.6 0 0 0-.55.37 1 1 0 0 0-.05.23l-.04 23.38a.6.6 0 0 0 .18.42.6.6 0 0 0 .42.18h.04a.6.6 0 0 0 .6-.6zm-168.39-43.74c-19.7 3.49-39.15 6.11-58.33 12.08q1.6-20.64 20.15-28.18c15.04-6.12 31.37 1.93 38.18 16.1"}),c.createElement("path",{fill:"#1350CE",d:"m449.42 171.54 11.68-26.95a293 293 0 0 1-8 21.46q-.94 2.16-3.68 5.49"}),c.createElement("path",{fill:"#B4D9FD",d:"M346.62 162.53c5.63 12.98 2.84 28.49-8.53 37.59q-4.74 3.79-7.98 11.18a95 95 0 0 0-23.38-1.22 88 88 0 0 0-10.39-13.43q-8.45-9.15-8.05-22.04c19.18-5.97 38.63-8.59 58.33-12.08m-21.37 5.36a.6.6 0 0 0-.5-.48 1 1 0 0 0-.25 0l-.04.01a.6.6 0 0 0-.48.5 1 1 0 0 0 0 .25l1.5 6.86a.6.6 0 0 0 .28.4.6.6 0 0 0 .48.08l.04-.01a1 1 0 0 0 .22-.1.6.6 0 0 0 .26-.4 1 1 0 0 0 0-.25zm6.3 18.02-1.58-5.33a.15.15 0 0 0-.18-.11l-.06.02q-.41.14-.48 1c-.04.56.06 1.28.27 1.99l.07.23c.21.71.52 1.36.86 1.81s.67.66.94.59l.06-.02a.2.2 0 0 0 .09-.07z"}),c.createElement("path",{fill:"#62A1FF",d:"m324.5 167.41-.04.01a.63.63 0 0 0-.48.75l1.5 6.86a.63.63 0 0 0 .76.48l.04-.01a.63.63 0 0 0 .48-.75l-1.5-6.86a.63.63 0 0 0-.76-.48m7.05 18.5-.01.11a.2.2 0 0 1-.09.07l-.06.02q-.42.1-.94-.58a6 6 0 0 1-.86-1.82l-.07-.23a6 6 0 0 1-.27-2c.04-.55.21-.91.48-.99l.06-.02a.15.15 0 0 1 .18.11z"}),c.createElement("path",{fill:"#E3F1FB",d:"M437.91 204.23q-.56 1.5.34 1.45a591 591 0 0 1 18.78-.5l21.47.03q.5 0 .49.49c-.04 12.25-2.23 22.96-4.89 38.19a290 290 0 0 0-2.97 23.83 44 44 0 0 0-1.48 15.27c-2.92 8.3-2.21 15.57-3.04 23.7-2.51 24.55-1.48 48-2.83 71.97a30.5 30.5 0 0 1-3.5 12.97 18 18 0 0 0-2.06 5.6 39 39 0 0 0-.47 5.72q-.2 15.8-.67 32.1-.2 6.57 2.35 11.03a26 26 0 0 1 3.47 12.32q.22 8.46.19 17.14a.9.9 0 0 1-.42.77 8 8 0 0 1-2.68 1.16q-.7.15-.11.56a15 15 0 0 1 2.52 2.19q.57.6-.07.07a5 5 0 0 1-.89-.98.2.2 0 0 0-.16-.1h-.1l-.09.05-.21.16q-.01.02.02.07l3.41 4.21q.35.43.33.98a462 462 0 0 0-.24 32.19c.33 12.07-.28 24.05-.54 35.99q-.1 4.95-2.03 9.61a.7.7 0 0 1-.69.47l-25.85.16-23.53.3q-1.59.02-1.59-1.57v-30.62l-.16-29.21q.98-14 .95-33.25l-.1-78.33c-.02-13.49.27-26.52 1.5-40.08q3.55-39.25 5.62-53.01a1609 1609 0 0 1 5.53-34.32q1.8-10.5 3.83-19.61c2.93-13.16 5.4-27.21 10.57-39.17m17.61 51.56a.6.6 0 0 0-.08-.43.6.6 0 0 0-.36-.24l-.06-.01a.57.57 0 0 0-.67.45l-1.31 6.75a.6.6 0 0 0 .08.43.6.6 0 0 0 .37.24l.05.01a.57.57 0 0 0 .67-.45zm-2.11 11.99a.6.6 0 0 0-.1-.4.6.6 0 0 0-.37-.22h-.04a.55.55 0 0 0-.62.46l-1.4 10.02a.6.6 0 0 0 .1.4.6.6 0 0 0 .36.22h.04a.55.55 0 0 0 .62-.46zm-2.1 15.06a.5.5 0 0 0-.45-.54h-.04a1 1 0 0 0-.2.02.5.5 0 0 0-.29.24l-.05.19-1.1 11.93a.5.5 0 0 0 .46.54h.04a1 1 0 0 0 .2-.02.5.5 0 0 0 .29-.24l.06-.19zm-7.13 89.41a51 51 0 0 0 1.94-11.77c.19-5.37 1.37-10.9 1.07-16.28q-.4-7.12.88-13.46a.7.7 0 0 0-.1-.5.7.7 0 0 0-.45-.28l-.27-.05a.4.4 0 0 0-.28.08.4.4 0 0 0-.14.26q-.9 13.94-1.76 28.77-.3 5-1.79 12.65a.54.54 0 0 0 .43.63l.31.05.1-.02zm-2.17 8.65c-.76.75-.92 1.92-1.15 2.96q-.2.9.38.19a5.5 5.5 0 0 0 1.33-2.88q.11-.94-.56-.27m-3.87 40.91c-1.39-5.35-1.95-11.84-1.93-17.36a38 38 0 0 1 3.78-15.99q.11-.25-.14-.36l-.24-.1a.46.46 0 0 0-.6.24q-3 7.08-3.61 13.25-1.38 13.65 3.69 27.62a318 318 0 0 0 4 10.39q.18.45.63.27l.29-.12a.3.3 0 0 0 .17-.16.3.3 0 0 0 0-.24 118 118 0 0 1-6.04-17.44m11.22 33.11c-.61-1.5-.86-3.11-1.53-4.63q-.44-1.01-.66.08-.21 1.06.19 2.19a121 121 0 0 0 7.28 16.43q.08.15.26.14l.56-.03q.47-.03.25-.45a151 151 0 0 1-6.35-13.73"}),c.createElement("path",{fill:"#B4D9FD",d:"m547.14 204.63 21.9.42c12.18 12.41 15.08 26.24 15.64 43.01q.78 23.1-.71 49.57-.03.6-.58.38a13.6 13.6 0 0 0-6.71-.9c-7.51.93-7.93 7.99-7.9 14.12.03 8.61-.88 19.74 11.72 18.57q.64-.05.74.58c3.29 20.55 4.83 41.28 3.88 61.89-1.64 35.5-2.52 70.7-.94 106.33q.81 18.4 6.32 34.08l.58 2.82a16 16 0 0 1-4.9-.66 579 579 0 0 1-12.8-3.99 12 12 0 0 1-6.2-4.17 26 26 0 0 0-4.19-4.38c-1.69-1.39-1.76-5.5-1.84-7.56a7.7 7.7 0 0 1 2.93 3.58q.2.45.65.26l4.39-1.8a25 25 0 0 0-2.4-8.22q.4-1.7-1.62-2.31a.9.9 0 0 1-.63-.62l-.52-1.54q1.03-7.05-2.42-7.36l3.55-4.32 12.6-11.31q1.35-1.2-.46-1.2-4.66.04-9.6.18a7.8 7.8 0 0 1-5.31-1.82 490 490 0 0 1-21.3-18.93 34 34 0 0 0-6.06-4.55.9.9 0 0 0-1.3.43l-.63 1.51a5 5 0 0 1 .16-2.39.8.8 0 0 1 .69-.56l10.38-1.29 13.99-.35q.52 0 .73-.49a76 76 0 0 0 4.3-12.06c6.05-23.19-3.33-45.13-24.04-56.84a139 139 0 0 0-7.2-25.95q2.56-.09 1.92-1.15 2.8-16.56 6.22-33.64.74-3.65.9-9.68c24.41-21.41 19.59-58.45-9.7-72.66a177 177 0 0 1 7.06-35.2zm15.15 249.89 14.84 12.76q.22.2.19-.1a.5.5 0 0 0-.15-.32 126 126 0 0 0-14.88-12.34q-.7-1.2-1.42-1.12-.47.06-.11.37.69.6 1.53.75"}),c.createElement("path",{fill:"#9DCBFE",d:"M589.09 205.63a542 542 0 0 0 4.46 16.66 449 449 0 0 1 4.46 16.65 618 618 0 0 1 12.7 72.13l-9.32-.19a7 7 0 0 0-1.56.16q-1.46.35-.97 1.4.05.12.19.12l10.68-.28a.8.8 0 0 1 .74.4l.31.48q.2.47.28 1.09a715 715 0 0 1 4.55 60.75q.75 23.92-.28 78.75-.18 9.57.55 47.8l.14 26.24q0 1.5-1.48 1.71l-4.31.6q-1.45.2-1.65-1.26c-1.29-9.73-6.32-14.16-16.11-10.55-3.66 1.36-3.5 5.6-1.73 8.4q-.57 1.8-.38 2.62l.14 3.37q-5.5-15.67-6.32-34.08c-1.58-35.63-.7-70.83.94-106.33.95-20.61-.59-41.34-3.88-61.89q-.1-.63-.74-.58c-12.6 1.17-11.69-9.96-11.72-18.57-.03-6.13.39-13.19 7.9-14.12q3.3-.42 6.71.9.55.2.58-.38 1.5-26.46.71-49.57c-.56-16.77-3.46-30.6-15.64-43.01zm5.93 106.08a.7.7 0 0 0-.38-.65 1 1 0 0 0-.26-.07l-10.37-.67a.7.7 0 0 0-.5.16.7.7 0 0 0-.23.47v.06a.7.7 0 0 0 .17.5.7.7 0 0 0 .46.22l10.38.67a.7.7 0 0 0 .5-.16.7.7 0 0 0 .23-.47zm-2.5 85.31q1.1-14.56-.59-27.84a242 242 0 0 0-2.48-15.38q-.11-.58-.55-.18l-.19.18a1.3 1.3 0 0 0-.39 1.18 167 167 0 0 1 2.45 47.84 1.3 1.3 0 0 0 .47 1.14l.23.2a.4.4 0 0 0 .39.06.4.4 0 0 0 .22-.33q.17-3.21.44-6.87m-78.1-191.35h-.05a.6.6 0 0 0-.6.6l-.04 23.38a.6.6 0 0 0 .6.6h.04a.6.6 0 0 0 .6-.6l.04-23.38a.6.6 0 0 0-.6-.6M330.1 211.3l-1.93 4.89-19.66.14-1.79-6.25a95 95 0 0 1 23.38 1.22"}),c.createElement("path",{fill:"#1350CE",d:"m308.52 216.33 19.66-.14q2.23 3.12.37 6.63-.21.39-.17.84c.2 2.64 1.76 5.5-2.18 6.49a1.6 1.6 0 0 0-1.19 1.4c-.18 1.79-.3 3.69-1.69 5.01-3.17 3.02-12.14 2.36-11.39-3.96q.2-1.64-1.45-1.84c-2.78-.34-.78-3.12-2.75-4.57a1.67 1.67 0 0 1-.41-2.27q1.2-1.83.42-3.91c-.45-1.18-.19-2.87.78-3.68"}),c.createElement("path",{fill:"#62A1FF",d:"M639.61 229.09q.67-.1 1.08.39a.6.6 0 0 1 .11.52.6.6 0 0 1-.34.4q-.75.34-1.24-.28a.7.7 0 0 1-.1-.63.6.6 0 0 1 .49-.4"}),c.createElement("path",{fill:"#9DCBFE",d:"M658.19 229.41q.78-.65 1.39-.01a.4.4 0 0 1 .12.32.5.5 0 0 1-.16.32q-.75.63-1.36.09a.5.5 0 0 1-.16-.36.5.5 0 0 1 .17-.36"}),c.createElement("path",{fill:"#1350CE",d:"M531.37 239.66c29.29 14.21 34.11 51.25 9.7 72.66-26.57 22.38-68.51 5.78-71.42-29.33a44 44 0 0 1 1.48-15.27q4.6-15.45 17.36-24.01 19.84-13.32 42.88-4.05m-16.21 1.96a1.77 1.77 0 0 0-3.02-1.25 1.8 1.8 0 0 0 0 2.5 1.8 1.8 0 0 0 2.5 0c.33-.33.52-.78.52-1.25m9.04-.1a1.33 1.33 0 0 0-1.28-1.38l-1.5-.06a1.33 1.33 0 0 0-1.38 1.28l-.04 1a1.3 1.3 0 0 0 .78 1.26q.24.1.5.12l1.5.06a1.33 1.33 0 0 0 1.38-1.28zm-17.27.13a1.7 1.7 0 0 0-.62-1.14 1.7 1.7 0 0 0-1.24-.38l-.92.1a1.7 1.7 0 0 0-1.14.61 1.7 1.7 0 0 0-.38 1.25l.04.34a1.7 1.7 0 0 0 .61 1.14 1.7 1.7 0 0 0 1.25.38l.92-.1a1.7 1.7 0 0 0 1.14-.61 1.7 1.7 0 0 0 .38-1.25zm8.69 70.01q16.92-2.5 24.87-15.05a33 33 0 0 0 5.31-18.97q-.3-12.36-9.79-22.19a17 17 0 0 0-3.6-2.87 39 39 0 0 0-16.86-5.8q-8.16-.9-16.36 2.98c-9.29 4.4-17.42 14.39-18.53 25.47-.81 8.21.59 15.04 5.59 22.06q10.75 15.1 29.37 14.37m.75 5.47a2.34 2.34 0 1 0-4.68 0 2.34 2.34 0 0 0 4.68 0"}),c.createElement("path",{fill:"#E3F1FB",d:"M513.39 243.39a1.77 1.77 0 1 0 0-3.54 1.77 1.77 0 0 0 0 3.54m9.53-3.25-1.5-.06a1.33 1.33 0 0 0-1.38 1.28l-.04 1a1.33 1.33 0 0 0 1.28 1.38l1.5.06a1.33 1.33 0 0 0 1.38-1.28l.04-1a1.33 1.33 0 0 0-1.28-1.38m-17.85 0-.92.09a1.7 1.7 0 0 0-1.52 1.86l.04.34a1.7 1.7 0 0 0 1.86 1.52l.92-.1a1.7 1.7 0 0 0 1.52-1.86l-.04-.34a1.7 1.7 0 0 0-1.86-1.52"}),c.createElement("path",{fill:"#9DCBFE",d:"m455.08 255.12-.06-.01a.57.57 0 0 0-.67.45l-1.32 6.75a.57.57 0 0 0 .45.67l.06.01a.57.57 0 0 0 .67-.45l1.31-6.75a.57.57 0 0 0-.45-.67m-2.14 12.04h-.04a.55.55 0 0 0-.62.46l-1.4 10.02a.55.55 0 0 0 .46.62h.04a.55.55 0 0 0 .62-.46l1.4-10.02a.55.55 0 0 0-.46-.62m354.36 8.92c-4.93 1.58-6.19 4.61-6.29 9.48q0 .5.49.58a19 19 0 0 1 7.31 2.9c-9.5-4.13-21.47-1.09-25.38 9.2a18.4 18.4 0 0 0 .87 15.53c4.26 8.3 14.81 10.74 23.46 7.77-4.02 11.85-2.14 24.65 2.61 36.02l-6.73 11.87a.5.5 0 0 1-.44.25.5.5 0 0 1-.44-.25 588 588 0 0 1-16.82-31.76 153 153 0 0 1-9.51-24.36 38 38 0 0 1-1.28-8.72 26 26 0 0 1 7.58-20.09q9.76-10.15 24.57-8.42"}),c.createElement("path",{fill:"#62A1FF",d:"M807.3 276.08q17.4 2.56 24.02 18.79a26.5 26.5 0 0 1 .81 18.76q-2.44 7.5-6.94 16.28a824 824 0 0 1-14.82 27.65c-4.75-11.37-6.63-24.17-2.61-36.02 12.9-6.09 13.43-25.64 1.05-32.5a19 19 0 0 0-7.31-2.9q-.5-.08-.49-.58c.1-4.87 1.36-7.9 6.29-9.48"}),c.createElement("path",{fill:"#2466F8",d:"m716.95 290.22.03 4.42a.83.83 0 0 1-.81.83l-6.97.2a.83.83 0 0 0-.81.81l-.18 7.1a.83.83 0 0 1-.82.81l-4.38.04a.83.83 0 0 1-.84-.81l-.18-7.08a.83.83 0 0 0-.8-.8l-7.54-.24a.83.83 0 0 1-.81-.83l-.02-4.24a.83.83 0 0 1 .79-.83l7.55-.33a.83.83 0 0 0 .79-.81l.12-7.07a.83.83 0 0 1 .83-.82l4.39-.01a.83.83 0 0 1 .83.8l.23 7.12a.83.83 0 0 0 .82.8l6.97.12a.83.83 0 0 1 .81.82"}),c.createElement("path",{fill:"#B4D9FD",d:"M450.86 282.3h-.04a.5.5 0 0 0-.55.45l-1.08 11.93a.5.5 0 0 0 .45.54h.04a.5.5 0 0 0 .54-.45l1.1-11.93a.5.5 0 0 0-.46-.54"}),c.createElement("path",{fill:"#C3E1FB",d:"M469.65 282.99c2.91 35.11 44.85 51.71 71.42 29.33a60 60 0 0 1-.9 9.68q-3.42 17.08-6.22 33.64l-31.73-.41a.5.5 0 0 0-.34.11.5.5 0 0 0-.18.3l-.06.26a.5.5 0 0 0 .28.59q.1.05.23.05l29.88.25q4.85 12.6 7.2 25.95a53.5 53.5 0 0 0-25.48-6.53q-22.87-.08-38.4 16.63-9.82 10.57-12.37 23.42-5.61 28.29 15.82 47.86-2.1 4.2-2.02 10.67.1 9.45.32 19.23.09 4.23 1.69 8.79.21.58.81.67l5.52.83q.6.09.69-.51c2.03-14.28 2.61-26.06-5.89-38.65q14.63 12.05 33.33 12.16 8.48.06 16.99-.8.5-.04.56-.54l.64-5.45.23 8.82q0 .5.33.89a76 76 0 0 0 24.54 19.72.7.7 0 0 0 .81-.08q.6-.48 1.26-.55.56 1.3 1.96-.15a1.12 1.12 0 0 1 1.92.61q.32 2.13 1.46 4.31l.52 1.54q.15.47.63.62 2.03.6 1.62 2.31-.72-1.07-1.67-1.22-.57-.08-.73.46a18 18 0 0 0-.46 8.88 1 1 0 0 0 .22.45 1 1 0 0 0 .92.31 1 1 0 0 0 .45-.23q.97-.9 3.67-.43l-4.39 1.8q-.45.2-.65-.26a7.7 7.7 0 0 0-2.93-3.58 38 38 0 0 1-1.22-2.63q-.4-1 .36-.26a1 1 0 0 1 .19.32q.24.86.16-.03a.8.8 0 0 0-.39-.64 3 3 0 0 1-.56-.5 149 149 0 0 1-3.06-3.66q-.7-.31.23-1.5.32-.4.19-.92l-.55-2.11a.8.8 0 0 0-.5-.53.8.8 0 0 0-.71.12 15 15 0 0 1-4.58 2.5 1.4 1.4 0 0 1-1.23-.16 1.4 1.4 0 0 1-.63-1.07c-.09-1.12-.87-1.84-1.77-2.39a128 128 0 0 1-14.86-10.51 8 8 0 0 1-2.32-3.42q-.12-.35-.39-.09l-3.33 3.16q-2.37.31-4.68.33-16.08.08-27.77.47-.43 0-.4.42.05.46.5.46l31.36-.08a20 20 0 0 0-1.26 4.89 6 6 0 0 0 1.27 4.3 20 20 0 0 0 3.21 3.44 154 154 0 0 1 1.3 57.72q-.08.49-.57.52l-5.35.42-88.5-.21 25.85-.16q.5 0 .69-.47a27 27 0 0 0 2.03-9.61c.26-11.94.87-23.92.54-35.99a462 462 0 0 1 .24-32.19 1.4 1.4 0 0 0-.33-.98l-3.41-4.21q-.04-.05-.02-.07l.21-.16.09-.04a.2.2 0 0 1 .19.02l.07.07q.38.56.89.98.64.54.07-.07a15 15 0 0 0-2.52-2.19q-.58-.4.11-.56a8 8 0 0 0 2.68-1.16.9.9 0 0 0 .42-.77q.03-8.68-.19-17.14a26 26 0 0 0-3.47-12.32q-2.55-4.46-2.35-11.03.48-16.3.67-32.1.03-2.93.47-5.72.4-2.55 2.06-5.6a30.5 30.5 0 0 0 3.5-12.97c1.35-23.97.32-47.42 2.83-71.97.83-8.13.12-15.4 3.04-23.7m24.62 49.6a.5.5 0 0 0-.36-.58l-9.27-2.02a.5.5 0 0 0-.57.37v.02a.5.5 0 0 0 .36.57l9.27 2.02a.5.5 0 0 0 .57-.37zm39.93-.7a.5.5 0 0 0-.6-.43l-6.53 1.13a.5.5 0 0 0-.42.6l.01.06a.5.5 0 0 0 .6.42l6.52-1.12a.5.5 0 0 0 .43-.6zm-37.69.77q-.9.07-.07.41 1.76.74 3.88.75a.4.4 0 0 0 .29-.12.5.5 0 0 0 .13-.27q.04-.51-.46-.6-1.77-.3-3.77-.17m26.38 1.17a.65.65 0 0 0-.65-.64l-17.46.12a.65.65 0 0 0-.65.66v.03a.65.65 0 0 0 .66.65l17.45-.12a.65.65 0 0 0 .65-.66zm-30.86 22q0-.15-.28-.28a4 4 0 0 0-.8-.24 11 11 0 0 0-1.21-.16 18 18 0 0 0-1.43-.06c-.98 0-1.93.07-2.63.2q-1.07.21-1.09.52 0 .15.28.28t.8.24 1.21.16a18 18 0 0 0 4.06-.15q1.07-.2 1.09-.5M469 487.12q.06-.06-.03-.22a2 2 0 0 0-.28-.4l-.49-.54a12 12 0 0 0-1.28-1.1l-.6-.39a2 2 0 0 0-.44-.21q-.17-.06-.22 0-.07.08.03.22.08.17.28.4.2.26.49.54a12 12 0 0 0 1.28 1.1q.32.22.6.39.26.16.44.21.17.06.22 0"}),c.createElement("path",{fill:"#FAF9F9",d:"M808.81 289.04c12.38 6.86 11.85 26.41-1.05 32.5-8.65 2.97-19.2.53-23.46-7.77a18.4 18.4 0 0 1-.87-15.53c3.91-10.29 15.88-13.33 25.38-9.2"}),c.createElement("path",{fill:"#9DCBFE",d:"m248 308.57-3.06.97q-.48.15-.79.54c-2.12 2.61-4.28 6.52.07 8.47l3 1.35a29 29 0 0 0-8.51.51c-10.31 2.17-14.51 12.25-12.1 21.89 2.44 9.75 11.99 14.92 21.85 12.56q.1 12.87 1.37 23.81a50 50 0 0 0 2.97 11.84l-5.89 10.4a.7.7 0 0 1-1.22-.01 559 559 0 0 1-15.9-30.76 190 190 0 0 1-10.05-24.99c-5.8-18.51 8.86-37.78 28.26-36.58"}),c.createElement("path",{fill:"#62A1FF",d:"M248 308.57q13.3.66 21.4 9.72c9.05 10.11 8.71 21.96 3.73 33.84q-5.2 12.33-20.33 38.38a50 50 0 0 1-2.97-11.84q-1.26-10.94-1.37-23.81 8.88-2.85 11.8-11c3.57-9.96-1.94-22.36-13.04-23.96l-3-1.35c-4.35-1.95-2.19-5.86-.07-8.47q.3-.39.79-.54z"}),c.createElement("path",{fill:"#C3E1FB",d:"m594.39 310.99-10.38-.67a.7.7 0 0 0-.73.63v.06a.7.7 0 0 0 .64.72l10.37.67a.7.7 0 0 0 .73-.63v-.06a.7.7 0 0 0-.63-.72"}),c.createElement("path",{fill:"#E3F1FB",d:"M610.71 311.07q-.15.75.07 2.09l-.31-.48a.8.8 0 0 0-.74-.4l-10.68.28q-.15 0-.19-.12-.49-1.05.97-1.4a7 7 0 0 1 1.56-.16z"}),c.createElement("path",{fill:"#FAF9F9",d:"M514.03 319.47a2.34 2.34 0 1 0 0-4.68 2.34 2.34 0 0 0 0 4.68m-266.81.43c11.1 1.6 16.61 14 13.04 23.96q-2.92 8.15-11.8 11c-9.86 2.36-19.41-2.81-21.85-12.56-2.41-9.64 1.79-19.72 12.1-21.89a29 29 0 0 1 8.51-.51"}),c.createElement("path",{fill:"#B4D9FD",d:"m444.18 372.25-.06.08-.1.02-.31-.05a.54.54 0 0 1-.43-.63q1.5-7.65 1.79-12.65.86-14.84 1.76-28.77a.37.37 0 0 1 .42-.34l.27.05a.7.7 0 0 1 .55.52 1 1 0 0 1 0 .26 54 54 0 0 0-.88 13.46c.3 5.38-.88 10.91-1.07 16.28a51 51 0 0 1-1.94 11.77"}),c.createElement("path",{fill:"#62A1FF",d:"m493.9 332.01-9.26-2.02a.5.5 0 0 0-.57.37v.02a.5.5 0 0 0 .36.57l9.27 2.02a.5.5 0 0 0 .57-.37v-.02a.5.5 0 0 0-.36-.57m39.7-.55-6.53 1.13a.5.5 0 0 0-.42.6l.01.06a.5.5 0 0 0 .6.42l6.52-1.12a.5.5 0 0 0 .43-.6l-.01-.06a.5.5 0 0 0-.6-.43m-37.09 1.2q2-.15 3.77.17.5.09.46.6a.5.5 0 0 1-.13.27.4.4 0 0 1-.29.12q-2.11-.01-3.88-.75-.82-.34.07-.41"}),c.createElement("path",{fill:"#9DCBFE",d:"m522.24 333.19-17.46.12a.65.65 0 0 0-.65.66v.04a.65.65 0 0 0 .66.64l17.45-.12a.65.65 0 0 0 .65-.66v-.04a.65.65 0 0 0-.66-.64m-147.39 15.4q-.8 0-.82.91a89 89 0 0 0 .93 13.86q.13.9-.04 2.26-.26 2.04.82 2.25-.34 1.29-1.99 1.21a50 50 0 0 0-5.29-.05l-5.45.09q-3.86.57-7.78 1.32c-2.48.48-6.03.14-6.16-3.16q-.3-7.13-.44-13.77c-.08-3.23 4.43-3.38 6.71-3.49a100 100 0 0 0 14.34-1.76 6 6 0 0 0 2.35-1 1 1 0 0 1 .94-.13q.88.3 1.88 1.46m-13.74 13.23c.47.3 1.2.17 2.06-.4a9 9 0 0 0 2.47-2.59q.54-.82.92-1.68a8 8 0 0 0 .51-1.6q.14-.75.03-1.28a1.2 1.2 0 0 0-.47-.77c-.47-.3-1.2-.17-2.06.4a9 9 0 0 0-2.47 2.59 11 11 0 0 0-.92 1.68 8 8 0 0 0-.51 1.6q-.14.75-.03 1.28t.47.77"}),c.createElement("path",{fill:"#478FFF",d:"M375.74 367.87q-1.1-.21-.82-2.25.17-1.35.04-2.26a89 89 0 0 1-.93-13.86q0-.9.82-.91 1.32 9.45.89 19.28"}),c.createElement("path",{fill:"#FAF9F9",d:"M362.1 356.49c-1.52 2.3-1.97 4.68-1 5.33.98.64 3.01-.7 4.54-2.99 1.52-2.3 1.97-4.68 1-5.33-.98-.64-3.01.7-4.54 2.99"}),c.createElement("path",{fill:"#C3E1FB",d:"M592.52 397.02a331 331 0 0 0-.44 6.87.4.4 0 0 1-.22.33.4.4 0 0 1-.39-.06l-.23-.2a1.3 1.3 0 0 1-.38-.5 1.3 1.3 0 0 1-.09-.64q2.23-23.9-2.45-47.84a1.3 1.3 0 0 1 .39-1.18l.19-.18q.45-.4.55.18a242 242 0 0 1 2.48 15.38q1.69 13.28.59 27.84"}),c.createElement("path",{fill:"#62A1FF",d:"M488.3 356.55c2.06 0 3.73-.31 3.73-.72s-1.66-.73-3.72-.74c-2.05 0-3.72.31-3.72.72s1.66.73 3.72.74"}),c.createElement("path",{fill:"#9DCBFE",d:"M533.95 355.64q.64 1.05-1.92 1.15l-29.88-.25a.5.5 0 0 1-.5-.4 1 1 0 0 1-.01-.24l.06-.26a.5.5 0 0 1 .19-.3.5.5 0 0 1 .33-.11z"}),c.createElement("path",{fill:"#FE6D04",d:"M368.46 369.03c-.21 4.73-1.98 6.87-4.14 10.32q-.93 1.48-.86 6.14-5.29-.84-5.64-3.77c3.19-4.23 5.98-6.56 5.19-12.6z"}),c.createElement("path",{fill:"#1350CE",d:"M539.23 382.74c20.71 11.71 30.09 33.65 24.04 56.84a76 76 0 0 1-4.3 12.06.8.8 0 0 1-.73.49l-13.99.35-.95-.21q-.53-.1-.27-.59 4.14-7.42 5.52-10.96 8.16-20.82-5.85-38.19a23 23 0 0 0-4.35-4.04q-8.15-6.42-17.05-8.1c-15.6-2.95-31.26 3.15-40.25 16.58-5.89 8.8-7.71 20.84-4.25 30.51 5.9 16.5 20.75 26.66 38.31 26.58q8.86-.3 17.68-4.43l-1.35 10.89-.64 5.45q-.06.49-.56.54-8.51.86-16.99.8-18.7-.11-33.33-12.16a4 4 0 0 0-1.12-1.03q-21.43-19.57-15.82-47.86 2.55-12.85 12.37-23.42 15.52-16.7 38.4-16.63a53.5 53.5 0 0 1 25.48 6.53m-23.9-.84a1.4 1.4 0 0 0-.36-.97 1.4 1.4 0 0 0-.94-.44l-1.36-.04a1.4 1.4 0 0 0-.98.36 1.4 1.4 0 0 0-.43.95l-.04 1.14a1.4 1.4 0 0 0 .37.97 1.4 1.4 0 0 0 .94.44l1.36.04a1.4 1.4 0 0 0 .98-.36 1.4 1.4 0 0 0 .43-.95zm-10.32.87a2.6 2.6 0 0 0-.82-1.48 1.2 1.2 0 0 0-.87-.28q-.48.04-.82.4-1.54 1.72.08 3.42a1.5 1.5 0 0 0 1.06.45q1.94 0 1.37-2.51m20.16.36c0-.54-.21-1.05-.6-1.43a2 2 0 0 0-2.85 0 2 2 0 0 0 0 2.86 2 2 0 0 0 2.86 0c.38-.38.59-.9.59-1.43m-9.34 88.26a2.2 2.2 0 0 0-.63-1.53 2.15 2.15 0 0 0-3.06 0 2.16 2.16 0 1 0 3.69 1.53"}),c.createElement("path",{fill:"#E3F1FB",d:"m514.03 380.5-1.36-.05a1.36 1.36 0 0 0-1.4 1.3l-.05 1.15a1.36 1.36 0 0 0 1.31 1.4l1.36.05a1.36 1.36 0 0 0 1.4-1.3l.05-1.15a1.36 1.36 0 0 0-1.31-1.4"}),c.createElement("path",{fill:"#9DCBFE",d:"M440.86 383.86c.23-1.04.39-2.21 1.15-2.96q.67-.68.56.27-.18 1.5-1.33 2.88-.6.7-.38-.19"}),c.createElement("path",{fill:"#E3F1FB",d:"M505.01 382.77q.57 2.5-1.37 2.51a1.5 1.5 0 0 1-1.06-.45q-1.63-1.7-.08-3.42.33-.36.82-.4a1.2 1.2 0 0 1 .87.28q.6.5.82 1.48"}),c.createElement("path",{fill:"#FAF9F9",d:"M523.15 385.15a2.02 2.02 0 1 0 0-4.04 2.02 2.02 0 0 0 0 4.04"}),c.createElement("path",{fill:"#C3E1FB",d:"M357.82 381.72q.35 2.93 5.64 3.77a71 71 0 0 1-7.66 18.49c-1.57 2.69-3.53 4.75-5.16 7.37a.3.3 0 0 0-.03.3.4.4 0 0 0 .23.2l.7.19c-8.13 3.85-8.64 10.61-11.03 17.89-6.12-2.51-15.63-4.38-18.6 3.53q-.23.6.38.41a37.5 37.5 0 0 1 20.45-.17q.54-1.68 1.82-3.3-2.96 10.76.53 19.04 3.17 7.5 3.9 12.36l-32.53 11.19q-1.32.23-1.15-1.77.07-.7.46-1.87a81 81 0 0 0 4.47-27.77q1.6-2 .53-4.84-1.5-3.9-1.5-6.74a55 55 0 0 1 .83-9.72c.85-4.74 1.07-7.96 6.01-8.88q2.8-.52 5.78-1.57l2.43-.1c1.24-.37 2.43-.51 3.4-1.44 1.42-1.36 2.72-2.54 3.75-4.27a405 405 0 0 1 9.77-15.8q2.86-4.42 6.58-6.5"}),c.createElement("path",{fill:"#9DCBFE",d:"M438.14 421.81a118 118 0 0 0 6.04 17.44.3.3 0 0 1-.17.4l-.29.12q-.45.18-.63-.27a318 318 0 0 1-4-10.39q-5.07-13.95-3.69-27.62a46 46 0 0 1 3.61-13.25.46.46 0 0 1 .6-.24l.24.1q.25.11.14.36a38 38 0 0 0-3.78 15.99c-.02 5.52.54 12.01 1.93 17.36"}),c.createElement("path",{fill:"#1350CE",d:"M335.25 392.24q-.82 6.78-6.28 10.06c-1.22.73-2.63 1.02-3.91 1.53a1.78 1.78 0 0 0-.8 2.65q.3.45.81.65l6.82 2.7q-3 1.05-5.78 1.57c-4.94.92-5.16 4.14-6.01 8.88a55 55 0 0 0-.83 9.72q0 2.83 1.5 6.74 1.08 2.85-.53 4.84a136 136 0 0 1-6.9-7.67c-2.09-2.52-.34-4.39.53-6.77a1.1 1.1 0 0 0-.12-1.01q-2.51-3.9-2.83-7.15a670 670 0 0 1-1.55-19.33c-.2-2.97-.56-8.94 4.04-8.28.79.11 1.61.67 2.32.72q.51.02.93-.25a19 19 0 0 1 4.58-2.33q9.25-2.9 14.01 2.73"}),c.createElement("path",{fill:"#62A1FF",d:"M538.35 398.49a1552 1552 0 0 1-23.24 65.57c-17.56.08-32.41-10.08-38.31-26.58-3.46-9.67-1.64-21.71 4.25-30.51 8.99-13.43 24.65-19.53 40.25-16.58q8.9 1.68 17.05 8.1"}),c.createElement("path",{fill:"#FE6D04",d:"M335.25 392.24a27.5 27.5 0 0 1 4.78 10.47q.3 1.37-.42 2.15a45 45 0 0 1-5.29 4.87l-2.43.1-6.82-2.7a1.8 1.8 0 0 1-1.12-1.65 1.8 1.8 0 0 1 1.11-1.65c1.28-.51 2.69-.8 3.91-1.53q5.46-3.28 6.28-10.06m38.54 15.86q-3.24-2.67-1.7-6.05c2.92-1.88 7.15-4.15 10.61-4.17q5.18-.04.89 2.87a51 51 0 0 0-8.09 6.74q-.51.54-1.71.61"}),c.createElement("path",{fill:"#9DCBFE",d:"M538.35 398.49a23 23 0 0 1 4.35 4.04q14.01 17.37 5.85 38.19-1.38 3.52-5.52 10.96-.25.48.27.59l.95.21-10.38 1.29a.8.8 0 0 0-.69.56q-.37 1.05-.16 2.39.2.8-.02 1.57l-.21 1.34a45 45 0 0 1-17.68 4.43 1552 1552 0 0 0 23.24-65.57"}),c.createElement("path",{fill:"#B4D9FD",d:"M372.09 402.05q-1.54 3.38 1.7 6.05c-.46 4.51-8.33 9.24-11.94 12.19-2.12 1.74-4.6 2.88-7 4.26l-4.95 3.24a22 22 0 0 1-5.34 2.61 11 11 0 0 0-1.82 3.3 8 8 0 0 0-2.23-3.77c2.39-7.28 2.9-14.04 11.03-17.89a93 93 0 0 0 6.07-3.68 45 45 0 0 1 14.48-6.31"}),c.createElement("path",{fill:"#1350CE",d:"M686.83 439.95a4.6 4.6 0 0 1-2.27-1.61q-1.04-1.49-2.76-.9l-7.8 2.62q-6.6-3.33-6.41-9.96c.09-3.31 3.32-4.47 3.32-7.73a1.2 1.2 0 0 0-1.08-1.2 1 1 0 0 0-.58.06q-1.35.52-2.63.52a1.2 1.2 0 0 1-.97-.42l-8.38-9.22q.26-3.5 2.78-4.11c11.78-2.85 16.56 6.68 19.55 15.99.26.83 1.07 2.53 1.6 3.22 3.17 4.09 5.82 7.72 5.63 12.74"}),c.createElement("path",{fill:"#FDCB7D",d:"m657.27 412.11 8.38 9.22q.39.44.97.42 1.27 0 2.63-.52a1.23 1.23 0 0 1 1.52.57q.15.27.14.57c0 3.26-3.23 4.42-3.32 7.73q-.2 6.63 6.41 9.96a16.5 16.5 0 0 1-10.33 3.33q-5.91 0-6.17-5.85l1.3-2.87a2.2 2.2 0 0 0-.45-2.49 7 7 0 0 1-1.83-2.85 27.3 27.3 0 0 1 .75-17.22"}),c.createElement("path",{fill:"#9DCBFE",d:"M340.51 429.93a8 8 0 0 1 2.23 3.77 37.5 37.5 0 0 0-20.45.17q-.6.18-.38-.41c2.97-7.91 12.48-6.04 18.6-3.53"}),c.createElement("path",{fill:"#C3E1FB",d:"M686.83 439.95q.87 3.2.87 7.05a399 399 0 0 1-.38 17.97q-.1 2.3 1.78 6.7a99 99 0 0 0 6.08 11.65q1.01 1.7-.14 3.21a139 139 0 0 1-36.52 4.15 1 1 0 0 0-.83.39l-.67.85.69-18.9q.07-1.68 1.67-1.17a6 6 0 0 0 1.71.27q.8 0 .79-.8v-3.46q0-.87-.87-.93l-4.24-.29-3.71-.83-3.87-23.03 8.31-5.24q.26 5.84 6.17 5.85a16.5 16.5 0 0 0 10.33-3.33l7.8-2.62q1.73-.58 2.76.9.73 1.05 2.27 1.61m-10.39 26.12a6 6 0 0 0-1.37.29q-.72.25.04.4 2.12.38 3.99.19.8-.09.94-.88.9-5.07.81-10.17a.4.4 0 0 0-.13-.3.4.4 0 0 0-.31-.13h-.28q-.65 0-.76.65l-1.35 8.49a1.75 1.75 0 0 1-1.58 1.46m9.92 1.33c-2.14 6.81-6.75 7.82-12.79 9.68q-.44.13-.42.59 0 .23.17.41.27.32.7.34c7.21.43 14.67-1.69 12.81-10.99q-.19-.93-.47-.03"}),c.createElement("path",{fill:"#1350CE",d:"m649.19 442.78 3.87 23.03q-.62.6-1.93.5-.52-.05-.5.48.03.73.69 1.15a90 90 0 0 0 2.43 1.51l1.51 2.6a.7.7 0 0 1 0 .71.7.7 0 0 1-.61.38q-13.11.24-25.41.16c-4.62-.04-6.01-6.76-6.72-10.32q-1.65-8.25-3.45-16.77a25 25 0 0 1-.5-3.82q-.04-.67.63-.67 14.06.05 27.3.19 1.33 0 2.69.87"}),c.createElement("path",{fill:"#62A1FF",d:"M788.42 457.33c.96 2.1 6.14 3.44 8.22 3.62 3.03.26 4.97-3.95 8.2-2.09 4.27 2.45 8.13 6.7 10.86 10.63 2.12 3.04-1.91 6.36-3.82 8.46a1.9 1.9 0 0 0-.45 1.63c.46 2.25 1.52 6.81 4.53 7.09 3.13.29 7.2-.57 7.92 3.58q1.3 7.5.32 14.93c-.63 4.77-8.75 1.96-10.58 5.27a16 16 0 0 0-1.75 4.67c-.96 4.82 7.27 6.46 3.92 11.56a47 47 0 0 1-8.47 9.63c-1.97 1.73-4.8 3.3-7.04.97-2.88-2.98-7.33-1.42-10.46.11-3.87 1.88.42 8.79-5.08 9.82-4.26.81-11.61 1.65-15.74-.47-3.84-1.96 0-7.43-4.19-9.68a13 13 0 0 0-5.69-1.55c-3.41-.11-5.77 4.32-9.31 2.08a31 31 0 0 1-10.65-11.37c-3.06-5.6 8.45-7.18 3.02-15.07-.94-1.38-1.98-2.52-3.8-2.54-3.68-.02-7.16-.1-7.42-4.79q-.35-6.39.11-11.31c.54-5.9 3.48-5.37 8.2-5.46 3.11-.07 5.99-7.18 3.71-9.31-3-2.8-5.79-5.43-2.41-9.59a71 71 0 0 1 7.77-8.11c1.96-1.75 4.4-1.96 6.5-.44 3.44 2.49 6.2 1.59 10.02-.35 2.32-1.18 2.21-4.55 2.5-6.8.38-2.96 4.05-3.39 6.47-3.5a57 57 0 0 1 11.05.43c1.56.24 2.43 1.65 2.51 3.16q.15 2.83 1.03 4.79m-6.48 57.2a16.6 16.6 0 0 0 10.17-7.85 17.2 17.2 0 0 0 1.66-12.9 17 17 0 0 0-2.9-6 17 17 0 0 0-4.92-4.41 17 17 0 0 0-6.2-2.19 17 17 0 0 0-6.52.39 16.6 16.6 0 0 0-10.18 7.85 17.2 17.2 0 0 0-1.66 12.9 17 17 0 0 0 2.9 6 17 17 0 0 0 4.92 4.41 17 17 0 0 0 6.2 2.19c2.18.3 4.4.18 6.52-.39"}),c.createElement("path",{fill:"#9DCBFE",d:"M449.36 454.92a151 151 0 0 0 6.35 13.73q.22.42-.25.45l-.56.03q-.18 0-.26-.14a121 121 0 0 1-7.28-16.43 4 4 0 0 1-.19-2.19q.22-1.09.66-.08c.67 1.52.92 3.13 1.53 4.63m112.93-.4a3.3 3.3 0 0 1-1.53-.75q-.36-.3.11-.37.72-.1 1.42 1.12"}),c.createElement("path",{fill:"#62A1FF",d:"M562.29 454.52a126 126 0 0 1 14.88 12.34.5.5 0 0 1 .15.32q.03.3-.19.1z"}),c.createElement("path",{fill:"#FAF9F9",d:"M565.08 492.41a118 118 0 0 1-31.81-27.96 1.4 1.4 0 0 1-.31-.89l.04-5.27q.22-.75.02-1.57l.63-1.51a1 1 0 0 1 .55-.51q.2-.06.39-.04a1 1 0 0 1 .36.12 34 34 0 0 1 6.06 4.55 490 490 0 0 0 21.3 18.93 7.8 7.8 0 0 0 5.31 1.82q4.93-.14 9.6-.18 1.8 0 .46 1.2z"}),c.createElement("path",{fill:"#9DCBFE",d:"M676.44 466.07a1.75 1.75 0 0 0 1.58-1.46l1.35-8.49q.11-.64.76-.65h.28q.19 0 .3.13.14.13.14.3.09 5.1-.81 10.17-.14.8-.94.88-1.87.19-3.99-.19-.76-.15-.04-.4a6 6 0 0 1 1.37-.29"}),c.createElement("path",{fill:"#E3F1FB",d:"m533 458.29-.04 5.27q0 .5.31.89a118 118 0 0 0 31.81 27.96l-3.55 4.32-2.92 2.59q-.68.07-1.26.55a.7.7 0 0 1-.81.08A76 76 0 0 1 532 480.23a1.4 1.4 0 0 1-.33-.89l-.23-8.82 1.35-10.89z"}),c.createElement("path",{fill:"#2466F8",d:"M348.99 461.8q-1.2 2.2.19 4.99.67 1.33 1.72 1.69a86 86 0 0 0 4.42 20.51q-.8-.03-.51 1.75.95 5.94 1.25 11.94.28 5.49-.83 11.91a390 390 0 0 1-7.2 33l-7.54.29a25 25 0 0 1-1.34-8.13q-.15-8-.14-16.29.02-9.55-1.3-14.11a55 55 0 0 0-5.79-13.53c-2.73-4.41-6.85-8.4-10.52-12.23q-3.75-3.92-4.94-10.6z"}),c.createElement("path",{fill:"#E3F1FB",d:"M478.8 464.12q.8.56 1.12 1.03c8.5 12.59 7.92 24.37 5.89 38.65q-.09.6-.69.51l-5.52-.83a1 1 0 0 1-.81-.67 28 28 0 0 1-1.69-8.79q-.22-9.78-.32-19.23-.07-6.48 2.02-10.67"}),c.createElement("path",{fill:"#FDCB7D",d:"m653.06 465.81 3.71.83q1.35.43 3.25.19.34-.04.64.07a1.4 1.4 0 0 1 .87 1.58l-.54 2.43a1 1 0 0 1-.57.69 1 1 0 0 1-.89-.06 15 15 0 0 0-5.78-2.09 90 90 0 0 1-2.43-1.51 1.4 1.4 0 0 1-.69-1.15q-.02-.52.5-.48 1.31.1 1.93-.5"}),c.createElement("path",{fill:"#9DCBFE",d:"M673.57 477.08c6.04-1.86 10.65-2.87 12.79-9.68q.28-.9.47.03c1.86 9.3-5.6 11.42-12.81 10.99a1 1 0 0 1-.7-.34.7.7 0 0 1-.17-.41q-.02-.45.42-.59"}),c.createElement("path",{fill:"#1350CE",d:"M350.9 468.48a236 236 0 0 1 24.45 7.23c1.53.53 2.22 1.08 2.55 2.78a289 289 0 0 1 4.5 33.39q-4.54.34-7.06 2.02-.96.1-1.14-.64c-2.02-8.55-4.49-16.9-5.69-25.44q-.1-.75-.86-.64l-12.33 1.81a86 86 0 0 1-4.42-20.51"}),c.createElement("path",{fill:"#FAF9F9",d:"M513.67 473.55a2.16 2.16 0 1 0 0-4.32 2.16 2.16 0 0 0 0 4.32m247.72 28.78c2.4 9.1 11.6 14.57 20.54 12.2 8.95-2.36 14.25-11.65 11.84-20.76-2.4-9.1-11.6-14.57-20.54-12.2-8.95 2.36-14.25 11.66-11.84 20.76"}),c.createElement("path",{fill:"#B4D9FD",d:"M467.08 485.99c.92.79 1.78 1.3 1.92 1.13.14-.16-.5-.94-1.42-1.73s-1.78-1.3-1.92-1.13.5.94 1.42 1.73"}),c.createElement("path",{fill:"#2466F8",d:"M695.04 486.53c.79 13.23-2.99 27.33-5.57 39.72q-.92 4.46.52 12.01 1.4 7.3 7.22 27.29.2.7.47 3.2a15 15 0 0 0-5.54 2.01q-1.23.6-1.9-.76a437 437 0 0 1-11.02-24.72c-3.73-8.99-5.68-18.13-7.91-27.51q-.56-2.37-1.69-.21-5.36 10.29-11.49 21.84a37 37 0 0 0-1.93 4.25q-.32.88.33 1.56l37.88 39.19-3.1 2.04q-1.12.07-3.32 1.33a8 8 0 0 1-4.23.8c-4.9-.24-9.76 2.14-13.27 5.42q-3.1 2.9-4.9 8.2a253 253 0 0 0-4.01 12.93q-4.66 16.54-11.92 39.55a.5.5 0 0 1-.41.35 1 1 0 0 1-.27-.01q-.45-.13-.46-.6a916 916 0 0 0-.38-18.96c-.33-11.18 2.46-21.31 5.45-31.92q.15-.52.59-.85c4.3-3.22 9.34-4.61 6.58-11.65q-2.6-6.6-8.46-13.98c-.88-1.1-2.38-1.63-2.86-2.7a1.1 1.1 0 0 1 .09-1.17l2.9-4.32a1.4 1.4 0 0 0 .2-1.07 1.4 1.4 0 0 0-.62-.89l-3.25-2.07a1.5 1.5 0 0 1-.64-.8 78 78 0 0 1-4.26-17.75c-.84-7.62-3.21-11.68-10.66-14.13a1.5 1.5 0 0 1-.86-.71 51 51 0 0 1-6.25-19.58 726 726 0 0 1 15.89 16.17 26 26 0 0 1 2.9 3.65.5.5 0 0 0 .72.1 1 1 0 0 0 .15-.21 331 331 0 0 0 11.27-39.65l.67-.85a1 1 0 0 1 .83-.39 139 139 0 0 0 36.52-4.15"}),c.createElement("path",{fill:"#FAF9F9",d:"m375.34 513.9 3.67 17.35q1.13-1.36 1.26 1.08a119 119 0 0 0-9.87 9.3 1201 1201 0 0 1-18.2 18.79 6.4 6.4 0 0 1-4.8 2.03 1 1 0 0 1-.53-.14 1.3 1.3 0 0 1-.68-.82 1 1 0 0 1-.04-.54l1.88-13.36a390 390 0 0 0 7.2-33q1.11-6.42.83-11.91-.3-6-1.25-11.94-.3-1.78.51-1.75l12.33-1.81q.75-.1.86.64c1.2 8.54 3.67 16.89 5.69 25.44q.18.75 1.14.64"}),c.createElement("path",{fill:"#2466F8",d:"M556.63 507.34q-1.2 3.41-2.93 4.96-2.7.79-7.21.98c-7.78.33-12.39-4.29-18.08-8.63a20 20 0 0 1-3.21-3.44 6 6 0 0 1-1.27-4.3q.23-2.2 1.26-4.89-.04-.34.99-1.6l3.33-3.16q.27-.25.39.09a8 8 0 0 0 2.32 3.42 128 128 0 0 0 14.86 10.51c.9.55 1.68 1.27 1.77 2.39q.03.32.2.6a1.4 1.4 0 0 0 1.03.69q.32.04.63-.06a15 15 0 0 0 4.58-2.5.8.8 0 0 1 .71-.12.8.8 0 0 1 .5.53l.55 2.11q.13.51-.19.92-.93 1.19-.23 1.5"}),c.createElement("path",{fill:"#62A1FF",d:"M526.18 490.42q-1.03 1.26-.99 1.6l-31.36.08q-.45 0-.5-.46-.03-.4.4-.42 11.7-.4 27.77-.47 2.31 0 4.68-.33m37.77 13.67a14 14 0 0 1-1.46-4.31 1.1 1.1 0 0 0-.77-.9 1.1 1.1 0 0 0-1.15.29q-1.4 1.46-1.96.15l2.92-2.59q3.44.32 2.42 7.36"}),c.createElement("path",{fill:"#2466F8",d:"m242.93 512.83-7.52-.17a.8.8 0 0 1-.52-.22.8.8 0 0 1-.22-.52l-.11-4.09a.76.76 0 0 1 .71-.78l7.5-.45a.76.76 0 0 0 .72-.74l.18-7.3a.76.76 0 0 1 .76-.74l4.7-.02a.76.76 0 0 1 .76.75l.08 7.23a.76.76 0 0 0 .75.75l7.23.08a.76.76 0 0 1 .75.76l-.02 4.72a.76.76 0 0 1-.74.76l-7.3.16a.76.76 0 0 0-.74.72l-.45 7.49a.76.76 0 0 1-.77.71l-4.1-.1a.8.8 0 0 1-.52-.22.8.8 0 0 1-.22-.52l-.16-7.51a.76.76 0 0 0-.75-.75"}),c.createElement("path",{fill:"#1350CE",d:"m615.88 501.55 10.21 10.31a51 51 0 0 0 6.25 19.58q.3.52.86.71c7.45 2.45 9.82 6.51 10.66 14.13a78 78 0 0 0 4.26 17.75q.18.5.64.8l3.25 2.07a1.4 1.4 0 0 1 .63 1.45 1.4 1.4 0 0 1-.21.51l-2.9 4.32a1.1 1.1 0 0 0-.09 1.17c.48 1.07 1.98 1.6 2.86 2.7q5.87 7.38 8.46 13.98c2.76 7.04-2.28 8.43-6.58 11.65a1.6 1.6 0 0 0-.59.85c-2.99 10.61-5.78 20.74-5.45 31.92q.3 9.56.38 18.96 0 .46.46.6.13.03.27.01a.5.5 0 0 0 .41-.35q7.26-23 11.92-39.55a253 253 0 0 1 4.01-12.93q1.8-5.3 4.9-8.2c3.51-3.28 8.37-5.66 13.27-5.42a8 8 0 0 0 4.23-.8q2.2-1.26 3.32-1.33a42 42 0 0 1-5.76 3.16q-2.64 1.16.21 1.58.6.09 2.24-.11 5.8-.67 11.49-1.96a148 148 0 0 1 18.96 20.21q3 3.88 5.46 8.57-1.4 3.87-2.59 5.41c-1.86 2.4-4.14 3.18-6.61 4.31a1.5 1.5 0 0 0-.89 1.43c.15 5.65-.58 11.38.99 16.89 1.33 4.72 3.61 9.38 5.73 13.9 3.57 7.62 3.2 14.7-3.9 20.36-2.87 2.29-7.72 3.72-11.47 4.91a3.8 3.8 0 0 0-2.49 2.49c-7.95 24.94-16.01 52.66-23.86 78.72a56 56 0 0 0-1.33 5.28q-.38 1.9 1.38 6.55A498 498 0 0 0 685 793.5q1.66 3.97 1.78 9.2l-2.23-1.68-11.64-8.89q.1-.3-.56-.55a.6.6 0 0 0-.62.16 1 1 0 0 0-.11.2l-46.54 152.4a236 236 0 0 0-7.84 25.67l-9.51.21 6.99-24.19q.4-1.42-1.06-1.42l-42.41.15q-.5 0-.85.36a3 3 0 0 0-.77 2.19l-6.89 22.91-9.19.07-.14-.26a.7.7 0 0 1-.05-.54l2.28-7.57 4.26-15.65 9.33-29.9 12.87-42.25 5.82-18.91 1.31-4.55 31.26-100a1.4 1.4 0 0 0-.47-1.56l-33.67-27.21-.16-10.31 11.59.09q4.83.03 7.22-.75c4.2-1.36 8.02-4.54 9.61-8.78q1.46-3.9 1.49-5.93.08-3.98.04-7.79-.04-3.62.56-7.52.87-5.7.56-11.85a.8.8 0 0 0-.48-.75q-.55-.28-1.41.07.15-3.3 1.66-3.48 2.7-5.64-1.57-11.19-.2-2.18-.18-5.95 0-4.73.11-9.58c.06-2.58.82-4.91 2.1-7.12q6.22-10.75 12.76-19.72c4.33-5.95 7.82-11.4 8.67-18.87q.05-.45-.37-.6-.33-.13-.42-.29l.1-.99q3.74 1.66 7.68 1.18a2.3 2.3 0 0 0 2.01-2.45q-.45-5.79 1.25-11.77a2.3 2.3 0 0 0-.85-2.47 2 2 0 0 0-.83-.39l-4.38-1.03a.8.8 0 0 0-.7.16.8.8 0 0 0-.3.67q.2 4.05.49 7.79.3 3.68-3.22 4.31-1.66.51-3.09-1.38c-4.71-6.28-8.89-12.38-11.73-19.77a115 115 0 0 0-4.35-9.93q-1.69-3.4-3.44-3.82a16.6 16.6 0 0 1-11.42-8.9l-3.69-6.35-.13-2.26 2.36-2.39a.7.7 0 0 0 .17-.66.7.7 0 0 0-.49-.48c-3.09-.76-2.67-3-2.68-5.49a.75.75 0 0 0-1.26-.54 4.5 4.5 0 0 0-1.23 1.99q-.39 1.34-1.77 1.29a14 14 0 0 1-5.89-1.2c-1.77-2.8-1.93-7.04 1.73-8.4 9.79-3.61 14.82.82 16.11 10.55q.2 1.46 1.65 1.26l4.31-.6q1.5-.2 1.48-1.71zm20.05 177.15c-2.03 3.04-.8 5.72-1.79 8.71q-.15.48-.59.74-2.18 1.35-4.39 2.52-1.05.56-.79 2.01a.7.7 0 0 0 .79.59c2.45-.28 5.31.08 7.44-1.42a22 22 0 0 1 5.25-2.69 2.27 2.27 0 0 0 1.46-2.69 13 13 0 0 0-2.16-4.47c-.06-3.15-1.79-6.4-5.22-3.3m40.6 62.36q.87-2.54-1.05-.66c-3.28 3.19-7.08 4.07-11.21 5.63a.8.8 0 0 0-.45 1.03c1.8 4.25 3.19 9.56 5.55 13.82a.5.5 0 0 0 .47.27.5.5 0 0 0 .46-.29q.45-.9.76-1.99 2.7-9.72 5.47-17.81m-27.54 29.91q6.56.45 12.8-.07a1.02 1.02 0 0 0 .8-1.53c-1-1.76-2.03-3.54-2.71-5.44a127 127 0 0 0-7.92-17.79 12 12 0 0 0-2.31-2.98.6.6 0 0 0-.7-.12.6.6 0 0 0-.38.58q-.1 9.7.13 20.15a43 43 0 0 1-.35 6.42.7.7 0 0 0 .15.53.7.7 0 0 0 .49.25m-19.51-14.15a.6.6 0 0 0-.76.04 1 1 0 0 0-.15.23l-4.49 13.13a1 1 0 0 0-.02.27.6.6 0 0 0 .3.43 1 1 0 0 0 .27.06l21.05-.12a.58.58 0 0 0 .36-1.04zm33.73 25.46a11 11 0 0 1-3.33-1.79c-.87-.68-1.87-.62-2.91-.61q-17.13.25-34.72.09a1.2 1.2 0 0 0-1.12.81l-10.02 31.7a.64.64 0 0 0 .33.77q.14.07.29.07h42.02a1.84 1.84 0 0 0 1.76-1.3l8.64-27.93q.42-1.36-.94-1.81m-21.86 73.83a.7.7 0 0 0 .66-.49l10.06-32.05a.7.7 0 0 0-.67-.91l-42.9-.07a.7.7 0 0 0-.66.49l-10.06 32.06a.7.7 0 0 0 .1.62.7.7 0 0 0 .56.29zM589.65 882l-4.3 13.85a.9.9 0 0 0 .13.77.9.9 0 0 0 .69.36l4.9.08 37.41.01a1.2 1.2 0 0 0 .73-.24q.32-.24.44-.62l9.31-30.25a.8.8 0 0 0-.12-.7.8.8 0 0 0-.63-.32l-42.37-.08a1.15 1.15 0 0 0-1.1.81zm19.21 53.85 7.74-.07a1.4 1.4 0 0 0 1.35-1l8.63-27.93a.8.8 0 0 0-.4-.93 1 1 0 0 0-.35-.08l-29.57.11-12.83-.1q-1.28 0-1.64 1.2l-8.59 27.77a.7.7 0 0 0 .11.65.7.7 0 0 0 .58.29z"}),c.createElement("path",{fill:"#2466F8",d:"m410.17 531.21-.35-1.79q-.21-1.05-.36.01-.12.85-.88 1.22a1.3 1.3 0 0 0-.52.45c-4.05 6.33-8.25 12.55-11.68 19.28a2747 2747 0 0 0-9.23 18.27c-3.46 6.92-7.05 14.95-9.08 21.62q-1.37 4.5-1.37 8.11.03 84.17-.11 168.34 0 1.02.75 3.31a10600 10600 0 0 0-49.71 40.86q-5.4 4.5-10.31 7.19l-6.63 1.05a15.6 15.6 0 0 1-15.54-13.26q-.2-1.32-.2-8.75.18-87.1-.05-164.12c-.01-5.61 4.5-12.2 8.01-17.3a203 203 0 0 1 12.75-16.77 363 363 0 0 1 30.77-31.86 861 861 0 0 1 11.07-5.8q.3-.15.24-.42-.1-.46-.55-.36a10 10 0 0 1-4.99-.07 1201 1201 0 0 0 18.2-18.79q4.71-4.96 9.87-9.3l12.06-10.22q.38-.31.49-.81l.56-2.58L410.01 502z"}),c.createElement("path",{fill:"#B4D9FD",d:"M553.7 512.3q.12 5.97 1.63 12.13c1.5 6.09 5.39 9.5 10.17 13.47a18 18 0 0 0 5.3 3.04q7.08 2.05 14.22 4.26c-6.19-.01-11.42.9-14.17 6.9a364 364 0 0 1-5 10.43.9.9 0 0 1-.82.53l-8 .3-33.24-.05 5.35-.42q.5-.04.57-.52a154 154 0 0 0-1.3-57.72c5.69 4.34 10.3 8.96 18.08 8.63q4.52-.19 7.21-.98"}),c.createElement("path",{fill:"#FDCB7D",d:"M566.72 508.56a25 25 0 0 1 2.4 8.22q-2.7-.45-3.67.43a.96.96 0 0 1-1.59-.53 18 18 0 0 1 .46-8.88q.15-.55.73-.46.95.15 1.67 1.22"}),c.createElement("path",{fill:"#FAF9F9",d:"M561.15 514.74c.08 2.06.15 6.17 1.84 7.56a26 26 0 0 1 4.19 4.38 12 12 0 0 0 6.2 4.17q6.38 2.07 12.8 3.99a16 16 0 0 0 4.9.66q2.53 4.68 7.58 2.82c1.19-.44 1.81-1.37 3-1.89l3.69 6.35q-1.1 3.64-5.37 3.89-3.2.19-8.87-.58l-6.09-.89q-7.14-2.21-14.22-4.26a18 18 0 0 1-5.3-3.04c-4.78-3.97-8.67-7.38-10.17-13.47q-1.5-6.15-1.63-12.13 1.73-1.55 2.93-4.96a149 149 0 0 0 3.06 3.66q.25.3.56.5a.8.8 0 0 1 .39.64q.08.89-.16.03a1 1 0 0 0-.19-.32q-.76-.74-.36.26.5 1.2 1.22 2.63"}),c.createElement("path",{fill:"#FE6D04",d:"m382.4 511.88-.3 10.77a1.4 1.4 0 0 0 1 1.4q.3.1.6.05l3.02-.38q2.18.22-.41 2.17-1.07.8-1.58 1.23a47 47 0 0 1-5.72 4.13l-3.67-17.35q2.52-1.67 7.06-2.02"}),c.createElement("path",{fill:"#FAF9F9",d:"M692.14 570.76a6 6 0 0 0 .59 3.78q1.64 3.39 2.48 5.05.72 1.43.76 2.45.07 2.22-1.56 2.36l-37.88-39.19a1.4 1.4 0 0 1-.33-1.56q.75-2.01 1.93-4.25 6.14-11.55 11.49-21.84 1.13-2.16 1.69.21c2.23 9.38 4.18 18.52 7.91 27.51q6 14.46 11.02 24.72.67 1.37 1.9.76"}),c.createElement("path",{fill:"#FDCB7D",d:"m393.38 518.72-.56 2.58a1.4 1.4 0 0 1-.49.81l-12.06 10.22q-.14-2.45-1.26-1.08a47 47 0 0 0 5.72-4.13q.51-.43 1.58-1.23 2.59-1.95.41-2.17 3.46-1.35 6.66-5"}),c.createElement("path",{fill:"#E3F1FB",d:"M601.53 534.17a5.5 5.5 0 0 1-.94-3.59 1.2 1.2 0 0 0-.7-1.12 1 1 0 0 0-.46-.1l-9.07-.05q-.2-.83.38-2.62a14 14 0 0 0 5.89 1.2q1.38.04 1.77-1.29a4.5 4.5 0 0 1 1.23-1.99.8.8 0 0 1 .8-.14.8.8 0 0 1 .46.68c.01 2.49-.41 4.73 2.68 5.49a.7.7 0 0 1 .5.48.7.7 0 0 1-.18.66z"}),c.createElement("path",{fill:"#FDCB7D",d:"m601.53 534.17.13 2.26c-1.19.52-1.81 1.45-3 1.89q-5.06 1.86-7.58-2.82l-.58-2.82-.14-3.37 9.07.05a1.2 1.2 0 0 1 .84.36 1.2 1.2 0 0 1 .32.86 5.5 5.5 0 0 0 .94 3.59"}),c.createElement("path",{fill:"#1350CE",d:"M410.17 531.21v30.62q0 1.59 1.59 1.57l23.53-.3 88.5.21 33.24.05 41.28.14q.46 0 .8.33.32.33.33.8 0 .94-.92 1a91 91 0 0 1-7.02.19q-15.07-.13-30.4.14l-26.9-.02-53.76.12h-.22l-22.64.01-46.71-.11a.66.66 0 0 0-.67.67l-.08 97.69a.76.76 0 0 0 .76.76l59.65-.06 7.01.09 17.61-.07q.83 0 .82-.83a2650 2650 0 0 1-.09-44.28q.05-8.74 7.14-14.04c1.61-1.2 3.8-1.79 5.75-2.52a1.5 1.5 0 0 0 .97-1.41l-.09-21.05q0-.9.91-.93l4.23-.09q.96-.02.96.95l-.02 20.84a28 28 0 0 0-1.41 9.57q.17 29.92-.11 98-.01 5.47.3 23.56c.07 3.86-.49 7.53-1.13 11.22a1.2 1.2 0 0 0 .21.93c3.01 4.29 3.53 13.65 2.51 18.69-.64 3.18-1.25 6.28-1.22 9.56q.06 6.4-.27 13.14-.03.52-.55.57l-2.64.23c-8.3-1.06-15.41-7.42-15.38-16.2q.05-18.97.07-38.36a1 1 0 0 0-.6-.89 1 1 0 0 0-.36-.07q-17.77.13-35.9.06-.46 0-1.54.4l-17.75-.25a1.05 1.05 0 0 1-1.03-1.06l.13-9.33a.45.45 0 0 0-.74-.36l-60.98 48.94a12 12 0 0 1-.75-3.31q.14-84.17.11-168.34 0-3.6 1.37-8.11c2.03-6.67 5.62-14.7 9.08-21.62a2747 2747 0 0 1 9.23-18.27c3.43-6.73 7.63-12.95 11.68-19.28q.2-.3.52-.45.75-.37.88-1.22.15-1.07.36-.01zm84.96 136.86h-84.55a.54.54 0 0 0-.54.56 386 386 0 0 1 .08 21.09c-.23 9.6 1.49 16.96 10.43 21.04q1.74.8 3.63.82 35.65.5 71.24.07a.6.6 0 0 0 .42-.18.6.6 0 0 0 .18-.43l-.08-42.16a.8.8 0 0 0-.81-.81"}),c.createElement("path",{fill:"#9DCBFE",d:"m585.02 545.2 6.09.89a77 77 0 0 0 4.47 6.32c2.25 2.87 3.26 5.7 3.88 9.18a235 235 0 0 0 6.53 27.09 1.8 1.8 0 0 0 1.32 1.22l9.5 2.03-5.68-.42-4-1.18a99 99 0 0 0-22.61 15.19c-2.62 2.36-4.41 5.47-2.12 8.07q7.89 8.97 15.64 17.98c1 1.5 2.33 2.65 2.74 4.43.54 2.37.23 4.89.58 7.37a176 176 0 0 0 4.08 20.6.65.65 0 0 1-.33.74 1 1 0 0 1-.28.06l-40.04.25 10.95-.91q.14 0 .18-.15a1 1 0 0 0-.01-.35.6.6 0 0 0-.35-.4c-5.46-2.44-5.43-8.36-5.5-13.63q-.18-12.82-.52-25.69-.07-2.36-1.75-8.31c-1.38-4.89-.34-10.24-.5-15.3q-.4-13.2-.21-29.48.05-4.38-5.98-4.84 15.33-.27 30.4-.14a91 91 0 0 0 7.02-.19q.92-.06.92-1a1.2 1.2 0 0 0-.33-.8 1.1 1.1 0 0 0-.8-.33l-41.28-.14 8-.3a.9.9 0 0 0 .82-.53 364 364 0 0 0 5-10.43c2.75-6 7.98-6.91 14.17-6.9"}),c.createElement("path",{fill:"#C3E1FB",d:"M605.35 542.78a16.6 16.6 0 0 0 11.42 8.9q1.75.42 3.44 3.82a115 115 0 0 1 4.35 9.93c2.84 7.39 7.02 13.49 11.73 19.77q1.43 1.89 3.09 1.38-1.4 2.23-1.15 4l-.1.99a4.5 4.5 0 0 1-3.07 1.5 91 91 0 0 1-18.25-1.14l-9.5-2.03a1.8 1.8 0 0 1-1.32-1.22 235 235 0 0 1-6.53-27.09c-.62-3.48-1.63-6.31-3.88-9.18a77 77 0 0 1-4.47-6.32q5.67.76 8.87.58 4.28-.25 5.37-3.89"}),c.createElement("path",{fill:"#FDCB7D",d:"m348.03 547.59-1.88 13.36q-.03.28.04.54a1.3 1.3 0 0 0 .68.82q.26.13.53.14a6.4 6.4 0 0 0 4.8-2.03q2.27.65 4.99.07.45-.1.55.36.05.27-.24.42a861 861 0 0 0-11.07 5.8l-3.82.64a1.7 1.7 0 0 1-1.36-.39 1.7 1.7 0 0 1-.57-1.3c.05-1.81.93-2.89.95-4.74a70 70 0 0 0-1.14-13.4z"}),c.createElement("path",{fill:"#E3F1FB",d:"M457.58 566.07q.33 2.87.17 6.08a137 137 0 0 0-.2 6.05q-.14 11.96.01 24.48c.07 5.72 1.84 10.99 8.6 10.94 9.15-.07 8.99-8.7 9.12-15.86q.27-15.17-.4-30.47-.02-.48.45-.54l4.89-.69h.22l6.95.33q.5.02.69.49c6.62 16.99 7.97 33.48 7.46 51.47-.42 14.93.04 30.1-.13 44.95a1.3 1.3 0 0 1-.37.9 1.3 1.3 0 0 1-.89.4q-9.15.4-18.31-.19a.8.8 0 0 1-.54-.25.8.8 0 0 1-.22-.55l-.18-28.92q.21-7.32.14-15.36 0-1.25-1.45-1.68a1 1 0 0 0-.57.02q-1.05.35-1.07 1.46-.13 18.42-.08 36.1a36 36 0 0 1-1.34 9.79l-59.65.06a.8.8 0 0 1-.54-.22.8.8 0 0 1-.22-.54l.08-97.69a.7.7 0 0 1 .41-.62 1 1 0 0 1 .26-.05z"}),c.createElement("path",{fill:"#C3E1FB",d:"m480.22 566.06-4.89.69q-.47.06-.45.54.67 15.3.4 30.47c-.13 7.16.03 15.79-9.12 15.86-6.76.05-8.53-5.22-8.6-10.94q-.15-12.52-.01-24.48.03-2.94.2-6.05.16-3.21-.17-6.08zm53.98-.12q5.42 12.82 5.09 27.86-.18 8-.01 36.13c.06 9.85-2.08 22.49-2.84 34.97l-5.57-.01a.4.4 0 0 1-.41-.41q-.15-19.87.19-40.74.1-6.63-1.07-9.94c-1.8-5.1-6.84-9.69-12.5-10.41q-.8-.1-1.05-.46-.32-.45-.3-1.25l.02-20.84q0-.97-.96-.95l-4.23.09q-.9.02-.91.93l.09 21.05a1.5 1.5 0 0 1-.97 1.41c-1.95.73-4.14 1.32-5.75 2.52q-7.09 5.3-7.14 14.04-.14 22.37.09 44.28 0 .82-.82.83l-17.61.07a4.6 4.6 0 0 1-2.48-.37.7.7 0 0 1-.45-.72l.29-29.33.18 28.92q0 .31.22.55a.8.8 0 0 0 .54.25q9.17.6 18.31.19a1.3 1.3 0 0 0 1.26-1.3c.17-14.85-.29-30.02.13-44.95.51-17.99-.84-34.48-7.46-51.47a.7.7 0 0 0-.69-.49l-6.95-.33z"}),c.createElement("path",{fill:"#B4D9FD",d:"M561.1 565.96q6.03.45 5.98 4.84-.2 16.3.21 29.48c.16 5.06-.88 10.41.5 15.3q1.68 5.96 1.75 8.31.34 12.87.52 25.69c.07 5.27.04 11.19 5.5 13.63q.26.12.35.4a1 1 0 0 1 .01.35q-.05.14-.18.15l-10.95.91-28.35-.12c.76-12.48 2.9-25.12 2.84-34.97q-.17-28.14.01-36.13.33-15.04-5.09-27.86z"}),c.createElement("path",{fill:"#FDCB7D",d:"M697.68 568.75a45 45 0 0 0 6.05 16.88 1.3 1.3 0 0 1 .07 1.17 1.3 1.3 0 0 1-.91.73q-2.23.41-3.4 1.58-5.7 1.29-11.49 1.96-1.65.2-2.24.11-2.85-.43-.21-1.58 2.57-1.12 5.76-3.16l3.1-2.04q1.63-.14 1.56-2.36a6 6 0 0 0-.76-2.45q-.84-1.65-2.48-5.05a6 6 0 0 1-.59-3.78 15 15 0 0 1 5.54-2.01"}),c.createElement("path",{fill:"#FE6D04",d:"M638.23 590.58q-.24-1.77 1.15-4 3.52-.63 3.22-4.31a234 234 0 0 1-.49-7.79.8.8 0 0 1 .3-.67.8.8 0 0 1 .7-.16l4.38 1.03a2.3 2.3 0 0 1 1.68 2.86 34 34 0 0 0-1.25 11.77 2.3 2.3 0 0 1-2.01 2.45q-3.95.48-7.68-1.18"}),c.createElement("path",{fill:"#1350CE",d:"M607.13 590.33a422 422 0 0 1-2.04 16.86q-.57 4-4.41 4.04-1.54 0-2.19 1.25-.32.63.16 1.15l3.14 3.32.61.81-1.2 15.45q-.05.49-.51.32a7 7 0 0 1-2.65-1.96q-7.75-9-15.64-17.98c-2.29-2.6-.5-5.71 2.12-8.07a99 99 0 0 1 22.61-15.19"}),c.createElement("path",{fill:"#2466F8",d:"M611.13 591.51a17 17 0 0 0 7.57 1.78q4.9.06 9.8.54 3.4.33 6.56-.76a4.5 4.5 0 0 0 3.07-1.5q.09.15.42.29.42.15.37.6c-.85 7.47-4.34 12.92-8.67 18.87a226 226 0 0 0-12.76 19.72 14.7 14.7 0 0 0-2.1 7.12 518 518 0 0 0-.11 9.58q-.02 3.77.18 5.95-.74 5.6-.18 10.55.08.66 1.75.64-1.52.2-1.66 3.48 0 6.2.16 12.62.05 1.9-.97 2.15l-4.05.39q-.72 0-.84-.51a731 731 0 0 0-3.28-13.99.9.9 0 0 0-.85-.68l-45.38-.31-29.05.25a.4.4 0 0 0-.3.13.4.4 0 0 0-.12.3l-.03 42.3q0 .47.4.7.3.17.65.1-1.12.03-1.08 1.21a383 383 0 0 1 .12 17.97q0 .5.49.5l32.3.12-31.7.13a1.2 1.2 0 0 0-.87.36 1.2 1.2 0 0 0-.36.88l.07 31.34.07 3.61q.34 5.1-.69 7.61-5 12.2-18.64 11.6l2.64-.23q.52-.04.55-.57.33-6.74.27-13.14c-.03-3.28.58-6.38 1.22-9.56 1.02-5.04.5-14.4-2.51-18.69a1.2 1.2 0 0 1-.21-.93c.64-3.69 1.2-7.36 1.13-11.22q-.32-18.1-.3-23.56.28-68.08.11-98a28 28 0 0 1 1.41-9.57q-.01.79.3 1.25.25.37 1.05.46c5.66.72 10.7 5.31 12.5 10.41q1.17 3.3 1.07 9.94-.34 20.87-.19 40.74a.4.4 0 0 0 .41.41l5.57.01 28.35.12 40.04-.25a.6.6 0 0 0 .5-.25.6.6 0 0 0 .11-.55 176 176 0 0 1-4.08-20.6c-.35-2.48-.04-5-.58-7.37-.41-1.78-1.74-2.93-2.74-4.43a7 7 0 0 0 2.65 1.96q.46.16.51-.32l1.2-15.45q1.54-2.1 1.34-5.98a.66.66 0 0 0-.6-.62 6 6 0 0 0-4.05.96.8.8 0 0 0-.34.9 1 1 0 0 0 .16.31l2.88 3.62-3.14-3.32a.94.94 0 0 1-.16-1.15q.64-1.24 2.19-1.25 3.84-.04 4.41-4.04a422 422 0 0 0 2.04-16.86z"}),c.createElement("path",{fill:"#62A1FF",d:"M616.81 591.93a91 91 0 0 0 18.25 1.14q-3.16 1.1-6.56.76-4.9-.48-9.8-.54a17 17 0 0 1-7.57-1.78zm-14.41 25.83-.61-.81-2.88-3.62a.84.84 0 0 1 .18-1.21 6 6 0 0 1 4.05-.96.66.66 0 0 1 .6.62q.2 3.9-1.34 5.98"}),c.createElement("path",{fill:"#C3E1FB",d:"M745.74 617.55c1 1.87 3.24 2.63 5 1.7 1.75-.94 2.36-3.22 1.36-5.1s-3.24-2.63-5-1.7c-1.75.94-2.36 3.22-1.36 5.1"}),c.createElement("path",{fill:"#9DCBFE",d:"m474.9 634.69-.29 29.33q0 .5.45.72 1.05.5 2.48.37l-7.01-.09a36 36 0 0 0 1.34-9.79q-.04-17.68.08-36.1 0-1.11 1.07-1.46.3-.1.57-.02 1.44.44 1.45 1.68.07 8.04-.14 15.36"}),c.createElement("path",{fill:"#2466F8",d:"M686.78 802.7a25 25 0 0 0-1.78-9.2 498 498 0 0 1-6.13-15.36q-1.75-4.65-1.38-6.55.52-2.61 1.33-5.28c7.85-26.06 15.91-53.78 23.86-78.72a3.8 3.8 0 0 1 2.49-2.49c3.75-1.19 8.6-2.62 11.47-4.91 7.1-5.66 7.47-12.74 3.9-20.36-2.12-4.52-4.4-9.18-5.73-13.9-1.57-5.51-.84-11.24-.99-16.89a1.5 1.5 0 0 1 .89-1.43c2.47-1.13 4.75-1.91 6.61-4.31q1.2-1.54 2.59-5.41a69 69 0 0 1 5.42 9.55q1.22 2.73 1.23 6.56.03 14.6-.11 27.27c-.08 7.79.05 15.6.04 23.32q-.05 55.85.03 111.7 0 9.81-1.29 12.6c-4.01 8.58-13.57 12.72-22.3 8.81q-2.9-1.3-7.81-5.27a605 605 0 0 0-12.34-9.73"}),c.createElement("path",{fill:"#B4D9FD",d:"M615.46 653.7q4.27 5.55 1.57 11.19-1.67.02-1.75-.64-.55-4.95.18-10.55"}),c.createElement("path",{fill:"#478FFF",d:"M738.04 673.51a4.4 4.4 0 1 0 0-8.8 4.4 4.4 0 0 0 0 8.8"}),c.createElement("path",{fill:"#C3E1FB",d:"M410.58 668.07h84.55a.8.8 0 0 1 .81.81l.08 42.16a.6.6 0 0 1-.18.43.6.6 0 0 1-.42.18q-35.59.44-71.24-.07a9 9 0 0 1-3.63-.82c-8.94-4.08-10.66-11.44-10.43-21.04a386 386 0 0 0-.08-21.09.5.5 0 0 1 .33-.52 1 1 0 0 1 .21-.04m149.58-.03a30 30 0 0 1 6.56 17.13c.46 8.48-.56 17.07-.19 26.6l-34.82.05a1 1 0 0 1-.65-.1.8.8 0 0 1-.4-.7l.03-42.3q0-.17.12-.3a.4.4 0 0 1 .3-.13z"}),c.createElement("path",{fill:"#B4D9FD",d:"M610.51 683.53c-.54 1.22-1.11 2.43-1.24 3.76a12.5 12.5 0 0 1-4.46 8.71 2 2 0 0 0-.7 1.12q-.47 1.89.82 1.77c2.09-.19 3.53.08 4.88-1.73q.37-.5.97-.7a7 7 0 0 0 2.82-1.73 5 5 0 0 0 1.14-3.16q.32-4.37-.18-8.43 1.02-.24.97-2.15-.17-6.42-.16-12.62.86-.35 1.41-.07.46.24.48.75.3 6.15-.56 11.85-.6 3.9-.56 7.52.04 3.81-.04 7.79-.03 2.04-1.49 5.93c-1.59 4.24-5.41 7.42-9.61 8.78q-2.4.78-7.22.75l-11.59-.09-18.21.22-1.45-.03c-.37-9.53.65-18.12.19-26.6a30 30 0 0 0-6.56-17.13l45.38.31a.9.9 0 0 1 .85.68q1.7 6.9 3.28 13.99.12.5.84.51"}),c.createElement("path",{fill:"#2466F8",d:"m867.89 678.5-.03 4.67a.7.7 0 0 1-.7.71l-7.48.1a.7.7 0 0 0-.7.69l-.21 7.5a.7.7 0 0 1-.71.69l-4.53-.04a.7.7 0 0 1-.7-.69l-.17-7.48a.7.7 0 0 0-.7-.69l-7.48-.04a.7.7 0 0 1-.71-.7l-.04-4.68a1 1 0 0 1 .05-.27.7.7 0 0 1 .38-.4 1 1 0 0 1 .27-.05l7.52-.07a.7.7 0 0 0 .7-.69l.21-7.45a.7.7 0 0 1 .71-.69l4.54-.04a1 1 0 0 1 .27.05.7.7 0 0 1 .4.38 1 1 0 0 1 .05.27l.17 7.46a.7.7 0 0 0 .71.69l7.47.06a.7.7 0 0 1 .71.71"}),c.createElement("path",{fill:"#FDCB7D",d:"M641.15 682a7.6 7.6 0 0 0-5.22-3.3c3.43-3.1 5.16.15 5.22 3.3"}),c.createElement("path",{fill:"#1350CE",d:"M825.97 694.34a617 617 0 0 0-9.69-7.56q-3.3-2.52-5.32.66a30.5 30.5 0 0 0-4.53 13.09q-.56.12-1.05-.22a1 1 0 0 1-.46-.88q.03-2.02.96-3.92a1.2 1.2 0 0 0-.05-1.11 2 2 0 0 0-.72-.69 2.3 2.3 0 0 1-1.28-2.01 10.6 10.6 0 0 1 5-9.97c6.29-4.2 18.97-6.31 21.19 4.06q1.23 5.7-4.05 8.55"}),c.createElement("path",{fill:"#62A1FF",d:"M278.97 688.61a4.99 4.99 0 1 0 0-9.98 4.99 4.99 0 0 0 0 9.98"}),c.createElement("path",{fill:"#FE6D04",d:"M635.93 678.7q3.38.6 5.22 3.3a13 13 0 0 1 2.16 4.47 2.27 2.27 0 0 1-1.46 2.69 22 22 0 0 0-5.25 2.69c-2.13 1.5-4.99 1.14-7.44 1.42a.7.7 0 0 1-.79-.59q-.26-1.45.79-2.01a75 75 0 0 0 4.39-2.52q.43-.26.59-.74c.99-2.99-.24-5.67 1.79-8.71"}),c.createElement("path",{fill:"#FDCB7D",d:"M614.56 683.14q.5 4.06.18 8.43a5 5 0 0 1-1.14 3.16 7 7 0 0 1-2.82 1.73q-.6.2-.97.7c-1.35 1.81-2.79 1.54-4.88 1.73q-1.29.12-.82-1.77.17-.67.7-1.12a12.5 12.5 0 0 0 4.46-8.71c.13-1.33.7-2.54 1.24-3.76zm211.41 11.2a21 21 0 0 1-3.57 6.81q-1.15 1.44-4.97 5.48-5.05 3.22-9.28-2.95l-1.72-3.15a30.5 30.5 0 0 1 4.53-13.09q2.02-3.18 5.32-.66a617 617 0 0 1 9.69 7.56"}),c.createElement("path",{fill:"#9DCBFE",d:"M256.68 699.16c-.23.66.05 1.35.63 1.56s1.23-.16 1.47-.82-.05-1.35-.63-1.56-1.23.16-1.47.82"}),c.createElement("path",{fill:"#C3E1FB",d:"M817.43 706.63q.51.75 2.01.87 1.4.1 2.05.41c4.81 2.15 6.38 6.65 7.7 11.41l-14.79.57a3.6 3.6 0 0 0-3.19 2.23l-7.69 18.6q-1 .45-1.01 1.17c-2.44.67-3.86 1.94-2.99 4.63a3 3 0 0 0-.31 3.12q.22.5.76.49l36.5.11-4.72.74a.48.48 0 0 0-.24.84q5.6 4.95 5.17 10.72-4.5.82-8.68 1.15a3386 3386 0 0 1-34.76 2.59l-.22 2.06q-.05.47-.18.02c-.64-2.25-1.14-4.34-.44-6.67a74 74 0 0 0 2.18-9.33 2.3 2.3 0 0 0-.25-1.44 2.3 2.3 0 0 0-1.07-.99c-4.44-1.96-12.07-4.99-11.41-11.18q.33-3.1 1.51-6.29 2.97-8.03 7.27-16.31 2.25-4.35 5.58-7.69 3.79-3.82 11.94-4.78 4.23 6.17 9.28 2.95"}),c.createElement("path",{fill:"#2466F8",d:"m586.19 711.58.16 10.31a24 24 0 0 0-.49 8.87 1 1 0 0 1-.23.78 1 1 0 0 1-.74.35l-15.84.3-1.52-.46a4 4 0 0 0 1.2-.7q.38-.34.38-.85c.07-6.9 1.33-13.06-1.13-18.38z"}),c.createElement("path",{fill:"#1350CE",d:"m566.53 711.77 1.45.03c2.46 5.32 1.2 11.48 1.13 18.38q0 .5-.38.85a4 4 0 0 1-1.2.7l-3.99-.11-32.3-.12q-.5 0-.49-.5a383 383 0 0 0-.12-17.97q-.04-1.18 1.08-1.21z"}),c.createElement("path",{fill:"#9DCBFE",d:"M255.8 713.8c.41-.31.52-.86.25-1.22s-.84-.4-1.25-.08c-.41.31-.52.86-.25 1.22s.84.4 1.25.08"}),c.createElement("path",{fill:"#62A1FF",d:"M280.81 718.19a1 1 0 0 1 .46-.13.9.9 0 0 1 .92.91q0 1.15-.97 1.3a.8.8 0 0 1-.91-.58q-.28-1.04.5-1.5"}),c.createElement("path",{fill:"#2466F8",d:"m836.47 750.24-36.5-.11a.8.8 0 0 1-.76-.49 3 3 0 0 1 .31-3.12c3.38.78 5.9-2.07 8.45-3.7a.5.5 0 0 0 .1-.74.5.5 0 0 0-.25-.14q-1.8-.42-3.89-.13-.75-.5-.41-1.09l7.69-18.6a3.6 3.6 0 0 1 3.19-2.23l14.79-.57q30.3-.3 42.56-.72 3.4-.12 3.13 3.07-.9 10.32-3.37 20.99-1.11 4.83-4.14 6.54-1.9 1.07-6.81 1-12.08-.17-24.09.04m8.45-15.88c-.01-.67-.65-1.3-1.78-1.74a11 11 0 0 0-4.23-.66q-1.19.02-2.29.23a9 9 0 0 0-1.92.58q-.84.36-1.28.83t-.44.96c.01.67.65 1.3 1.78 1.74 1.13.45 2.65.69 4.23.66a14 14 0 0 0 2.29-.23 9 9 0 0 0 1.92-.58q.84-.36 1.28-.83t.44-.96"}),c.createElement("path",{fill:"#FAF9F9",d:"m586.35 721.89 33.67 27.21a1.4 1.4 0 0 1 .47 1.56l-31.26 100q-.6-4.2-2.18-6.76-3.64-5.85-9.36-5.51-5.44.32-8.52 4.25c-3.59 4.6-2.55 11.99 2.55 15.36q2.85 1.88 6.96 1.58 4.5-.33 9.24-4.37l-5.82 18.91q-2.37-1.02-4.77-2.19a21.2 21.2 0 0 0-15.41-1.55 23.4 23.4 0 0 0-16.94 24.97c1.16 10.36 8.2 18.52 18.74 20.22q2.67.43 5.51.8l-9.33 29.9c-4.84-.94-7.44.55-11.44 3.5a1 1 0 0 1-.52.2 1.1 1.1 0 0 1-1-.39 1 1 0 0 1-.24-.5 4 4 0 0 1 .13-1.91 2.6 2.6 0 0 1 1.08-1.42c6.66-4.23 10.81-8.72 10.51-16.94q-.2-5.12-2.38-8.36a18 18 0 0 0-6.46-5.98c-2.41-1.31-5.31-1.48-7.97-2a.6.6 0 0 1-.37-.22.6.6 0 0 1-.12-.42q.48-6.76-.12-13.62c-.66-7.38.13-15.19-.16-22.67a1.1 1.1 0 0 1 .35-.86 1.1 1.1 0 0 1 .88-.31c7.97.71 10.21-4.71 10.04-11.66q-.15-6.02.06-11.62c.27-7.41-3.21-13.65-11.19-13.44q-.62.01-.61-.6l.17-27.1q0-.64.59-.88c3.41-1.45 5.37-4.19 5.35-7.96a5585 5585 0 0 1-.1-32.76.66.66 0 0 0-.67-.66l-14.96.25-.07-3.61q.58-1 .94-.7.57.46 1.31.49 6.36.18 12.52-.07.54-.03.92-.4a5 5 0 0 1 3.28-1.56q6.46-.45 13.56-.5c4.82-.02 5.73-3.48 5.71-7.61q-.06-10.88.13-21.79l15.84-.3a1 1 0 0 0 .97-1.13q-.58-4.4.49-8.87m-24.99 84.19c-6.53-1.92-15.2 1.44-15.81 9.18q-.5 6.29-.41 12.3a7.7 7.7 0 0 0 1.61 4.94q6.2 7.54 15.42 4.32c13.71-4.79 13.24-26.59-.81-30.74"}),c.createElement("path",{fill:"#9DCBFE",d:"M511.42 787.15q13.63.6 18.64-11.6 1.04-2.52.69-7.61l14.96-.25a.7.7 0 0 1 .62.4 1 1 0 0 1 .05.26q0 15.64.1 32.76c.02 3.77-1.94 6.51-5.35 7.96a.9.9 0 0 0-.59.88l-.17 27.1q0 .62.61.6c7.98-.21 11.46 6.03 11.19 13.44q-.21 5.6-.06 11.62c.17 6.95-2.07 12.37-10.04 11.66a1.1 1.1 0 0 0-.88.3 1.1 1.1 0 0 0-.35.87c.29 7.48-.5 15.29.16 22.67q.6 6.86.12 13.62a.6.6 0 0 0 .49.64c2.66.52 5.56.69 7.97 2a18 18 0 0 1 6.46 5.98q2.19 3.24 2.38 8.36c.3 8.22-3.85 12.71-10.51 16.94a2.6 2.6 0 0 0-1.08 1.42 4 4 0 0 0-.13 1.91 1.12 1.12 0 0 0 1.76.69c4-2.95 6.6-4.44 11.44-3.5l-4.26 15.65q-.88-1.24-4.46-.89-.5.05-.89-.28a16 16 0 0 1-2.66-2.96c-5.22-7.53-18.14-8.95-26.54-8.09q-8.3.85-14.31 7a1.6 1.6 0 0 1-1 .47 1.6 1.6 0 0 1-1.06-.3 21 21 0 0 0-17.8-3.73 15.2 15.2 0 0 0-8.25 5.2.7.7 0 0 1-.65.27.7.7 0 0 1-.58-.4 25 25 0 0 0-4.43-6.1.8.8 0 0 1-.21-.87l.23-.79q1.05-.43.88-1.73a3.2 3.2 0 0 0-1.84-2.53c-13.21-6.88-16.59-24.84-9.36-37.17q7.46-12.75 22.69-14.98a1.43 1.43 0 0 0 1.22-1.52 57 57 0 0 1-.09-8.16c.31-4.81 3.03-8.63 3.36-13.44q.45-6.35 1.03-9.23 2.33-11.31 12.29-16.5a1 1 0 0 0 .54-.91q-.24-24.96.22-52.15.03-1.45-1.39-1.15-.84.18-1.44 1.06a2 2 0 0 0-.33 1.1q.14 21.1.27 43.88c.03 5.06-2.12 7.23-6.1 9.89-3.73 2.49-8.01 2.74-11.82.55-2.83-1.62-2.29-5.05-2.3-7.73 1.4-15.13 1.7-30.43.15-45.52q-.58-5.58-1.13-11.15l.45-15.32 14.57-.03q.65 0 .81-.62.41-1.53-.41-2.46a1.2 1.2 0 0 0-.92-.42l-10.48-.07c-1.86.02-3.39.21-5.17-.62-4.6-2.14-9.38-1.73-14.25-1.57-5.98.2-7.36-4.09-7.21-9q.3-10.42.44-20.86 1.08-.4 1.54-.4 18.13.07 35.9-.06a.9.9 0 0 1 .89.59 1 1 0 0 1 .07.37q-.02 19.39-.07 38.36c-.03 8.78 7.08 15.14 15.38 16.2"}),c.createElement("path",{fill:"#9DCBFE",d:"m563.54 731.62 3.99.11 1.52.46a949 949 0 0 0-.13 21.79c.02 4.13-.89 7.59-5.71 7.61q-7.1.05-13.56.5-1.88.13-3.28 1.56a1.4 1.4 0 0 1-.92.4q-6.15.25-12.52.07a2 2 0 0 1-1.31-.49q-.36-.3-.94.7l-.07-31.34a1.3 1.3 0 0 1 .36-.88 1.2 1.2 0 0 1 .87-.36z"}),c.createElement("path",{fill:"#B4D9FD",d:"M839 736.96c3.29-.06 5.94-1.22 5.92-2.6s-2.72-2.46-6.01-2.4c-3.3.06-5.95 1.22-5.93 2.6s2.72 2.46 6.01 2.4"}),c.createElement("path",{fill:"#2466F8",d:"M676.53 741.06a329 329 0 0 0-5.47 17.81q-.3 1.1-.76 1.99a.53.53 0 0 1-.73.22 1 1 0 0 1-.2-.2c-2.36-4.26-3.75-9.57-5.55-13.82a.8.8 0 0 1 .45-1.03c4.13-1.56 7.93-2.44 11.21-5.63q1.92-1.87 1.05.66"}),c.createElement("path",{fill:"#62A1FF",d:"M803.52 740.72q-.34.6.41 1.09l-1.42.08q0-.72 1.01-1.17"}),c.createElement("path",{fill:"#FDCB7D",d:"M799.52 746.52c-.87-2.69.55-3.96 2.99-4.63l1.42-.08q2.1-.3 3.89.13a.5.5 0 0 1 .36.38.5.5 0 0 1-.21.5c-2.55 1.63-5.07 4.48-8.45 3.7"}),c.createElement("path",{fill:"#2466F8",d:"M648.99 770.97a.7.7 0 0 1-.49-.25.7.7 0 0 1-.15-.53q.42-3.3.35-6.42a624 624 0 0 1-.13-20.15.64.64 0 0 1 1.08-.46 12 12 0 0 1 2.31 2.98 127 127 0 0 1 7.92 17.79c.68 1.9 1.71 3.68 2.71 5.44a1.02 1.02 0 0 1-.8 1.53q-6.24.52-12.8.07"}),c.createElement("path",{fill:"#B4D9FD",d:"M192 774.89q-1.62 4.92-5.4 2.9a2689 2689 0 0 1-6.63-16.74c-1.56-3.97-3.69-7.59-4.22-11.76q-.36-2.83 2.64-2.77 1.05.01 1.63.9a33 33 0 0 1 3.31 6.71q.9 2.48 4.66 10.69c.47 1.02.54 2.31.98 3.21a108 108 0 0 1 3.03 6.86"}),c.createElement("path",{fill:"#FDCB7D",d:"M836.41 752.64a.5.5 0 0 1 .18-.29l.23-.18a.4.4 0 0 1 .16-.06l19.3.18q.84 0 .75.84l-.04.35a.5.5 0 0 1-.53.47l-20.53-.26q-.5 0-.39-.48a1 1 0 0 1 .27-.46q.2-.16.4-.03.15.11.2-.08"}),c.createElement("path",{fill:"#FAF9F9",d:"m629.48 756.82 16.56 13a.58.58 0 0 1-.02.93.6.6 0 0 1-.34.11l-21.05.12a.6.6 0 0 1-.47-.24 1 1 0 0 1-.1-.25 1 1 0 0 1 .02-.27l4.49-13.13a.58.58 0 0 1 .91-.27"}),c.createElement("path",{fill:"#1350CE",d:"m211.06 771.05-2.55 9.07-.95-1.2q-.72-2.03 1.24-3.14.43-.25.24-.71a44 44 0 0 0-1.41-3.15c-2.42-4.85 1.92-9.94 6.3-12.01 7.02-3.31 17.53-2.41 16.58 8.19a4.9 4.9 0 0 1-1.96 3.64c-2.12 1.61-4.31 0-5.77-1.55a37 37 0 0 0-4.58-4.09c-3.66-2.77-5.81 1.59-7.14 4.95"}),c.createElement("path",{fill:"#2466F8",d:"M836.68 762.54q2.49 12.2 3.15 21.21 2.29 31.17.84 65.73l-8.46.26-.47.07q-.48-.57-.53-1.06c-2.32-24.01-5.19-45.63-13.61-67.87a6 6 0 0 0-1.02-1.76.6.6 0 0 0-.56-.2.6.6 0 0 0-.41.43 72 72 0 0 0-1.01 5.71 590 590 0 0 0-5.06 58.19q-.1 2.4-.83 5.97l-10.02-.13a2513 2513 0 0 1-1.2-31.09q-.3-9.54-1.53-19.5l-1.9-20.57-.82-11.65a3386 3386 0 0 0 34.76-2.59q4.17-.33 8.68-1.15"}),c.createElement("path",{fill:"#E3F1FB",d:"M480.33 767.74q2.79-.4 3.69-1.49.66-.8-.12-2.11l10.48.07q.55 0 .92.42.82.93.41 2.46-.16.62-.81.62z"}),c.createElement("path",{fill:"#FDCB7D",d:"M217.32 783.96a15 15 0 0 1-8.81-3.84l2.55-9.07q.81-.3 2.56-2.45c2.69-3.3 5.81.55 7.69 2.46 1.04 1.06 1.94 1 3.01 1.69q2.25 1.45.59 3.4c-2.03 2.39-4.68 6.75-7.59 7.81"}),c.createElement("path",{fill:"#C3E1FB",d:"m207.56 778.92.95 1.2a15 15 0 0 0 8.81 3.84 12 12 0 0 0-.52 3.8q-1.78.2-2.21 1.4a300 300 0 0 1-9.52 23.14 2 2 0 0 0-.18.98q.04.44.23.83.75 1.47-.9 1.54-2 .1-2.66 1.6a1.18 1.18 0 0 0 1.01 1.64q8.54.5 17.49-.26a1 1 0 0 0 .76-.47 5 5 0 0 0 .69-1.98l2.68 1.43q.44.23.56.71l2.42 9.57-3.55 2.31a87 87 0 0 1-15.99 1.92q-12.18.5-21.19-.56-2.46-3.23-1.23-5.9a272 272 0 0 1 5.22-10.69 2.3 2.3 0 0 0-.56-2.77q-5.6-4.74-10.99-13.79c-2.34-3.93-1.18-8.06 3.06-10.15q2.9-1.42 5.93-2.76 2.01-.88 1.06-2.86l-2.33-4.85q3.78 2.02 5.4-2.9l2.21 5.75a2.4 2.4 0 0 0 1.17 1.3 2.4 2.4 0 0 0 1.74.17q4.96-1.35 9.88-1.94.63-.08.56-1.25"}),c.createElement("path",{fill:"#B4D9FD",d:"M766.95 782.15a2.83 2.83 0 1 0 0-5.66 2.83 2.83 0 0 0 0 5.66m29.01 16.35-30.98 76.42q-.19.46.31.51.38.04.97-.18a58 58 0 0 0-10.51 16.48q-1.65 3.9-3.35 7.8-.98 2.23-2.51 2.47-2.05.3-4.19.82 2.96-1.34 3.78-3.39 4.38-11 9.49-24.85 3.09-8.4 7.38-18.51a839 839 0 0 0 10.3-25.47c1.96-5.08 4.44-9.73 6.41-14.75q3.5-8.88 7.06-18.23.33-.88 2.25-5.21 1.56-3.52 1.52-5.53-.08-4.45.17-8.95z"}),c.createElement("path",{fill:"#FAF9F9",d:"m831.74 849.81.15 1.12.67 9.46q-.63.74-.68 1.83.28-1.74-1-2.35a39 39 0 0 0-17.72-3.94l-4.4-.18q-.51-.02-.47-.53l.42-6q.74-3.56.83-5.97a590 590 0 0 1 5.06-58.19 72 72 0 0 1 1.01-5.71.6.6 0 0 1 .41-.42.5.5 0 0 1 .56.19q.68.83 1.02 1.76c8.42 22.24 11.29 43.86 13.61 67.87q.05.5.53 1.06m-168.53-67.53q1.36.45.94 1.81l-8.64 27.93a1.84 1.84 0 0 1-1.76 1.3h-42.02a.65.65 0 0 1-.64-.54 1 1 0 0 1 .02-.3l10.02-31.7a1.2 1.2 0 0 1 1.12-.81q17.6.17 34.72-.09c1.04-.01 2.04-.07 2.91.61a11 11 0 0 0 3.33 1.79"}),c.createElement("path",{fill:"#B4D9FD",d:"M479.88 783.06q.55 5.57 1.13 11.15c1.55 15.09 1.25 30.39-.15 45.52l-.03-5.05a.86.86 0 0 0-.88-.85c-3.13.05-6.47.38-9.42-.51-4.72-1.42-9.13-5.79-9.67-11.01-1.1-10.83 6.99-18.05 17.61-16.94a1.2 1.2 0 0 0 .93-.3 1.2 1.2 0 0 0 .41-.9z"}),c.createElement("path",{fill:"#FE6D04",d:"M425.78 807.1q-3.53-1.1-6.04.08c-4.04 4.05-8.57 8.33-10.04 13.79-.67 2.49-.49 6.95-3.17 8.21-2.83 1.32-6.85 2.28-9.96 1.19-8.71-3.07-8.6-14.06-2.71-19.71 3.45-3.3 7.73-5.32 9.06-10.52l1.82-7.15c1.5-5.9 5.51-7.23 11.36-6.9q3.75.21 7.4.57a1.5 1.5 0 0 1 1.27 1.08q.08.29.04.58c-.44 3.32 1.2 5.77 1.35 9.01a60 60 0 0 1-.38 9.77"}),c.createElement("path",{fill:"#1350CE",d:"m216.8 787.76 42.71.13q.53 0 .74.48a6 6 0 0 1 .43 3.35 175 175 0 0 1-3.18 15.53c-.83 3.31-2.15 7.76-6.3 7.89q-15.1.45-25.7.26a8.3 8.3 0 0 0-3.99.78 5 5 0 0 1-.69 1.98 1 1 0 0 1-.76.47q-8.96.76-17.49.26a1.2 1.2 0 0 1-.93-.56 1.2 1.2 0 0 1-.08-1.08q.66-1.5 2.66-1.6 1.65-.07.9-1.54a2 2 0 0 1-.23-.83 2 2 0 0 1 .18-.98 300 300 0 0 0 9.52-23.14q.43-1.2 2.21-1.4m21.03 14q-.05-.52-.88-.85a5 5 0 0 0-2.03-.26 6 6 0 0 0-1.09.15 4 4 0 0 0-.9.32q-.4.2-.6.44a.7.7 0 0 0-.2.49q.05.51.88.84c.55.2 1.28.3 2.04.26a6 6 0 0 0 1.08-.15q.52-.14.9-.32c.39-.18.47-.28.6-.44a.7.7 0 0 0 .2-.49"}),c.createElement("path",{fill:"#FE6D04",d:"M459.27 795.66a7.5 7.5 0 0 0-2.48-1.99q3.01-4.64 9.06-5.42a.9.9 0 0 1 .97.65q.41 1.58-.92 2.76a18 18 0 0 1-6.63 4"}),c.createElement("path",{fill:"#B4D9FD",d:"M684.55 801.02q-1.4.47-.7 1.72 1.8 3.21 3 6.06a6674 6674 0 0 1 36.22 86.44 219 219 0 0 0 3.77 8.65q1.95 4.2 3.91 8.52l-5.9 6.28a206 206 0 0 0-6.12-15.92L713 889.58a10590 10590 0 0 0-32.9-78.91 353 353 0 0 1-7.19-18.54z"}),c.createElement("path",{fill:"#C3E1FB",d:"M456.79 793.67a7.5 7.5 0 0 1 2.48 1.99q1.41.64.29 2.57a16 16 0 0 1-2.52 3.33 78 78 0 0 1-24.85 16.51q-4.46 1.89-5.75 7.24a32 32 0 0 0-.86 11.19q1 9.15 2.08 18.26-.3-1.04-1.16-.87a91 91 0 0 1-26 1.43q-5.9-.52-13.48-4.76 1.35-2.31 2.65-4.17 3.99-5.65 8.01-11.5c.64-.94 2.13-2.07 3.27-2.21a17 17 0 0 0 7.75-2.97q.55-.4.71-1.06c.64-2.89 1.02-5.4 2.46-8.17a264 264 0 0 1 3.4-6.36c1.2-2.18 3.15-4.43 4.47-6.94q2.51-1.2 6.04-.08a42 42 0 0 0 13.04-5.04 224 224 0 0 1 14.98-8.26q1.87-.92 2.99-.13"}),c.createElement("path",{fill:"#FAF9F9",d:"M795.96 798.5q1.23 9.96 1.53 19.5.51 15.71 1.2 31.09l1.06 7.51-12.78 3.86a58 58 0 0 0-20.71 14.79q-.6.22-.97.18-.5-.05-.31-.51z"}),c.createElement("path",{fill:"#9DCBFE",d:"M235.05 803.15c1.57-.08 2.81-.71 2.78-1.4s-1.34-1.18-2.92-1.1-2.81.71-2.78 1.4 1.34 1.18 2.91 1.1m326.31 2.93c14.05 4.15 14.52 25.95.81 30.74q-9.23 3.22-15.42-4.32a7.7 7.7 0 0 1-1.61-4.94q-.09-6.02.41-12.3c.61-7.74 9.28-11.1 15.81-9.18"}),c.createElement("path",{fill:"#E3F1FB",d:"M229.07 817.12q2.85-.17 4.55.13 2.82.5-.04.64a31 31 0 0 1-4.78-.25l.01-.25q.01-.25.26-.27m88.25.96q-2.44 2.06-3.21 3.54c-2.45 4.77-4.98 9.45-7.05 14.42a1854 1854 0 0 1-8.56 20.25 49 49 0 0 0-1.81 6.05l-2.61-2.27a1.5 1.5 0 0 0-.88-.38l-2.17-.23q2.32-2.6 3.77-5.92c2.62-6.03 4.84-11.5 7.9-17.9 2.56-5.36 4.42-11.75 7.99-16.51z"}),c.createElement("path",{fill:"#FAF9F9",d:"m641.35 856.11-42.91-.06a.7.7 0 0 1-.66-.91l10.06-32.06a.7.7 0 0 1 .66-.49l42.9.07a.7.7 0 0 1 .7.59 1 1 0 0 1-.03.32l-10.06 32.05a.7.7 0 0 1-.66.49"}),c.createElement("path",{fill:"#B4D9FD",d:"M227.17 827.89c4.28 9.11 8.13 18.32 12.52 27.64a77 77 0 0 1 3.28 7.96c2.03 5.95 4.79 11.21 6.87 17.02a105 105 0 0 0 7.06 15.59q1.24 2.25 1.81 7.01a7 7 0 0 0-3.85.61q-3.56-6.52-4.4-8.71c-2.73-7.16-6.16-14.05-8.76-21.1-1.59-4.32-3.8-8.31-5.48-12.68l-4.18-11.06c-1.87-4.97-4.29-9.73-6.35-14.67a.7.7 0 0 0-.4-.38.7.7 0 0 0-.53.03q-.15.07-.05.65l-1.09-5.6z"}),c.createElement("path",{fill:"#1350CE",d:"m223.62 830.2 1.09 5.6a787 787 0 0 0 10.15 67.01 21 21 0 0 0-8.68 1.53 21 21 0 0 1-2.32-4.91 970 970 0 0 1-17.14-52.11q-.6-2-1.21-.01c-3.59 11.56-5.24 23.81-6.34 35.69q-.65 7.02-1.1 21.88-4.5-.75-8.49.39a6 6 0 0 1-.49-2.03 836 836 0 0 1-3.58-49.49q-.42-10.5.93-22.19 9 1.05 21.19.56a87 87 0 0 0 15.99-1.92"}),c.createElement("path",{fill:"#9DCBFE",d:"M257.5 838.83a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5m331.73 11.83-1.31 4.55q-4.74 4.05-9.24 4.37-4.11.3-6.96-1.58c-5.1-3.37-6.14-10.76-2.55-15.36q3.08-3.93 8.52-4.25 5.71-.34 9.36 5.51 1.6 2.55 2.18 6.76m-150.74 10.7c-3.45 13.65 4.6 27.46 17.99 30.85s27.03-4.93 30.49-18.58-4.6-27.45-17.99-30.84-27.03 4.93-30.49 18.58"}),c.createElement("path",{fill:"#B4D9FD",d:"M739.71 845.47c.6-.28.92-.83.73-1.23-.19-.39-.82-.48-1.41-.2-.6.3-.92.84-.73 1.24.19.39.82.48 1.41.2"}),c.createElement("path",{fill:"#FAF9F9",d:"M226.18 904.34q.12 1.13-.46 1.8-11.18-2.32-19.97.85a86 86 0 0 1-8.08 2.64l.4-4.75q.45-14.86 1.1-21.88c1.1-11.88 2.75-24.13 6.34-35.69q.62-1.98 1.21.01a970 970 0 0 0 17.14 52.11q1.2 3.37 2.32 4.91"}),c.createElement("path",{fill:"#FE6D04",d:"m808.71 849.22-.42 6q-.04.51.47.53l4.4.18-13.41.67-1.06-7.51zm31.96.26q-.8 5.25-1.69 10.27-.55 3.17 2.13 3.56 2.67.38 5.17 1.48l-12.88.13q-.53 0-.71-.49a7 7 0 0 1-.13-4.04l-.67-9.46q.5-.79.32-1.19z"}),c.createElement("path",{fill:"#FDCB7D",d:"M832.21 849.74q.18.4-.32 1.19l-.15-1.12z"}),c.createElement("path",{fill:"#62A1FF",d:"M427.66 854.76q-1.2 1.42-1.21 2.4a9 9 0 0 0-4.49-.35q-4.29.6-4.42.6a81 81 0 0 1-26.92-2.95 1 1 0 0 0-.89.18l-.72.57q.6-1.2-1.99-4.65 7.57 4.24 13.48 4.76 13.1 1.15 26-1.43.87-.16 1.16.87"}),c.createElement("path",{fill:"#2466F8",d:"M862.57 866.8a6.49 6.49 0 1 0 0-12.98 6.49 6.49 0 0 0 0 12.98"}),c.createElement("path",{fill:"#C3E1FB",d:"M324.2 865.06a5.58 5.58 0 1 0 0-11.16 5.58 5.58 0 0 0 0 11.16"}),c.createElement("path",{fill:"#2466F8",d:"M426.45 857.16q1.1 14.6.03 28.11a238 238 0 0 0 .67 43.85q1.1 10.05 2.25 20.12.14 1.16-.22 3.01a6.7 6.7 0 0 0-3.86.07q-1.54.56-1.98-1.19a1512 1512 0 0 0-7.13-26.98c-3.23-11.75-4.46-19.97-6.1-33.08a70 70 0 0 0-1.83-8.71q-1.3-4.6-2.96-.11a51 51 0 0 0-2.44 10.99l-3.53 20.49-4.54 24.04-2.04 13.95q-.9.32-1.48.01a14 14 0 0 0-3.58-1.37q-.93-.15-.8-2.11.44-6.47.21-11.86.53-33.36.3-65.64a83 83 0 0 1 1.59-15.54l.72-.57a1 1 0 0 1 .89-.18 81 81 0 0 0 26.92 2.95q.13 0 4.42-.6 2.7-.37 4.49.35"}),c.createElement("path",{fill:"#478FFF",d:"M813.16 855.93a39 39 0 0 1 17.72 3.94q1.28.6 1 2.35-.6 3.67 3.11 3.86 7.66.38 12.27.01l-3.29.65q-.47.1-.41.56c2.44 21.1 2.79 41.11 2.9 64.46q.1 24.26-.67 36.76-.04.55-.59.55-10.62 0-21.02.69-.59.05-.02.2.14.03.34.03 8.55-.27 17.06.31l-54.77.05-32.73-.52q-1.77-.02-.2-.83c7.45-3.82 7.81-10.47 7.63-17.9-.46-18.93-.64-38.41 1.03-57.4 1.5-17.04 11.43-24.4 24.45-33.24l12.78-3.86z"}),c.createElement("path",{fill:"#C3E1FB",d:"m291.03 859.46 2.17.23q.5.05.88.38l2.61 2.27q3.51 7.46-4.93 10.33l-5.28-2.34q-3.6-8 4.55-10.87"}),c.createElement("path",{fill:"#62A1FF",d:"M786.97 860.46c-13.02 8.84-22.95 16.2-24.45 33.24-1.67 18.99-1.49 38.47-1.03 57.4.18 7.43-.18 14.08-7.63 17.9q-1.57.81.2.83l32.73.52-106.82-.05a.8.8 0 0 1-.75-.5 1 1 0 0 1-.06-.31q-.17-20.53 16.14-31.28c7.25-4.77 13.93-5.28 22.25-5.87q.6-.05.78-.6a47 47 0 0 1 6.52-13.05l5.9-6.28a51 51 0 0 1 14.95-9.59 51 51 0 0 1 4.19-.82q1.53-.24 2.51-2.47 1.7-3.9 3.35-7.8a58 58 0 0 1 10.51-16.48 58 58 0 0 1 20.71-14.79"}),c.createElement("path",{fill:"#FDCB7D",d:"M832.56 860.39a7 7 0 0 0 .13 4.04q.18.5.71.49l12.88-.13q1.37.22 1.45 1.67-.63-.15-.47-.37-4.6.37-12.27-.01-3.72-.19-3.11-3.86.05-1.1.68-1.83"}),c.createElement("path",{fill:"#FAF9F9",d:"M591.07 897.06q.8-6.84.12-8.89a44 44 0 0 1-1.54-6.17l5.09-16.33a1.15 1.15 0 0 1 1.1-.81l42.37.08a.8.8 0 0 1 .77.66 1 1 0 0 1-.02.36l-9.31 30.25a1.2 1.2 0 0 1-.44.62 1.3 1.3 0 0 1-.73.24z"}),c.createElement("path",{fill:"#377DFE",d:"M847.26 866.09q-.15.22.47.37c2.09 2.92 5.41 5.08 8.06 7.5a61 61 0 0 1 14.15 19.14.7.7 0 0 0 .5.37.7.7 0 0 0 .58-.2q9.2-9.8 22.78-8.96l-.74.17q-.8.18-.01.43c2.68.84 5.5.89 8.11 2.09q2.3 1.04 4.4 2.09c1.28.64 2.03 1.86 3.36 2.65 4.21 2.49 6.46 7.63 8.67 11.9q.05.1.15.16a.4.4 0 0 0 .42 0 .4.4 0 0 0 .15-.15q.12-.22.05-.67c3.77 9.19 1.31 20.16-4.69 27.7a.7.7 0 0 0-.13.63.7.7 0 0 0 .45.46l2.33.8q2.3 2.25 5.61 3.59c2.62 8.53 3.8 16.35-.3 24.69a77 77 0 0 1-4.37 7.73.9.9 0 0 1-.77.43l-25.34.17q-3.97.02-10.36 1.14l-39.23-.02q-8.52-.58-17.06-.31l-.34-.03q-.56-.15.02-.2a327 327 0 0 1 21.02-.69q.55 0 .59-.55.78-12.5.67-36.76c-.11-23.35-.46-43.36-2.9-64.46q-.06-.47.41-.56z"}),c.createElement("path",{fill:"#9DCBFE",d:"m582.1 874.12-12.87 42.25q-2.85-.37-5.51-.8c-10.54-1.7-17.58-9.86-18.74-20.22a23.4 23.4 0 0 1 16.94-24.97 21.2 21.2 0 0 1 15.41 1.55q2.4 1.17 4.77 2.19"}),c.createElement("path",{fill:"#E3F1FB",d:"M291.76 872.67a157 157 0 0 0-6.63 14.22q-8.26 20.01-11.36 27.89-1.9 4.84-1.33 8.5a20.2 20.2 0 0 0-12.45-2.26 10 10 0 0 1 3.75-.35 1 1 0 0 0 .84-.32 11 11 0 0 0 2.31-3.34q7.65-19.42 11.35-27.52a340 340 0 0 0 8.24-19.16zm118.35 18.4q-3.64 3.33-7.23 2.17a51 51 0 0 1 2.44-10.99q1.65-4.5 2.96.11 1.11 3.9 1.83 8.71"}),c.createElement("path",{fill:"#9DCBFE",d:"M589.65 882a44 44 0 0 0 1.54 6.17q.67 2.05-.12 8.89l-4.9-.08a1 1 0 0 1-.39-.1.9.9 0 0 1-.43-1.03z"}),c.createElement("path",{fill:"#1350CE",d:"M893.8 884.31q17.94 1.98 24.56 18.67.07.45-.05.67a.4.4 0 0 1-.72-.01c-2.21-4.27-4.46-9.41-8.67-11.9-1.33-.79-2.08-2.01-3.36-2.65q-2.1-1.05-4.4-2.09c-2.61-1.2-5.43-1.25-8.11-2.09q-.79-.25.01-.43z"}),c.createElement("path",{fill:"#B4D9FD",d:"M426.48 885.27q-.04 1.64.63 2.43.32.4.83.4 8.35.2 14.96 7.06 6.33 6.57 6.23 16.3-.14 14.6-.08 29.74a11.8 11.8 0 0 1-4.12 9.31 1.4 1.4 0 0 1-.87.35 9 9 0 0 0-2.99.61q-1.85.83.17.93.58.03 1.72-.01.5-.02.91-.32l4.88-3.6q14.31.64 18.18.27 2.9-.29 2.14-1.44 1.11 1.18 2.33 2.56.63.7 1.63.59l-.23.79q-.15.5.21.87a25 25 0 0 1 4.43 6.1.8.8 0 0 0 .58.4.7.7 0 0 0 .65-.27 15.2 15.2 0 0 1 8.25-5.2 21 21 0 0 1 17.8 3.73q.47.34 1.06.3a1.6 1.6 0 0 0 1-.47 22.8 22.8 0 0 1 14.31-7c8.4-.86 21.32.56 26.54 8.09a16 16 0 0 0 2.66 2.96q.4.33.89.28 3.58-.35 4.46.89l-2.28 7.57a.7.7 0 0 0 .05.54l.14.26q-40.4-.37-80.8-.09c-14 .09-29.34-.51-43-.4q-17.29.15-29.75.07c-14.04-.08-28.5.36-42.44.27q-2.79-.02-2.55-2.9 1.44-16.95 15.48-26.01a30 30 0 0 1 16.63-4.84q.22 5.4-.21 11.86-.13 1.96.8 2.11l-.47.18q-.5.2-.47.74a22 22 0 0 1-1.29 8.22 1.5 1.5 0 0 0 .09 1.19 1.5 1.5 0 0 0 .94.73l2.02.5q.54.13 1.01.43 3.85 2.45 7.82 1.57 3.43-.77.11-1.94a22 22 0 0 0-3.09-.83c-3.14-.59-4.01-4.18-3.37-6.92q.47-2.05 1.76-2.51l2.04-13.95 4.28.4a.9.9 0 0 0 .93-.55 1 1 0 0 0 .07-.37q-.22-10.11.49-20.7a10 10 0 0 0-.01-1.59q-.23-2.68-1.22-1.23l3.53-20.49q3.58 1.16 7.23-2.17c1.64 13.11 2.87 21.33 6.1 33.08a1512 1512 0 0 1 7.13 26.98q.44 1.75 1.98 1.19l-.61 9.74q-.03.55.47.78 5.84 2.63 10.59 1.39a1.3 1.3 0 0 0 .83-.66q.1-.18.15-.38.27-1.13-.89-1.15-8.7-.12-6.68-9.79.36-1.85.22-3.01a2989 2989 0 0 1-2.25-20.12 238 238 0 0 1-.67-43.85"}),c.createElement("path",{fill:"#FE6D04",d:"m234.86 902.81.66 6.55a22.5 22.5 0 0 1-9.8-3.22q.58-.67.46-1.8a21 21 0 0 1 8.68-1.53m23.85.3c8.16 2.43 4.59 14.2-2.59 12.05-5.36-1.59-5.53-8.44-1.26-11.44a7 7 0 0 1 3.85-.61m-60.64 1.77-.4 4.75q-.56 1.06-1.67 1.76a20 20 0 0 0-4.28 1.99 1 1 0 0 1-.87.08l-1.27-8.19q3.97-1.14 8.49-.39"}),c.createElement("path",{fill:"#478FFF",d:"M225.72 906.14a22.5 22.5 0 0 0 9.8 3.22 144 144 0 0 0 4.85 3.02q4.25 2.53 9.3 9.8-1.2 23.42.15 46.62l-57.03 1.02-6.54.34q-1.9.1-1.78-1.79l3.19-49.9q2.19-1.43 3.43-3.53c1.59-1.54 3.34-1.43 4.91-3.55a4.6 4.6 0 0 0 1.67-1.76q2.04-.46 8.08-2.64 8.8-3.17 19.97-.85"}),c.createElement("path",{fill:"#9DCBFE",d:"M596.26 905.95q8.44 4.26 12.02 11.52 4.28 8.72.58 18.38l-34.97-.09a.7.7 0 0 1-.71-.6 1 1 0 0 1 .02-.34l8.59-27.77q.38-1.2 1.64-1.2z"}),c.createElement("path",{fill:"#FAF9F9",d:"M608.86 935.85q3.7-9.66-.58-18.38-3.59-7.26-12.02-11.52l29.57-.11a.8.8 0 0 1 .63.32.8.8 0 0 1 .12.69l-8.63 27.93a1.4 1.4 0 0 1-1.35 1z"}),c.createElement("path",{fill:"#1350CE",d:"M959.01 919.75a6.77 6.77 0 1 0 0-13.54 6.77 6.77 0 0 0 0 13.54"}),c.createElement("path",{fill:"#FDCB7D",d:"M196 911.39c-1.57 2.12-3.32 2.01-4.91 3.55a3 3 0 0 0-.24-1.48q.46.17.87-.08a20 20 0 0 1 4.28-1.99"}),c.createElement("path",{fill:"#FAF9F9",d:"m394.81 937.77 4.54-24.04q1-1.46 1.22 1.23.07.8.01 1.59a234 234 0 0 0-.49 20.7.9.9 0 0 1-1 .92z"}),c.createElement("path",{fill:"#62A1FF",d:"m187.66 918.47-3.19 49.9q-.12 1.9 1.78 1.79l6.54-.34-.08.49-55.25-.22a.75.75 0 0 1-.75-.76q.15-14.95 14.77-20.35 4.07-1.5 8.73-1.5a1.7 1.7 0 0 0 1.6-1.09 25.8 25.8 0 0 1 12.26-13.58c1.78-.95 7.06-1.7 7.98-4.56a27 27 0 0 1 5.61-9.78"}),c.createElement("path",{fill:"#377DFE",d:"m249.67 922.18 1.75 2.43a.8.8 0 0 0 .56.31.8.8 0 0 0 .6-.21 13.8 13.8 0 0 1 7.41-3.69q6.9-.81 12.45 2.26c6.78 5.54 9.94 14.57 4.94 22.39q-.36.57-.33.97.04.52.41 1.01.23 1.3 2.01 2.47.4.27.48.76l2.49 18.01q.07.5.56.52l13.8.89-27.11.11q-.07-1.5-3.71-1.61a272 272 0 0 0-16.16 0q-1.35-23.2-.15-46.62"}),c.createElement("path",{fill:"#2466F8",d:"M330.48 941.25a6.55 6.55 0 1 0 0-13.1 6.55 6.55 0 0 0 0 13.1"}),c.createElement("path",{fill:"#9DCBFE",d:"M654.7 948.96a8.59 8.59 0 1 0 0-17.18 8.59 8.59 0 0 0 0 17.18"}),c.createElement("path",{fill:"#1350CE",d:"M916.32 932.57q16.44 5.98 25.29 21.76a13 13 0 0 1-3.8-3.44 91 91 0 0 0-3.01-4.02c-2.56-2.73-4.86-5.37-8.01-7.48l-4.86-3.23q-3.3-1.34-5.61-3.59"}),c.createElement("path",{fill:"#2466F8",d:"M941.61 954.33q1.1 1.43 1.87 3.75a1.1 1.1 0 0 0 1.11.75q.25-.02.46-.14c1.85-1.05 3.46-2.52 5.4-3.36q11.06-4.78 21.31.61c5.26 2.76 9 7.85 9.47 13.85a.62.62 0 0 1-.62.67l-99.82-.14q6.37-1.13 10.36-1.14l25.34-.17q.5 0 .77-.43a77 77 0 0 0 4.37-7.73c4.1-8.34 2.92-16.16.3-24.69l4.86 3.23c3.15 2.11 5.45 4.75 8.01 7.48q.35.38 3.01 4.02a13 13 0 0 0 3.8 3.44"}),c.createElement("path",{fill:"#62A1FF",d:"M625.08 944.34c7.26 5.91 8.82 15.17 8.24 23.76a2.2 2.2 0 0 1-.68 1.43 2.1 2.1 0 0 1-1.48.57l-13.92-.09a236 236 0 0 1 7.84-25.67"}),c.createElement("path",{fill:"#9DCBFE",d:"M469.07 947.3q.76 1.15-2.14 1.44-3.87.38-18.18-.27c5.56-4.63 14.19-5.61 20.32-1.17m138.66 22.92-16.72-.05-1.03-.65a.9.9 0 0 1-.43-.74c-.39-7.25-3.68-15.06-10.72-18.11q-2.1-.9-9.2-3.36 0-1.4.77-2.19.35-.36.85-.36l42.41-.15q1.47 0 1.06 1.42z"}),c.createElement("path",{fill:"#1350CE",d:"M127.69 960.23a6.54 6.54 0 1 0 0-13.08 6.54 6.54 0 0 0 0 13.08"}),c.createElement("path",{fill:"#B4D9FD",d:"m591.01 970.17-28.27.05 6.89-22.91q7.1 2.44 9.2 3.36c7.04 3.05 10.33 10.86 10.72 18.11a.9.9 0 0 0 .43.74z"}),c.createElement("path",{fill:"#2466F8",d:"M277.46 947.65q3.72 1.23 4.6 1.77a35 35 0 0 1 11.65 11.59q.75 1.23 1.94.41c7.52-5.2 19.43-2.34 20.75 7.93q.15 1.2-1.04 1.17l-18.56-.22-13.8-.89q-.49-.04-.56-.52l-2.49-18.01a1 1 0 0 0-.48-.76q-1.79-1.17-2.01-2.47"}),c.createElement("path",{fill:"#FDCB7D",d:"M387.71 950.36q1.84.42 3.58 1.37.57.3 1.48-.01-1.28.45-1.76 2.51c-.64 2.74.23 6.33 3.37 6.92a22 22 0 0 1 3.09.83q3.32 1.17-.11 1.94-3.97.88-7.82-1.57a3 3 0 0 0-1.01-.43l-2.02-.5a1.5 1.5 0 0 1-.94-.73 1.5 1.5 0 0 1-.09-1.19 22 22 0 0 0 1.29-8.22q-.03-.54.47-.74zm41.47 1.89q-2.03 9.67 6.68 9.79 ********** 1.15-.04.2-.15.38a1.3 1.3 0 0 1-.83.66q-4.75 1.24-10.59-1.39a.75.75 0 0 1-.47-.78l.61-9.74a6.7 6.7 0 0 1 3.86-.07"}),c.createElement("path",{fill:"#1350CE",d:"m269.69 970.41-76.98-.1.08-.49 57.03-1.02q8-.24 16.16 0 3.64.1 3.71 1.61"}),c.createElement("g",{clipPath:"url(#a)"},c.createElement("path",{fill:"url(#b)",d:"m515.29 249.81 11.74 11.55a2.74 2.74 0 0 1 0 3.92 2.85 2.85 0 0 1-4 0l-7.74-7.62a2.85 2.85 0 0 0-4 0l-19.48 19.17a2.74 2.74 0 0 0 0 3.93l19.49 19.16a2.85 2.85 0 0 0 3.99 0l7.75-7.62a2.85 2.85 0 0 1 3.99 0 2.74 2.74 0 0 1 0 3.92l-11.74 11.55a2.85 2.85 0 0 1-4 0l-27.46-27.01a2.74 2.74 0 0 1 0-3.93l27.47-27.02a2.86 2.86 0 0 1 3.99 0"}),c.createElement("path",{fill:"url(#c)",d:"M531.02 269.2a2.85 2.85 0 0 1 3.99 0l7.75 7.63a2.74 2.74 0 0 1 0 3.93l-7.75 7.62a2.85 2.85 0 0 1-4 0 2.74 2.74 0 0 1 0-3.93l3.77-3.7a2.74 2.74 0 0 0 0-3.92l-3.76-3.7a2.74 2.74 0 0 1 0-3.92"}),c.createElement("path",{fill:"#61DAFB",d:"M512.69 279.71c0 .********** 1.06q.46.43 1.09.44a1.6 1.6 0 0 0 1.08-.44 1.5 1.5 0 0 0 0-2.11 1.55 1.55 0 0 0-2.17 0c-.28.28-.44.66-.44 1.05"}),c.createElement("path",{fill:"#61DAFB",d:"M526.06 279.35c0-1.74-1.95-3.25-4.98-4.16.76-3.2.5-5.73-.94-6.6s-3.66.15-5.92 2.45c-2.27-2.3-4.49-3.32-5.92-2.45-1.44.87-1.71 3.4-.95 6.6-3.03.92-4.97 2.42-4.97 4.16s1.94 3.25 4.97 4.16c-.76 3.2-.5 5.73.95 6.6a2.3 2.3 0 0 0 1.19.32c1.05 0 2.32-.6 3.7-1.78q.5-.45 1.03-.97.52.52 1.03.97c1.38 1.17 2.65 1.78 3.7 1.78q.65 0 1.19-.32c1.44-.87 1.7-3.4.94-6.6 3.03-.91 4.98-2.42 4.98-4.16m-6.43-9.82c.93.56 1.12 2.61.46 5.4a23 23 0 0 0-3.15-.52 25 25 0 0 0-2-2.6c1.56-1.59 3.01-2.45 4.01-2.45a1.3 1.3 0 0 1 .68.17m-2.11 11.82q-.56 1-1.14 1.91a27 27 0 0 1-4.32 0 30 30 0 0 1-2.15-3.9 32 32 0 0 1 1.01-2 31 31 0 0 1 1.15-1.92 27 27 0 0 1 2.15-.09q1.13 0 2.15.09a31 31 0 0 1 1.15 1.9 32 32 0 0 1 1 2 32 32 0 0 1-1 2.01m1.55-.73q.44 1.1.74 2.12-.99.23-2.12.39a33 33 0 0 0 1.38-2.51m-4.85 5.5a24 24 0 0 1-1.38-1.73 28 28 0 0 0 2.76 0 24 24 0 0 1-1.38 1.74m-3.47-3a22 22 0 0 1-2.12-.38q.3-1.01.74-2.12a33 33 0 0 0 1.38 2.5m-1.38-5.04a24 24 0 0 1-.74-2.12 22 22 0 0 1 2.11-.38 30 30 0 0 0-1.37 2.5m4.85-5.5q.68.77 1.37 1.73l-1.37-.04q-.7 0-1.38.04.69-.95 1.38-1.74m4.19 4.23-.72-1.23q1.13.15 2.12.38a24 24 0 0 1-.74 2.12zm-9.6-7.28q.3-.18.68-.17c1 0 2.44.86 4 2.45a26 26 0 0 0-2 2.6 23 23 0 0 0-3.14.52c-.67-2.8-.47-4.84.46-5.4m-5.4 9.82c0-1.13 1.59-2.33 4.22-3.12q.45 1.5 1.15 3.12a26 26 0 0 0-1.15 3.12c-2.63-.79-4.22-2-4.22-3.12m9.13 8.46c-1.58 1.35-2.94 1.84-3.73 1.36-.93-.56-1.13-2.6-.46-5.4q1.45.37 3.15.52a26 26 0 0 0 2 2.61q-.48.5-.96.91m7.09 1.36c-.8.48-2.15-.01-3.73-1.36a17 17 0 0 1-.96-.9 26 26 0 0 0 2-2.62 23 23 0 0 0 3.15-.51c.66 2.78.46 4.83-.46 5.4m1.18-6.7a26 26 0 0 0-1.15-3.12q.7-1.61 1.15-3.12c2.63.8 4.22 2 4.22 3.12s-1.6 2.33-4.22 3.12"})),c.createElement("defs",null,c.createElement("linearGradient",{id:"b",x1:537.66,x2:479.35,y1:278.79,y2:278.79,gradientUnits:"userSpaceOnUse"},c.createElement("stop",{stopColor:"#47B4E0"}),c.createElement("stop",{offset:.17,stopColor:"#1588E0"}),c.createElement("stop",{offset:1,stopColor:"#6EB4E0"})),c.createElement("linearGradient",{id:"c",x1:536.89,x2:536.89,y1:289.19,y2:268.4,gradientUnits:"userSpaceOnUse"},c.createElement("stop",{offset:.03,stopColor:"#F0776F"}),c.createElement("stop",{offset:.5,stopColor:"#F0656F"}),c.createElement("stop",{offset:1,stopColor:"#F0606F"})),c.createElement("clipPath",{id:"a"},c.createElement("path",{fill:"#fff",d:"M483 249h61v60h-61z"})))),we=c.createContext({});function Ve(t){const e=c.useRef(null);return e.current===null&&(e.current=t()),e.current}const Be=typeof window<"u",j1=Be?c.useLayoutEffect:c.useEffect,Gt=c.createContext(null),Re=c.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class wi extends c.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const s=n.offsetParent,i=s instanceof HTMLElement&&s.offsetWidth||0,r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Vi({children:t,isPresent:e,anchorX:n}){const s=c.useId(),i=c.useRef(null),r=c.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=c.useContext(Re);return c.useInsertionEffect(()=>{const{width:o,height:l,top:u,left:h,right:f}=r.current;if(e||!i.current||!o||!l)return;const d=n==="left"?`left: ${h}`:`right: ${f}`;i.current.dataset.motionPopId=s;const m=document.createElement("style");return a&&(m.nonce=a),document.head.appendChild(m),m.sheet&&m.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${l}px !important;
            ${d}px !important;
            top: ${u}px !important;
          }
        `),()=>{document.head.removeChild(m)}},[e]),C.jsx(wi,{isPresent:e,childRef:i,sizeRef:r,children:c.cloneElement(t,{ref:i})})}const Bi=({children:t,initial:e,isPresent:n,onExitComplete:s,custom:i,presenceAffectsLayout:r,mode:a,anchorX:o})=>{const l=Ve(Ri),u=c.useId(),h=c.useCallback(d=>{l.set(d,!0);for(const m of l.values())if(!m)return;s&&s()},[l,s]),f=c.useMemo(()=>({id:u,initial:e,isPresent:n,custom:i,onExitComplete:h,register:d=>(l.set(d,!1),()=>l.delete(d))}),r?[Math.random(),h]:[n,h]);return c.useMemo(()=>{l.forEach((d,m)=>l.set(m,!1))},[n]),c.useEffect(()=>{!n&&!l.size&&s&&s()},[n]),a==="popLayout"&&(t=C.jsx(Vi,{isPresent:n,anchorX:o,children:t})),C.jsx(Gt.Provider,{value:f,children:t})};function Ri(){return new Map}function I1(t=!0){const e=c.useContext(Gt);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:s,register:i}=e,r=c.useId();c.useEffect(()=>{if(t)return i(r)},[t]);const a=c.useCallback(()=>t&&s&&s(r),[r,s,t]);return!n&&s?[!1,a]:[!0]}const Rt=t=>t.key||"";function yn(t){const e=[];return c.Children.forEach(t,n=>{c.isValidElement(n)&&e.push(n)}),e}const Li=({children:t,custom:e,initial:n=!0,onExitComplete:s,presenceAffectsLayout:i=!0,mode:r="sync",propagate:a=!1,anchorX:o="left"})=>{const[l,u]=I1(a),h=c.useMemo(()=>yn(t),[t]),f=a&&!l?[]:h.map(Rt),d=c.useRef(!0),m=c.useRef(h),p=Ve(()=>new Map),[g,v]=c.useState(h),[y,x]=c.useState(h);j1(()=>{d.current=!1,m.current=h;for(let q=0;q<y.length;q++){const M=Rt(y[q]);f.includes(M)?p.delete(M):p.get(M)!==!0&&p.set(M,!1)}},[y,f.length,f.join("-")]);const E=[];if(h!==g){let q=[...h];for(let M=0;M<y.length;M++){const D=y[M],P=Rt(D);f.includes(P)||(q.splice(M,0,D),E.push(D))}return r==="wait"&&E.length&&(q=E),x(yn(q)),v(h),null}const{forceRender:F}=c.useContext(we);return C.jsx(C.Fragment,{children:y.map(q=>{const M=Rt(q),D=a&&!l?!1:h===y||f.includes(M),P=()=>{if(p.has(M))p.set(M,!0);else return;let V=!0;p.forEach(I=>{I||(V=!1)}),V&&(F==null||F(),x(m.current),a&&(u==null||u()),s&&s())};return C.jsx(Bi,{isPresent:D,initial:!d.current||n?void 0:!1,custom:e,presenceAffectsLayout:i,mode:r,onExitComplete:D?void 0:P,anchorX:o,children:q},M)})})},O=t=>t;let O1=O;const ki={useManualTiming:!1},Lt=["read","resolveKeyframes","update","preRender","render","postRender"],vn={value:null};function ji(t,e){let n=new Set,s=new Set,i=!1,r=!1;const a=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(f){a.has(f)&&(h.schedule(f),t()),l++,f(o)}const h={schedule:(f,d=!1,m=!1)=>{const g=m&&i?n:s;return d&&a.add(f),g.has(f)||g.add(f),f},cancel:f=>{s.delete(f),a.delete(f)},process:f=>{if(o=f,i){r=!0;return}i=!0,[n,s]=[s,n],n.forEach(u),e&&vn.value&&vn.value.frameloop[e].push(l),l=0,n.clear(),i=!1,r&&(r=!1,h.process(f))}};return h}const Ii=40;function z1(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=()=>n=!0,a=Lt.reduce((y,x)=>(y[x]=ji(r,e?x:void 0),y),{}),{read:o,resolveKeyframes:l,update:u,preRender:h,render:f,postRender:d}=a,m=()=>{const y=performance.now();n=!1,i.delta=s?1e3/60:Math.max(Math.min(y-i.timestamp,Ii),1),i.timestamp=y,i.isProcessing=!0,o.process(i),l.process(i),u.process(i),h.process(i),f.process(i),d.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(m))},p=()=>{n=!0,s=!0,i.isProcessing||t(m)};return{schedule:Lt.reduce((y,x)=>{const E=a[x];return y[x]=(F,q=!1,M=!1)=>(n||p(),E.schedule(F,q,M)),y},{}),cancel:y=>{for(let x=0;x<Lt.length;x++)a[Lt[x]].cancel(y)},state:i,steps:a}}const{schedule:A,cancel:Y,state:B,steps:te}=z1(typeof requestAnimationFrame<"u"?requestAnimationFrame:O,!0),N1=c.createContext({strict:!1}),qn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ht={};for(const t in qn)ht[t]={isEnabled:e=>qn[t].some(n=>!!e[n])};function Oi(t){for(const e in t)ht[e]={...ht[e],...t[e]}}const zi=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Nt(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||zi.has(t)}let U1=t=>!Nt(t);function Ni(t){t&&(U1=e=>e.startsWith("on")?!Nt(e):t(e))}try{Ni(require("@emotion/is-prop-valid").default)}catch{}function Ui(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(U1(i)||n===!0&&Nt(i)||!e&&!Nt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function _i(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...s)=>t(...s);return new Proxy(n,{get:(s,i)=>i==="create"?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const Ht=c.createContext({});function Xt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function Mt(t){return typeof t=="string"||Array.isArray(t)}const Le=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ke=["initial",...Le];function Yt(t){return Xt(t.animate)||ke.some(e=>Mt(t[e]))}function _1(t){return!!(Yt(t)||t.variants)}function Ki(t,e){if(Yt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Mt(n)?n:void 0,animate:Mt(s)?s:void 0}}return t.inherit!==!1?e:{}}function Wi(t){const{initial:e,animate:n}=Ki(t,c.useContext(Ht));return c.useMemo(()=>({initial:e,animate:n}),[xn(e),xn(n)])}function xn(t){return Array.isArray(t)?t.join(" "):t}const $i=Symbol.for("motionComponentSymbol");function rt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Gi(t,e,n){return c.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):rt(n)&&(n.current=s))},[e])}const{schedule:je}=z1(queueMicrotask,!1),Ie=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Hi="framerAppearId",K1="data-"+Ie(Hi),W1=c.createContext({});function Xi(t,e,n,s,i){var r,a;const{visualElement:o}=c.useContext(Ht),l=c.useContext(N1),u=c.useContext(Gt),h=c.useContext(Re).reducedMotion,f=c.useRef(null);s=s||l.renderer,!f.current&&s&&(f.current=s(t,{visualState:e,parent:o,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:h}));const d=f.current,m=c.useContext(W1);d&&!d.projection&&i&&(d.type==="html"||d.type==="svg")&&Yi(f.current,n,i,m);const p=c.useRef(!1);c.useInsertionEffect(()=>{d&&p.current&&d.update(n,u)});const g=n[K1],v=c.useRef(!!g&&!(!((r=window.MotionHandoffIsComplete)===null||r===void 0)&&r.call(window,g))&&((a=window.MotionHasOptimisedAnimation)===null||a===void 0?void 0:a.call(window,g)));return j1(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),je.render(d.render),v.current&&d.animationState&&d.animationState.animateChanges())}),c.useEffect(()=>{d&&(!v.current&&d.animationState&&d.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var y;(y=window.MotionHandoffMarkAsComplete)===null||y===void 0||y.call(window,g)}),v.current=!1))}),d}function Yi(t,e,n,s){const{layoutId:i,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:$1(t.parent)),t.projection.setOptions({layoutId:i,layout:r,alwaysMeasureLayout:!!a||o&&rt(o),visualElement:t,animationType:typeof r=="string"?r:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:u})}function $1(t){if(t)return t.options.allowProjection!==!1?t.projection:$1(t.parent)}function Zi({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){var r,a;t&&Oi(t);function o(u,h){let f;const d={...c.useContext(Re),...u,layoutId:Ji(u)},{isStatic:m}=d,p=Wi(u),g=s(u,m);if(!m&&Be){Qi();const v=ta(d);f=v.MeasureLayout,p.visualElement=Xi(i,g,d,e,v.ProjectionNode)}return C.jsxs(Ht.Provider,{value:p,children:[f&&p.visualElement?C.jsx(f,{visualElement:p.visualElement,...d}):null,n(i,u,Gi(g,p.visualElement,h),g,m,p.visualElement)]})}o.displayName=`motion.${typeof i=="string"?i:`create(${(a=(r=i.displayName)!==null&&r!==void 0?r:i.name)!==null&&a!==void 0?a:""})`}`;const l=c.forwardRef(o);return l[$i]=i,l}function Ji({layoutId:t}){const e=c.useContext(we).id;return e&&t!==void 0?e+"-"+t:t}function Qi(t,e){c.useContext(N1).strict}function ta(t){const{drag:e,layout:n}=ht;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const G1=t=>e=>typeof e=="string"&&e.startsWith(t),Oe=G1("--"),ea=G1("var(--"),ze=t=>ea(t)?na.test(t.split("/*")[0].trim()):!1,na=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Pt={};function sa(t){for(const e in t)Pt[e]=t[e],Oe(e)&&(Pt[e].isCSSVariable=!0)}const mt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],it=new Set(mt);function H1(t,{layout:e,layoutId:n}){return it.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Pt[t]||t==="opacity")}const L=t=>!!(t&&t.getVelocity),X1=(t,e)=>e&&typeof t=="number"?e.transform(t):t,H=(t,e,n)=>n>e?e:n<t?t:n,pt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Ct={...pt,transform:t=>H(0,1,t)},kt={...pt,default:1},bt=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),X=bt("deg"),_=bt("%"),T=bt("px"),ia=bt("vh"),aa=bt("vw"),En={..._,parse:t=>_.parse(t)/100,transform:t=>_.transform(t*100)},ra={borderWidth:T,borderTopWidth:T,borderRightWidth:T,borderBottomWidth:T,borderLeftWidth:T,borderRadius:T,radius:T,borderTopLeftRadius:T,borderTopRightRadius:T,borderBottomRightRadius:T,borderBottomLeftRadius:T,width:T,maxWidth:T,height:T,maxHeight:T,top:T,right:T,bottom:T,left:T,padding:T,paddingTop:T,paddingRight:T,paddingBottom:T,paddingLeft:T,margin:T,marginTop:T,marginRight:T,marginBottom:T,marginLeft:T,backgroundPositionX:T,backgroundPositionY:T},oa={rotate:X,rotateX:X,rotateY:X,rotateZ:X,scale:kt,scaleX:kt,scaleY:kt,scaleZ:kt,skew:X,skewX:X,skewY:X,distance:T,translateX:T,translateY:T,translateZ:T,x:T,y:T,z:T,perspective:T,transformPerspective:T,opacity:Ct,originX:En,originY:En,originZ:T},Tn={...pt,transform:Math.round},Ne={...ra,...oa,zIndex:Tn,size:T,fillOpacity:Ct,strokeOpacity:Ct,numOctaves:Tn},la={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ca=mt.length;function ua(t,e,n){let s="",i=!0;for(let r=0;r<ca;r++){const a=mt[r],o=t[a];if(o===void 0)continue;let l=!0;if(typeof o=="number"?l=o===(a.startsWith("scale")?1:0):l=parseFloat(o)===0,!l||n){const u=X1(o,Ne[a]);if(!l){i=!1;const h=la[a]||a;s+=`${h}(${u}) `}n&&(e[a]=u)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function Ue(t,e,n){const{style:s,vars:i,transformOrigin:r}=t;let a=!1,o=!1;for(const l in e){const u=e[l];if(it.has(l)){a=!0;continue}else if(Oe(l)){i[l]=u;continue}else{const h=X1(u,Ne[l]);l.startsWith("origin")?(o=!0,r[l]=h):s[l]=h}}if(e.transform||(a||n?s.transform=ua(e,t.transform,n):s.transform&&(s.transform="none")),o){const{originX:l="50%",originY:u="50%",originZ:h=0}=r;s.transformOrigin=`${l} ${u} ${h}`}}const _e=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Y1(t,e,n){for(const s in e)!L(e[s])&&!H1(s,n)&&(t[s]=e[s])}function ha({transformTemplate:t},e){return c.useMemo(()=>{const n=_e();return Ue(n,e,t),Object.assign({},n.vars,n.style)},[e])}function fa(t,e){const n=t.style||{},s={};return Y1(s,n,t),Object.assign(s,ha(t,e)),s}function da(t,e){const n={},s=fa(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const ma=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ke(t){return typeof t!="string"||t.includes("-")?!1:!!(ma.indexOf(t)>-1||/[A-Z]/u.test(t))}const pa={offset:"stroke-dashoffset",array:"stroke-dasharray"},ga={offset:"strokeDashoffset",array:"strokeDasharray"};function ya(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?pa:ga;t[r.offset]=T.transform(-s);const a=T.transform(e),o=T.transform(n);t[r.array]=`${a} ${o}`}function Mn(t,e,n){return typeof t=="string"?t:T.transform(e+n*t)}function va(t,e,n){const s=Mn(e,t.x,t.width),i=Mn(n,t.y,t.height);return`${s} ${i}`}function We(t,{attrX:e,attrY:n,attrScale:s,originX:i,originY:r,pathLength:a,pathSpacing:o=1,pathOffset:l=0,...u},h,f){if(Ue(t,u,f),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:m,dimensions:p}=t;d.transform&&(p&&(m.transform=d.transform),delete d.transform),p&&(i!==void 0||r!==void 0||m.transform)&&(m.transformOrigin=va(p,i!==void 0?i:.5,r!==void 0?r:.5)),e!==void 0&&(d.x=e),n!==void 0&&(d.y=n),s!==void 0&&(d.scale=s),a!==void 0&&ya(d,a,o,l,!1)}const Z1=()=>({..._e(),attrs:{}}),$e=t=>typeof t=="string"&&t.toLowerCase()==="svg";function qa(t,e,n,s){const i=c.useMemo(()=>{const r=Z1();return We(r,e,$e(s),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){const r={};Y1(r,t.style,t),i.style={...r,...i.style}}return i}function xa(t=!1){return(n,s,i,{latestValues:r},a)=>{const l=(Ke(n)?qa:da)(s,r,a,n),u=Ui(s,typeof n=="string",t),h=n!==c.Fragment?{...u,...l,ref:i}:{},{children:f}=s,d=c.useMemo(()=>L(f)?f.get():f,[f]);return c.createElement(n,{...h,children:d})}}function Pn(t){const e=[{},{}];return t==null||t.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function Ge(t,e,n,s){if(typeof e=="function"){const[i,r]=Pn(s);e=e(n!==void 0?n:t.custom,i,r)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,r]=Pn(s);e=e(n!==void 0?n:t.custom,i,r)}return e}const fe=t=>Array.isArray(t),Ea=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),Ta=t=>fe(t)?t[t.length-1]||0:t;function It(t){const e=L(t)?t.get():t;return Ea(e)?e.toValue():e}function Ma({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:n},s,i,r){const a={latestValues:Pa(s,i,r,t),renderState:e()};return n&&(a.onMount=o=>n({props:s,current:o,...a}),a.onUpdate=o=>n(o)),a}const J1=t=>(e,n)=>{const s=c.useContext(Ht),i=c.useContext(Gt),r=()=>Ma(t,e,s,i);return n?r():Ve(r)};function Pa(t,e,n,s){const i={},r=s(t,{});for(const d in r)i[d]=It(r[d]);let{initial:a,animate:o}=t;const l=Yt(t),u=_1(t);e&&u&&!l&&t.inherit!==!1&&(a===void 0&&(a=e.initial),o===void 0&&(o=e.animate));let h=n?n.initial===!1:!1;h=h||a===!1;const f=h?o:a;if(f&&typeof f!="boolean"&&!Xt(f)){const d=Array.isArray(f)?f:[f];for(let m=0;m<d.length;m++){const p=Ge(t,d[m]);if(p){const{transitionEnd:g,transition:v,...y}=p;for(const x in y){let E=y[x];if(Array.isArray(E)){const F=h?E.length-1:0;E=E[F]}E!==null&&(i[x]=E)}for(const x in g)i[x]=g[x]}}}return i}function He(t,e,n){var s;const{style:i}=t,r={};for(const a in i)(L(i[a])||e.style&&L(e.style[a])||H1(a,t)||((s=n==null?void 0:n.getValue(a))===null||s===void 0?void 0:s.liveStyle)!==void 0)&&(r[a]=i[a]);return r}const Ca={useVisualState:J1({scrapeMotionValuesFromProps:He,createRenderState:_e})};function Q1(t,e){try{e.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{e.dimensions={x:0,y:0,width:0,height:0}}}function ts(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const r in n)t.style.setProperty(r,n[r])}const es=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ns(t,e,n,s){ts(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(es.has(i)?i:Ie(i),e.attrs[i])}function ss(t,e,n){const s=He(t,e,n);for(const i in t)if(L(t[i])||L(e[i])){const r=mt.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[r]=t[i]}return s}const Cn=["x","y","width","height","cx","cy","r"],Fa={useVisualState:J1({scrapeMotionValuesFromProps:ss,createRenderState:Z1,onUpdate:({props:t,prevProps:e,current:n,renderState:s,latestValues:i})=>{if(!n)return;let r=!!t.drag;if(!r){for(const o in i)if(it.has(o)){r=!0;break}}if(!r)return;let a=!e;if(e)for(let o=0;o<Cn.length;o++){const l=Cn[o];t[l]!==e[l]&&(a=!0)}a&&A.read(()=>{Q1(n,s),A.render(()=>{We(s,i,$e(n.tagName),t.transformTemplate),ns(n,s)})})}})};function Aa(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){const a={...Ke(s)?Fa:Ca,preloadedFeatures:t,useRender:xa(i),createVisualElement:e,Component:s};return Zi(a)}}function Ft(t,e,n){const s=t.getProps();return Ge(s,e,n!==void 0?n:s.custom,t)}function Xe(t,e){return t?t[e]||t.default||t:void 0}const is=new Set(["width","height","top","left","right","bottom",...mt]);function Ye(t,e){t.indexOf(e)===-1&&t.push(e)}function Ze(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class Je{constructor(){this.subscriptions=[]}add(e){return Ye(this.subscriptions,e),()=>Ze(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const a=this.subscriptions[r];a&&a(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function as(t,e){return e?t*(1e3/e):0}let Ot;function Da(){Ot=void 0}const K={now:()=>(Ot===void 0&&K.set(B.isProcessing||ki.useManualTiming?B.timestamp:performance.now()),Ot),set:t=>{Ot=t,queueMicrotask(Da)}},Fn=30,Sa=t=>!isNaN(parseFloat(t));class ba{constructor(e,n={}){this.version="12.6.0",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{const r=K.now();this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=K.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Sa(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Je);const s=this.events[e].add(n);return e==="change"?()=>{s(),A.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=K.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Fn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Fn);return as(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function At(t,e){return new ba(t,e)}function wa(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,At(n))}function Va(t,e){const n=Ft(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const a in r){const o=Ta(r[a]);wa(t,a,o)}}function Ba(t){return!!(L(t)&&t.add)}function de(t,e){const n=t.getValue("willChange");if(Ba(n))return n.add(e)}function rs(t){return t.props[K1]}function Qe(t){let e;return()=>(e===void 0&&(e=t()),e)}const Ra=Qe(()=>window.ScrollTimeline!==void 0);class La{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,n){for(let s=0;s<this.animations.length;s++)this.animations[s][e]=n}attachTimeline(e,n){const s=this.animations.map(i=>{if(Ra()&&i.attachTimeline)return i.attachTimeline(e);if(typeof n=="function")return n(i)});return()=>{s.forEach((i,r)=>{i&&i(),this.animations[r].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let n=0;n<this.animations.length;n++)e=Math.max(e,this.animations[n].duration);return e}runAll(e){this.animations.forEach(n=>n[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class ka extends La{then(e,n){return Promise.all(this.animations).then(e).catch(n)}}const $=t=>t*1e3,G=t=>t/1e3;function tn(t){return typeof t=="function"}function An(t,e){t.timeline=e,t.onfinish=null}const en=t=>Array.isArray(t)&&typeof t[0]=="number",ja={linearEasing:void 0};function Ia(t,e){const n=Qe(t);return()=>{var s;return(s=ja[e])!==null&&s!==void 0?s:n()}}const Ut=Ia(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),ft=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},os=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let r=0;r<i;r++)s+=t(ft(0,i-1,r))+", ";return`linear(${s.substring(0,s.length-2)})`};function ls(t){return!!(typeof t=="function"&&Ut()||!t||typeof t=="string"&&(t in me||Ut())||en(t)||Array.isArray(t)&&t.every(ls))}const yt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,me={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:yt([0,.65,.55,1]),circOut:yt([.55,0,1,.45]),backIn:yt([.31,.01,.66,-.59]),backOut:yt([.33,1.53,.69,.99])};function cs(t,e){if(t)return typeof t=="function"&&Ut()?os(t,e):en(t)?yt(t):Array.isArray(t)?t.map(n=>cs(n,e)||me.easeOut):me[t]}const us=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Oa=1e-7,za=12;function Na(t,e,n,s,i){let r,a,o=0;do a=e+(n-e)/2,r=us(a,s,i)-t,r>0?n=a:e=a;while(Math.abs(r)>Oa&&++o<za);return a}function wt(t,e,n,s){if(t===e&&n===s)return O;const i=r=>Na(r,0,1,t,n);return r=>r===0||r===1?r:us(i(r),e,s)}const hs=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,fs=t=>e=>1-t(1-e),ds=wt(.33,1.53,.69,.99),nn=fs(ds),ms=hs(nn),ps=t=>(t*=2)<1?.5*nn(t):.5*(2-Math.pow(2,-10*(t-1))),sn=t=>1-Math.sin(Math.acos(t)),gs=fs(sn),ys=hs(sn),vs=t=>/^0[^.\s]+$/u.test(t);function Ua(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||vs(t):!0}const qt=t=>Math.round(t*1e5)/1e5,an=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function _a(t){return t==null}const Ka=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,rn=(t,e)=>n=>!!(typeof n=="string"&&Ka.test(n)&&n.startsWith(t)||e&&!_a(n)&&Object.prototype.hasOwnProperty.call(n,e)),qs=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,r,a,o]=s.match(an);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(a),alpha:o!==void 0?parseFloat(o):1}},Wa=t=>H(0,255,t),ee={...pt,transform:t=>Math.round(Wa(t))},et={test:rn("rgb","red"),parse:qs("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+ee.transform(t)+", "+ee.transform(e)+", "+ee.transform(n)+", "+qt(Ct.transform(s))+")"};function $a(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const pe={test:rn("#"),parse:$a,transform:et.transform},ot={test:rn("hsl","hue"),parse:qs("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+_.transform(qt(e))+", "+_.transform(qt(n))+", "+qt(Ct.transform(s))+")"},R={test:t=>et.test(t)||pe.test(t)||ot.test(t),parse:t=>et.test(t)?et.parse(t):ot.test(t)?ot.parse(t):pe.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?et.transform(t):ot.transform(t)},Ga=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Ha(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(an))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(Ga))===null||n===void 0?void 0:n.length)||0)>0}const xs="number",Es="color",Xa="var",Ya="var(",Dn="${}",Za=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Dt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const o=e.replace(Za,l=>(R.test(l)?(s.color.push(r),i.push(Es),n.push(R.parse(l))):l.startsWith(Ya)?(s.var.push(r),i.push(Xa),n.push(l)):(s.number.push(r),i.push(xs),n.push(parseFloat(l))),++r,Dn)).split(Dn);return{values:n,split:o,indexes:s,types:i}}function Ts(t){return Dt(t).values}function Ms(t){const{split:e,types:n}=Dt(t),s=e.length;return i=>{let r="";for(let a=0;a<s;a++)if(r+=e[a],i[a]!==void 0){const o=n[a];o===xs?r+=qt(i[a]):o===Es?r+=R.transform(i[a]):r+=i[a]}return r}}const Ja=t=>typeof t=="number"?0:t;function Qa(t){const e=Ts(t);return Ms(t)(e.map(Ja))}const Z={test:Ha,parse:Ts,createTransformer:Ms,getAnimatableNone:Qa},tr=new Set(["brightness","contrast","saturate","opacity"]);function er(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(an)||[];if(!s)return t;const i=n.replace(s,"");let r=tr.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const nr=/\b([a-z-]*)\(.*?\)/gu,ge={...Z,getAnimatableNone:t=>{const e=t.match(nr);return e?e.map(er).join(" "):t}},sr={...Ne,color:R,backgroundColor:R,outlineColor:R,fill:R,stroke:R,borderColor:R,borderTopColor:R,borderRightColor:R,borderBottomColor:R,borderLeftColor:R,filter:ge,WebkitFilter:ge},Ps=t=>sr[t];function Cs(t,e){let n=Ps(t);return n!==ge&&(n=Z),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const ir=new Set(["auto","none","0"]);function ar(t,e,n){let s=0,i;for(;s<t.length&&!i;){const r=t[s];typeof r=="string"&&!ir.has(r)&&Dt(r).values.length&&(i=t[s]),s++}if(i&&n)for(const r of e)t[r]=Cs(n,i)}const nt=t=>t*180/Math.PI,ye=t=>{const e=nt(Math.atan2(t[1],t[0]));return ve(e)},rr={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ye,rotateZ:ye,skewX:t=>nt(Math.atan(t[1])),skewY:t=>nt(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ve=t=>(t=t%360,t<0&&(t+=360),t),Sn=ye,bn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),wn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),or={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:bn,scaleY:wn,scale:t=>(bn(t)+wn(t))/2,rotateX:t=>ve(nt(Math.atan2(t[6],t[5]))),rotateY:t=>ve(nt(Math.atan2(-t[2],t[0]))),rotateZ:Sn,rotate:Sn,skewX:t=>nt(Math.atan(t[4])),skewY:t=>nt(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Vn(t){return t.includes("scale")?1:0}function qe(t,e){if(!t||t==="none")return Vn(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=or,i=n;else{const o=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=rr,i=o}if(!i)return Vn(e);const r=s[e],a=i[1].split(",").map(cr);return typeof r=="function"?r(a):a[r]}const lr=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return qe(n,e)};function cr(t){return parseFloat(t.trim())}const Bn=t=>t===pt||t===T,ur=new Set(["x","y","z"]),hr=mt.filter(t=>!ur.has(t));function fr(t){const e=[];return hr.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const dt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>qe(e,"x"),y:(t,{transform:e})=>qe(e,"y")};dt.translateX=dt.x;dt.translateY=dt.y;const st=new Set;let xe=!1,Ee=!1;function Fs(){if(Ee){const t=Array.from(st).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=fr(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([r,a])=>{var o;(o=s.getValue(r))===null||o===void 0||o.set(a)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}Ee=!1,xe=!1,st.forEach(t=>t.complete()),st.clear()}function As(){st.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ee=!0)})}function dr(){As(),Fs()}class on{constructor(e,n,s,i,r,a=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=r,this.isAsync=a}scheduleResolve(){this.isScheduled=!0,this.isAsync?(st.add(this),xe||(xe=!0,A.read(As),A.resolveKeyframes(Fs))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;for(let r=0;r<e.length;r++)if(e[r]===null)if(r===0){const a=i==null?void 0:i.get(),o=e[e.length-1];if(a!==void 0)e[0]=a;else if(s&&n){const l=s.readValue(n,o);l!=null&&(e[0]=l)}e[0]===void 0&&(e[0]=o),i&&a===void 0&&i.set(e[0])}else e[r]=e[r-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),st.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,st.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Ds=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),mr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function pr(t){const e=mr.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Ss(t,e,n=1){const[s,i]=pr(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const a=r.trim();return Ds(a)?parseFloat(a):a}return ze(i)?Ss(i,e,n+1):i}const bs=t=>e=>e.test(t),gr={test:t=>t==="auto",parse:t=>t},ws=[pt,T,_,X,aa,ia,gr],Rn=t=>ws.find(bs(t));class Vs extends on{constructor(e,n,s,i,r){super(e,n,s,i,r,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let u=e[l];if(typeof u=="string"&&(u=u.trim(),ze(u))){const h=Ss(u,n.current);h!==void 0&&(e[l]=h),l===e.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!is.has(s)||e.length!==2)return;const[i,r]=e,a=Rn(i),o=Rn(r);if(a!==o)if(Bn(a)&&Bn(o))for(let l=0;l<e.length;l++){const u=e[l];typeof u=="string"&&(e[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)Ua(e[i])&&s.push(i);s.length&&ar(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=dt[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var e;const{element:n,name:s,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const r=n.getValue(s);r&&r.jump(this.measuredOrigin,!1);const a=i.length-1,o=i[a];i[a]=dt[s](n.measureViewportBox(),window.getComputedStyle(n.current)),o!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=o),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}const Ln=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Z.test(t)||t==="0")&&!t.startsWith("url("));function yr(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function vr(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const r=t[t.length-1],a=Ln(i,e),o=Ln(r,e);return!a||!o?!1:yr(t)||(n==="spring"||tn(n))&&s}const qr=t=>t!==null;function Zt(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(qr),r=e&&n!=="loop"&&e%2===1?0:i.length-1;return!r||s===void 0?i[r]:s}const xr=40;class Bs{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:a="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=K.now(),this.options={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:r,repeatType:a,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>xr?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&dr(),this._resolved}onKeyframesResolved(e,n){this.resolvedAt=K.now(),this.hasAttemptedResolve=!0;const{name:s,type:i,velocity:r,delay:a,onComplete:o,onUpdate:l,isGenerator:u}=this.options;if(!u&&!vr(e,s,i,r))if(a)this.options.duration=0;else{l&&l(Zt(e,this.options,n)),o&&o(),this.resolveFinishedPromise();return}const h=this.initPlayback(e,n);h!==!1&&(this._resolved={keyframes:e,finalKeyframe:n,...h},this.onPostResolved())}onPostResolved(){}then(e,n){return this.currentFinishedPromise.then(e,n)}flatten(){this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear")}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}const Te=2e4;function Rs(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<Te;)e+=n,s=t.next(e);return e>=Te?1/0:e}const S=(t,e,n)=>t+(e-t)*n;function ne(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Er({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,a=0;if(!e)i=r=a=n;else{const o=n<.5?n*(1+e):n+e-n*e,l=2*n-o;i=ne(l,o,t+1/3),r=ne(l,o,t),a=ne(l,o,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(a*255),alpha:s}}function _t(t,e){return n=>n>0?e:t}const se=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Tr=[pe,et,ot],Mr=t=>Tr.find(e=>e.test(t));function kn(t){const e=Mr(t);if(!e)return!1;let n=e.parse(t);return e===ot&&(n=Er(n)),n}const jn=(t,e)=>{const n=kn(t),s=kn(e);if(!n||!s)return _t(t,e);const i={...n};return r=>(i.red=se(n.red,s.red,r),i.green=se(n.green,s.green,r),i.blue=se(n.blue,s.blue,r),i.alpha=S(n.alpha,s.alpha,r),et.transform(i))},Pr=(t,e)=>n=>e(t(n)),Vt=(...t)=>t.reduce(Pr),Me=new Set(["none","hidden"]);function Cr(t,e){return Me.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Fr(t,e){return n=>S(t,e,n)}function ln(t){return typeof t=="number"?Fr:typeof t=="string"?ze(t)?_t:R.test(t)?jn:Sr:Array.isArray(t)?Ls:typeof t=="object"?R.test(t)?jn:Ar:_t}function Ls(t,e){const n=[...t],s=n.length,i=t.map((r,a)=>ln(r)(r,e[a]));return r=>{for(let a=0;a<s;a++)n[a]=i[a](r);return n}}function Ar(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=ln(t[i])(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}}function Dr(t,e){var n;const s=[],i={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){const a=e.types[r],o=t.indexes[a][i[a]],l=(n=t.values[o])!==null&&n!==void 0?n:0;s[r]=l,i[a]++}return s}const Sr=(t,e)=>{const n=Z.createTransformer(e),s=Dt(t),i=Dt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Me.has(t)&&!i.values.length||Me.has(e)&&!s.values.length?Cr(t,e):Vt(Ls(Dr(s,i),i.values),n):_t(t,e)};function ks(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?S(t,e,n):ln(t)(t,e)}const br=5;function js(t,e,n){const s=Math.max(e-br,0);return as(n-t(s),e-s)}const b={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},In=.001;function wr({duration:t=b.duration,bounce:e=b.bounce,velocity:n=b.velocity,mass:s=b.mass}){let i,r,a=1-e;a=H(b.minDamping,b.maxDamping,a),t=H(b.minDuration,b.maxDuration,G(t)),a<1?(i=u=>{const h=u*a,f=h*t,d=h-n,m=Pe(u,a),p=Math.exp(-f);return In-d/m*p},r=u=>{const f=u*a*t,d=f*n+n,m=Math.pow(a,2)*Math.pow(u,2)*t,p=Math.exp(-f),g=Pe(Math.pow(u,2),a);return(-i(u)+In>0?-1:1)*((d-m)*p)/g}):(i=u=>{const h=Math.exp(-u*t),f=(u-n)*t+1;return-.001+h*f},r=u=>{const h=Math.exp(-u*t),f=(n-u)*(t*t);return h*f});const o=5/t,l=Br(i,r,o);if(t=$(t),isNaN(l))return{stiffness:b.stiffness,damping:b.damping,duration:t};{const u=Math.pow(l,2)*s;return{stiffness:u,damping:a*2*Math.sqrt(s*u),duration:t}}}const Vr=12;function Br(t,e,n){let s=n;for(let i=1;i<Vr;i++)s=s-t(s)/e(s);return s}function Pe(t,e){return t*Math.sqrt(1-e*e)}const Rr=["duration","bounce"],Lr=["stiffness","damping","mass"];function On(t,e){return e.some(n=>t[n]!==void 0)}function kr(t){let e={velocity:b.velocity,stiffness:b.stiffness,damping:b.damping,mass:b.mass,isResolvedFromDuration:!1,...t};if(!On(t,Lr)&&On(t,Rr))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,r=2*H(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:b.mass,stiffness:i,damping:r}}else{const n=wr(t);e={...e,...n,mass:b.mass},e.isResolvedFromDuration=!0}return e}function Is(t=b.visualDuration,e=b.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const r=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],o={done:!1,value:r},{stiffness:l,damping:u,mass:h,duration:f,velocity:d,isResolvedFromDuration:m}=kr({...n,velocity:-G(n.velocity||0)}),p=d||0,g=u/(2*Math.sqrt(l*h)),v=a-r,y=G(Math.sqrt(l/h)),x=Math.abs(v)<5;s||(s=x?b.restSpeed.granular:b.restSpeed.default),i||(i=x?b.restDelta.granular:b.restDelta.default);let E;if(g<1){const q=Pe(y,g);E=M=>{const D=Math.exp(-g*y*M);return a-D*((p+g*y*v)/q*Math.sin(q*M)+v*Math.cos(q*M))}}else if(g===1)E=q=>a-Math.exp(-y*q)*(v+(p+y*v)*q);else{const q=y*Math.sqrt(g*g-1);E=M=>{const D=Math.exp(-g*y*M),P=Math.min(q*M,300);return a-D*((p+g*y*v)*Math.sinh(P)+q*v*Math.cosh(P))/q}}const F={calculatedDuration:m&&f||null,next:q=>{const M=E(q);if(m)o.done=q>=f;else{let D=0;g<1&&(D=q===0?$(p):js(E,q,M));const P=Math.abs(D)<=s,V=Math.abs(a-M)<=i;o.done=P&&V}return o.value=o.done?a:M,o},toString:()=>{const q=Math.min(Rs(F),Te),M=os(D=>F.next(q*D).value,q,30);return q+"ms "+M}};return F}function zn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){const f=t[0],d={done:!1,value:f},m=P=>o!==void 0&&P<o||l!==void 0&&P>l,p=P=>o===void 0?l:l===void 0||Math.abs(o-P)<Math.abs(l-P)?o:l;let g=n*e;const v=f+g,y=a===void 0?v:a(v);y!==v&&(g=y-f);const x=P=>-g*Math.exp(-P/s),E=P=>y+x(P),F=P=>{const V=x(P),I=E(P);d.done=Math.abs(V)<=u,d.value=d.done?y:I};let q,M;const D=P=>{m(d.value)&&(q=P,M=Is({keyframes:[d.value,p(d.value)],velocity:js(E,P,d.value),damping:i,stiffness:r,restDelta:u,restSpeed:h}))};return D(0),{calculatedDuration:null,next:P=>{let V=!1;return!M&&q===void 0&&(V=!0,F(P),D(P)),q!==void 0&&P>=q?M.next(P-q):(!V&&F(P),d)}}}const jr=wt(.42,0,1,1),Ir=wt(0,0,.58,1),Os=wt(.42,0,.58,1),Or=t=>Array.isArray(t)&&typeof t[0]!="number",zr={linear:O,easeIn:jr,easeInOut:Os,easeOut:Ir,circIn:sn,circInOut:ys,circOut:gs,backIn:nn,backInOut:ms,backOut:ds,anticipate:ps},Nn=t=>{if(en(t)){O1(t.length===4);const[e,n,s,i]=t;return wt(e,n,s,i)}else if(typeof t=="string")return zr[t];return t};function Nr(t,e,n){const s=[],i=n||ks,r=t.length-1;for(let a=0;a<r;a++){let o=i(t[a],t[a+1]);if(e){const l=Array.isArray(e)?e[a]||O:e;o=Vt(l,o)}s.push(o)}return s}function Ur(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(O1(r===e.length),r===1)return()=>e[0];if(r===2&&e[0]===e[1])return()=>e[1];const a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=Nr(e,s,i),l=o.length,u=h=>{if(a&&h<t[0])return e[0];let f=0;if(l>1)for(;f<t.length-2&&!(h<t[f+1]);f++);const d=ft(t[f],t[f+1],h);return o[f](d)};return n?h=>u(H(t[0],t[r-1],h)):u}function _r(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=ft(0,e,s);t.push(S(n,1,i))}}function Kr(t){const e=[0];return _r(e,t.length-1),e}function Wr(t,e){return t.map(n=>n*e)}function $r(t,e){return t.map(()=>e||Os).splice(0,t.length-1)}function Kt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=Or(s)?s.map(Nn):Nn(s),r={done:!1,value:e[0]},a=Wr(n&&n.length===e.length?n:Kr(e),t),o=Ur(a,e,{ease:Array.isArray(i)?i:$r(e,i)});return{calculatedDuration:t,next:l=>(r.value=o(l),r.done=l>=t,r)}}const Gr=t=>{const e=({timestamp:n})=>t(n);return{start:()=>A.update(e,!0),stop:()=>Y(e),now:()=>B.isProcessing?B.timestamp:K.now()}},Hr={decay:zn,inertia:zn,tween:Kt,keyframes:Kt,spring:Is},Xr=t=>t/100;class cn extends Bs{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:s,element:i,keyframes:r}=this.options,a=(i==null?void 0:i.KeyframeResolver)||on,o=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new a(r,o,n,s,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r,velocity:a=0}=this.options,o=tn(n)?n:Hr[n]||Kt;let l,u;o!==Kt&&typeof e[0]!="number"&&(l=Vt(Xr,ks(e[0],e[1])),e=[0,100]);const h=o({...this.options,keyframes:e});r==="mirror"&&(u=o({...this.options,keyframes:[...e].reverse(),velocity:-a})),h.calculatedDuration===null&&(h.calculatedDuration=Rs(h));const{calculatedDuration:f}=h,d=f+i,m=d*(s+1)-i;return{generator:h,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:f,resolvedDuration:d,totalDuration:m}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,n=!1){const{resolved:s}=this;if(!s){const{keyframes:P}=this.options;return{done:!0,value:P[P.length-1]}}const{finalKeyframe:i,generator:r,mirroredGenerator:a,mapPercentToKeyframes:o,keyframes:l,calculatedDuration:u,totalDuration:h,resolvedDuration:f}=s;if(this.startTime===null)return r.next(0);const{delay:d,repeat:m,repeatType:p,repeatDelay:g,onUpdate:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-h/this.speed,this.startTime)),n?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const y=this.currentTime-d*(this.speed>=0?1:-1),x=this.speed>=0?y<0:y>h;this.currentTime=Math.max(y,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=h);let E=this.currentTime,F=r;if(m){const P=Math.min(this.currentTime,h)/f;let V=Math.floor(P),I=P%1;!I&&P>=1&&(I=1),I===1&&V--,V=Math.min(V,m+1),!!(V%2)&&(p==="reverse"?(I=1-I,g&&(I-=g/f)):p==="mirror"&&(F=a)),E=H(0,1,I)*f}const q=x?{done:!1,value:l[0]}:F.next(E);o&&(q.value=o(q.value));let{done:M}=q;!x&&u!==null&&(M=this.speed>=0?this.currentTime>=h:this.currentTime<=0);const D=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&M);return D&&i!==void 0&&(q.value=Zt(l,this.options,i)),v&&v(q.value),D&&this.finish(),q}get duration(){const{resolved:e}=this;return e?G(e.calculatedDuration):0}get time(){return G(this.currentTime)}set time(e){e=$(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=G(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=Gr,onPlay:n,startTime:s}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=s??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const Yr=new Set(["opacity","clipPath","filter","transform"]);function Zr(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:a="loop",ease:o="easeInOut",times:l}={}){const u={[e]:n};l&&(u.offset=l);const h=cs(o,i);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:a==="reverse"?"alternate":"normal"})}const Jr=Qe(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Wt=10,Qr=2e4;function t0(t){return tn(t.type)||t.type==="spring"||!ls(t.ease)}function e0(t,e){const n=new cn({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let s={done:!1,value:t[0]};const i=[];let r=0;for(;!s.done&&r<Qr;)s=n.sample(r),i.push(s.value),r+=Wt;return{times:void 0,keyframes:i,duration:r-Wt,ease:"linear"}}const zs={anticipate:ps,backInOut:ms,circInOut:ys};function n0(t){return t in zs}class Un extends Bs{constructor(e){super(e);const{name:n,motionValue:s,element:i,keyframes:r}=this.options;this.resolver=new Vs(r,(a,o)=>this.onKeyframesResolved(a,o),n,s,i),this.resolver.scheduleResolve()}initPlayback(e,n){let{duration:s=300,times:i,ease:r,type:a,motionValue:o,name:l,startTime:u}=this.options;if(!o.owner||!o.owner.current)return!1;if(typeof r=="string"&&Ut()&&n0(r)&&(r=zs[r]),t0(this.options)){const{onComplete:f,onUpdate:d,motionValue:m,element:p,...g}=this.options,v=e0(e,g);e=v.keyframes,e.length===1&&(e[1]=e[0]),s=v.duration,i=v.times,r=v.ease,a="keyframes"}const h=Zr(o.owner.current,l,e,{...this.options,duration:s,times:i,ease:r});return h.startTime=u??this.calcStartTime(),this.pendingTimeline?(An(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{const{onComplete:f}=this.options;o.set(Zt(e,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:s,times:i,type:a,ease:r,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:n}=e;return G(n)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:n}=e;return G(n.currentTime||0)}set time(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.currentTime=$(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:n}=e;return n.playbackRate}set speed(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:n}=e;return n.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:n}=e;return n.startTime}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:n}=this;if(!n)return O;const{animation:s}=n;An(s,e)}return O}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:n,keyframes:s,duration:i,type:r,ease:a,times:o}=e;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:h,onComplete:f,element:d,...m}=this.options,p=new cn({...m,keyframes:s,duration:i,type:r,ease:a,times:o,isGenerator:!0}),g=$(this.time);u.setWithVelocity(p.sample(g-Wt).value,p.sample(g).value,Wt)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:n,name:s,repeatDelay:i,repeatType:r,damping:a,type:o}=e;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return Jr()&&s&&Yr.has(s)&&!l&&!u&&!i&&r!=="mirror"&&a!==0&&o!=="inertia"}}const s0={type:"spring",stiffness:500,damping:25,restSpeed:10},i0=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),a0={type:"keyframes",duration:.8},r0={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},o0=(t,{keyframes:e})=>e.length>2?a0:it.has(t)?t.startsWith("scale")?i0(e[1]):s0:r0;function l0({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}const un=(t,e,n,s={},i,r)=>a=>{const o=Xe(s,t)||{},l=o.delay||s.delay||0;let{elapsed:u=0}=s;u=u-$(l);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-u,onUpdate:d=>{e.set(d),o.onUpdate&&o.onUpdate(d)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:i};l0(o)||(h={...h,...o0(t,h)}),h.duration&&(h.duration=$(h.duration)),h.repeatDelay&&(h.repeatDelay=$(h.repeatDelay)),h.from!==void 0&&(h.keyframes[0]=h.from);let f=!1;if((h.type===!1||h.duration===0&&!h.repeatDelay)&&(h.duration=0,h.delay===0&&(f=!0)),h.allowFlatten=!o.type&&!o.ease,f&&!r&&e.get()!==void 0){const d=Zt(h.keyframes,o);if(d!==void 0)return A.update(()=>{h.onUpdate(d),h.onComplete()}),new ka([])}return!r&&Un.supports(h)?new Un(h):new cn(h)};function c0({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function Ns(t,e,{delay:n=0,transitionOverride:s,type:i}={}){var r;let{transition:a=t.getDefaultTransition(),transitionEnd:o,...l}=e;s&&(a=s);const u=[],h=i&&t.animationState&&t.animationState.getState()[i];for(const f in l){const d=t.getValue(f,(r=t.latestValues[f])!==null&&r!==void 0?r:null),m=l[f];if(m===void 0||h&&c0(h,f))continue;const p={delay:n,...Xe(a||{},f)};let g=!1;if(window.MotionHandoffAnimation){const y=rs(t);if(y){const x=window.MotionHandoffAnimation(y,f,A);x!==null&&(p.startTime=x,g=!0)}}de(t,f),d.start(un(f,d,m,t.shouldReduceMotion&&is.has(f)?{type:!1}:p,t,g));const v=d.animation;v&&u.push(v)}return o&&Promise.all(u).then(()=>{A.update(()=>{o&&Va(t,o)})}),u}function Ce(t,e,n={}){var s;const i=Ft(t,e,n.type==="exit"?(s=t.presenceContext)===null||s===void 0?void 0:s.custom:void 0);let{transition:r=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(r=n.transitionOverride);const a=i?()=>Promise.all(Ns(t,i,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(u=0)=>{const{delayChildren:h=0,staggerChildren:f,staggerDirection:d}=r;return u0(t,e,h+u,f,d,n)}:()=>Promise.resolve(),{when:l}=r;if(l){const[u,h]=l==="beforeChildren"?[a,o]:[o,a];return u().then(()=>h())}else return Promise.all([a(),o(n.delay)])}function u0(t,e,n=0,s=0,i=1,r){const a=[],o=(t.variantChildren.size-1)*s,l=i===1?(u=0)=>u*s:(u=0)=>o-u*s;return Array.from(t.variantChildren).sort(h0).forEach((u,h)=>{u.notify("AnimationStart",e),a.push(Ce(u,e,{...r,delay:n+l(h)}).then(()=>u.notify("AnimationComplete",e)))}),Promise.all(a)}function h0(t,e){return t.sortNodePosition(e)}function f0(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>Ce(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=Ce(t,e,n);else{const i=typeof e=="function"?Ft(t,e,n.custom):e;s=Promise.all(Ns(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function Us(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const d0=ke.length;function _s(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?_s(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<d0;n++){const s=ke[n],i=t.props[s];(Mt(i)||i===!1)&&(e[s]=i)}return e}const m0=[...Le].reverse(),p0=Le.length;function g0(t){return e=>Promise.all(e.map(({animation:n,options:s})=>f0(t,n,s)))}function y0(t){let e=g0(t),n=_n(),s=!0;const i=l=>(u,h)=>{var f;const d=Ft(t,h,l==="exit"?(f=t.presenceContext)===null||f===void 0?void 0:f.custom:void 0);if(d){const{transition:m,transitionEnd:p,...g}=d;u={...u,...g,...p}}return u};function r(l){e=l(t)}function a(l){const{props:u}=t,h=_s(t.parent)||{},f=[],d=new Set;let m={},p=1/0;for(let v=0;v<p0;v++){const y=m0[v],x=n[y],E=u[y]!==void 0?u[y]:h[y],F=Mt(E),q=y===l?x.isActive:null;q===!1&&(p=v);let M=E===h[y]&&E!==u[y]&&F;if(M&&s&&t.manuallyAnimateOnMount&&(M=!1),x.protectedKeys={...m},!x.isActive&&q===null||!E&&!x.prevProp||Xt(E)||typeof E=="boolean")continue;const D=v0(x.prevProp,E);let P=D||y===l&&x.isActive&&!M&&F||v>p&&F,V=!1;const I=Array.isArray(E)?E:[E];let at=I.reduce(i(y),{});q===!1&&(at={});const{prevResolvedValues:fn={}}=x,di={...fn,...at},dn=k=>{P=!0,d.has(k)&&(V=!0,d.delete(k)),x.needsAnimating[k]=!0;const W=t.getValue(k);W&&(W.liveStyle=!1)};for(const k in di){const W=at[k],Jt=fn[k];if(m.hasOwnProperty(k))continue;let Qt=!1;fe(W)&&fe(Jt)?Qt=!Us(W,Jt):Qt=W!==Jt,Qt?W!=null?dn(k):d.add(k):W!==void 0&&d.has(k)?dn(k):x.protectedKeys[k]=!0}x.prevProp=E,x.prevResolvedValues=at,x.isActive&&(m={...m,...at}),s&&t.blockInitialAnimation&&(P=!1),P&&(!(M&&D)||V)&&f.push(...I.map(k=>({animation:k,options:{type:y}})))}if(d.size){const v={};if(typeof u.initial!="boolean"){const y=Ft(t,Array.isArray(u.initial)?u.initial[0]:u.initial);y&&y.transition&&(v.transition=y.transition)}d.forEach(y=>{const x=t.getBaseTarget(y),E=t.getValue(y);E&&(E.liveStyle=!0),v[y]=x??null}),f.push({animation:v})}let g=!!f.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(f):Promise.resolve()}function o(l,u){var h;if(n[l].isActive===u)return Promise.resolve();(h=t.variantChildren)===null||h===void 0||h.forEach(d=>{var m;return(m=d.animationState)===null||m===void 0?void 0:m.setActive(l,u)}),n[l].isActive=u;const f=a(l);for(const d in n)n[d].protectedKeys={};return f}return{animateChanges:a,setActive:o,setAnimateFunction:r,getState:()=>n,reset:()=>{n=_n(),s=!0}}}function v0(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Us(e,t):!1}function Q(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function _n(){return{animate:Q(!0),whileInView:Q(),whileHover:Q(),whileTap:Q(),whileDrag:Q(),whileFocus:Q(),exit:Q()}}class J{constructor(e){this.isMounted=!1,this.node=e}update(){}}class q0 extends J{constructor(e){super(e),e.animationState||(e.animationState=y0(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Xt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)===null||e===void 0||e.call(this)}}let x0=0;class E0 extends J{constructor(){super(...arguments),this.id=x0++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const T0={animation:{Feature:q0},exit:{Feature:E0}},U={x:!1,y:!1};function Ks(){return U.x||U.y}function M0(t){return t==="x"||t==="y"?U[t]?null:(U[t]=!0,()=>{U[t]=!1}):U.x||U.y?null:(U.x=U.y=!0,()=>{U.x=U.y=!1})}function St(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}const hn=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function Bt(t){return{point:{x:t.pageX,y:t.pageY}}}const P0=t=>e=>hn(e)&&t(e,Bt(e));function xt(t,e,n,s){return St(t,e,P0(n),s)}function Ws({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function C0({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function F0(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}const $s=1e-4,A0=1-$s,D0=1+$s,Gs=.01,S0=0-Gs,b0=0+Gs;function j(t){return t.max-t.min}function w0(t,e,n){return Math.abs(t-e)<=n}function Kn(t,e,n,s=.5){t.origin=s,t.originPoint=S(e.min,e.max,t.origin),t.scale=j(n)/j(e),t.translate=S(n.min,n.max,t.origin)-t.originPoint,(t.scale>=A0&&t.scale<=D0||isNaN(t.scale))&&(t.scale=1),(t.translate>=S0&&t.translate<=b0||isNaN(t.translate))&&(t.translate=0)}function Et(t,e,n,s){Kn(t.x,e.x,n.x,s?s.originX:void 0),Kn(t.y,e.y,n.y,s?s.originY:void 0)}function Wn(t,e,n){t.min=n.min+e.min,t.max=t.min+j(e)}function V0(t,e,n){Wn(t.x,e.x,n.x),Wn(t.y,e.y,n.y)}function $n(t,e,n){t.min=e.min-n.min,t.max=t.min+j(e)}function Tt(t,e,n){$n(t.x,e.x,n.x),$n(t.y,e.y,n.y)}const Gn=()=>({translate:0,scale:1,origin:0,originPoint:0}),lt=()=>({x:Gn(),y:Gn()}),Hn=()=>({min:0,max:0}),w=()=>({x:Hn(),y:Hn()});function N(t){return[t("x"),t("y")]}function ie(t){return t===void 0||t===1}function Fe({scale:t,scaleX:e,scaleY:n}){return!ie(t)||!ie(e)||!ie(n)}function tt(t){return Fe(t)||Hs(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Hs(t){return Xn(t.x)||Xn(t.y)}function Xn(t){return t&&t!=="0%"}function $t(t,e,n){const s=t-n,i=e*s;return n+i}function Yn(t,e,n,s,i){return i!==void 0&&(t=$t(t,i,s)),$t(t,n,s)+e}function Ae(t,e=0,n=1,s,i){t.min=Yn(t.min,e,n,s,i),t.max=Yn(t.max,e,n,s,i)}function Xs(t,{x:e,y:n}){Ae(t.x,e.translate,e.scale,e.originPoint),Ae(t.y,n.translate,n.scale,n.originPoint)}const Zn=.999999999999,Jn=1.0000000000001;function B0(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let r,a;for(let o=0;o<i;o++){r=n[o],a=r.projectionDelta;const{visualElement:l}=r.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&ut(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),a&&(e.x*=a.x.scale,e.y*=a.y.scale,Xs(t,a)),s&&tt(r.latestValues)&&ut(t,r.latestValues))}e.x<Jn&&e.x>Zn&&(e.x=1),e.y<Jn&&e.y>Zn&&(e.y=1)}function ct(t,e){t.min=t.min+e,t.max=t.max+e}function Qn(t,e,n,s,i=.5){const r=S(t.min,t.max,i);Ae(t,e,n,r,s)}function ut(t,e){Qn(t.x,e.x,e.scaleX,e.scale,e.originX),Qn(t.y,e.y,e.scaleY,e.scale,e.originY)}function Ys(t,e){return Ws(F0(t.getBoundingClientRect(),e))}function R0(t,e,n){const s=Ys(t,n),{scroll:i}=e;return i&&(ct(s.x,i.offset.x),ct(s.y,i.offset.y)),s}const Zs=({current:t})=>t?t.ownerDocument.defaultView:null,t1=(t,e)=>Math.abs(t-e);function L0(t,e){const n=t1(t.x,e.x),s=t1(t.y,e.y);return Math.sqrt(n**2+s**2)}class Js{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=re(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,m=L0(f.offset,{x:0,y:0})>=3;if(!d&&!m)return;const{point:p}=f,{timestamp:g}=B;this.history.push({...p,timestamp:g});const{onStart:v,onMove:y}=this.handlers;d||(v&&v(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,f)},this.handlePointerMove=(f,d)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=ae(d,this.transformPagePoint),A.update(this.updatePoint,!0)},this.handlePointerUp=(f,d)=>{this.end();const{onEnd:m,onSessionEnd:p,resumeAnimation:g}=this.handlers;if(this.dragSnapToOrigin&&g&&g(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=re(f.type==="pointercancel"?this.lastMoveEventInfo:ae(d,this.transformPagePoint),this.history);this.startEvent&&m&&m(f,v),p&&p(f,v)},!hn(e))return;this.dragSnapToOrigin=r,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const a=Bt(e),o=ae(a,this.transformPagePoint),{point:l}=o,{timestamp:u}=B;this.history=[{...l,timestamp:u}];const{onSessionStart:h}=n;h&&h(e,re(o,this.history)),this.removeListeners=Vt(xt(this.contextWindow,"pointermove",this.handlePointerMove),xt(this.contextWindow,"pointerup",this.handlePointerUp),xt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Y(this.updatePoint)}}function ae(t,e){return e?{point:e(t.point)}:t}function e1(t,e){return{x:t.x-e.x,y:t.y-e.y}}function re({point:t},e){return{point:t,delta:e1(t,Qs(e)),offset:e1(t,k0(e)),velocity:j0(e,.1)}}function k0(t){return t[0]}function Qs(t){return t[t.length-1]}function j0(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=Qs(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>$(e)));)n--;if(!s)return{x:0,y:0};const r=G(i.timestamp-s.timestamp);if(r===0)return{x:0,y:0};const a={x:(i.x-s.x)/r,y:(i.y-s.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}function I0(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?S(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?S(n,t,s.max):Math.min(t,n)),t}function n1(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function O0(t,{top:e,left:n,bottom:s,right:i}){return{x:n1(t.x,n,i),y:n1(t.y,e,s)}}function s1(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function z0(t,e){return{x:s1(t.x,e.x),y:s1(t.y,e.y)}}function N0(t,e){let n=.5;const s=j(t),i=j(e);return i>s?n=ft(e.min,e.max-s,t.min):s>i&&(n=ft(t.min,t.max-i,e.min)),H(0,1,n)}function U0(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const De=.35;function _0(t=De){return t===!1?t=0:t===!0&&(t=De),{x:i1(t,"left","right"),y:i1(t,"top","bottom")}}function i1(t,e,n){return{min:a1(t,e),max:a1(t,n)}}function a1(t,e){return typeof t=="number"?t:t[e]||0}const K0=new WeakMap;class W0{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=w(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=h=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Bt(h).point)},r=(h,f)=>{const{drag:d,dragPropagation:m,onDragStart:p}=this.getProps();if(d&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=M0(d),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),N(v=>{let y=this.getAxisMotionValue(v).get()||0;if(_.test(y)){const{projection:x}=this.visualElement;if(x&&x.layout){const E=x.layout.layoutBox[v];E&&(y=j(E)*(parseFloat(y)/100))}}this.originPoint[v]=y}),p&&A.postRender(()=>p(h,f)),de(this.visualElement,"transform");const{animationState:g}=this.visualElement;g&&g.setActive("whileDrag",!0)},a=(h,f)=>{const{dragPropagation:d,dragDirectionLock:m,onDirectionLock:p,onDrag:g}=this.getProps();if(!d&&!this.openDragLock)return;const{offset:v}=f;if(m&&this.currentDirection===null){this.currentDirection=$0(v),this.currentDirection!==null&&p&&p(this.currentDirection);return}this.updateAxis("x",f.point,v),this.updateAxis("y",f.point,v),this.visualElement.render(),g&&g(h,f)},o=(h,f)=>this.stop(h,f),l=()=>N(h=>{var f;return this.getAnimationState(h)==="paused"&&((f=this.getAxisMotionValue(h).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Js(e,{onSessionStart:i,onStart:r,onMove:a,onSessionEnd:o,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Zs(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:r}=this.getProps();r&&A.postRender(()=>r(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!jt(e,i,this.currentDirection))return;const r=this.getAxisMotionValue(e);let a=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(a=I0(a,this.constraints[e],this.elastic[e])),r.set(a)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,r=this.constraints;n&&rt(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=O0(i.layoutBox,n):this.constraints=!1,this.elastic=_0(s),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&N(a=>{this.constraints!==!1&&this.getAxisMotionValue(a)&&(this.constraints[a]=U0(i.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!rt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const r=R0(s,i.root,this.visualElement.getTransformPagePoint());let a=z0(i.layout.layoutBox,r);if(n){const o=n(C0(a));this.hasMutatedConstraints=!!o,o&&(a=Ws(o))}return a}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:r,dragSnapToOrigin:a,onDragTransitionEnd:o}=this.getProps(),l=this.constraints||{},u=N(h=>{if(!jt(h,n,this.currentDirection))return;let f=l&&l[h]||{};a&&(f={min:0,max:0});const d=i?200:1e6,m=i?40:1e7,p={type:"inertia",velocity:s?e[h]:0,bounceStiffness:d,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...r,...f};return this.startAxisValueAnimation(h,p)});return Promise.all(u).then(o)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return de(this.visualElement,e),s.start(un(e,s,0,n,this.visualElement,!1))}stopAnimation(){N(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){N(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){N(n=>{const{drag:s}=this.getProps();if(!jt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,r=this.getAxisMotionValue(n);if(i&&i.layout){const{min:a,max:o}=i.layout.layoutBox[n];r.set(e[n]-S(a,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!rt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};N(a=>{const o=this.getAxisMotionValue(a);if(o&&this.constraints!==!1){const l=o.get();i[a]=N0({min:l,max:l},this.constraints[a])}});const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),N(a=>{if(!jt(a,e,null))return;const o=this.getAxisMotionValue(a),{min:l,max:u}=this.constraints[a];o.set(S(l,u,i[a]))})}addListeners(){if(!this.visualElement.current)return;K0.set(this.visualElement,this);const e=this.visualElement.current,n=xt(e,"pointerdown",l=>{const{drag:u,dragListener:h=!0}=this.getProps();u&&h&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();rt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),A.read(s);const a=St(window,"resize",()=>this.scalePositionWithinConstraints()),o=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(N(h=>{const f=this.getAxisMotionValue(h);f&&(this.originPoint[h]+=l[h].translate,f.set(f.get()+l[h].translate))}),this.visualElement.render())});return()=>{a(),n(),r(),o&&o()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:a=De,dragMomentum:o=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:r,dragElastic:a,dragMomentum:o}}}function jt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function $0(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class G0 extends J{constructor(e){super(e),this.removeGroupControls=O,this.removeListeners=O,this.controls=new W0(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||O}unmount(){this.removeGroupControls(),this.removeListeners()}}const r1=t=>(e,n)=>{t&&A.postRender(()=>t(e,n))};class H0 extends J{constructor(){super(...arguments),this.removePointerDownListener=O}onPointerDown(e){this.session=new Js(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Zs(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:r1(e),onStart:r1(n),onMove:s,onEnd:(r,a)=>{delete this.session,i&&A.postRender(()=>i(r,a))}}}mount(){this.removePointerDownListener=xt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const zt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function o1(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const gt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(T.test(t))t=parseFloat(t);else return t;const n=o1(t,e.target.x),s=o1(t,e.target.y);return`${n}% ${s}%`}},X0={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=Z.parse(t);if(i.length>5)return s;const r=Z.createTransformer(t),a=typeof i[0]!="number"?1:0,o=n.x.scale*e.x,l=n.y.scale*e.y;i[0+a]/=o,i[1+a]/=l;const u=S(o,l,.5);return typeof i[2+a]=="number"&&(i[2+a]/=u),typeof i[3+a]=="number"&&(i[3+a]/=u),r(i)}};class Y0 extends c.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:r}=e;sa(Z0),r&&(n.group&&n.group.add(r),s&&s.register&&i&&s.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),zt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:r}=this.props,a=s.projection;return a&&(a.isPresent=r,i||e.layoutDependency!==n||n===void 0||e.isPresent!==r?a.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?a.promote():a.relegate()||A.postRender(()=>{const o=a.getStack();(!o||!o.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),je.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ti(t){const[e,n]=I1(),s=c.useContext(we);return C.jsx(Y0,{...t,layoutGroup:s,switchLayoutGroup:c.useContext(W1),isPresent:e,safeToRemove:n})}const Z0={borderRadius:{...gt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:gt,borderTopRightRadius:gt,borderBottomLeftRadius:gt,borderBottomRightRadius:gt,boxShadow:X0};function J0(t,e,n){const s=L(t)?t:At(t);return s.start(un("",s,e,n)),s.animation}function Q0(t){return t instanceof SVGElement&&t.tagName!=="svg"}const to=(t,e)=>t.depth-e.depth;class eo{constructor(){this.children=[],this.isDirty=!1}add(e){Ye(this.children,e),this.isDirty=!0}remove(e){Ze(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(to),this.isDirty=!1,this.children.forEach(e)}}function no(t,e){const n=K.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(Y(s),t(r-e))};return A.read(s,!0),()=>Y(s)}const ei=["TopLeft","TopRight","BottomLeft","BottomRight"],so=ei.length,l1=t=>typeof t=="string"?parseFloat(t):t,c1=t=>typeof t=="number"||T.test(t);function io(t,e,n,s,i,r){i?(t.opacity=S(0,n.opacity!==void 0?n.opacity:1,ao(s)),t.opacityExit=S(e.opacity!==void 0?e.opacity:1,0,ro(s))):r&&(t.opacity=S(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let a=0;a<so;a++){const o=`border${ei[a]}Radius`;let l=u1(e,o),u=u1(n,o);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||c1(l)===c1(u)?(t[o]=Math.max(S(l1(l),l1(u),s),0),(_.test(u)||_.test(l))&&(t[o]+="%")):t[o]=u}(e.rotate||n.rotate)&&(t.rotate=S(e.rotate||0,n.rotate||0,s))}function u1(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const ao=ni(0,.5,gs),ro=ni(.5,.95,O);function ni(t,e,n){return s=>s<t?0:s>e?1:n(ft(t,e,s))}function h1(t,e){t.min=e.min,t.max=e.max}function z(t,e){h1(t.x,e.x),h1(t.y,e.y)}function f1(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function d1(t,e,n,s,i){return t-=e,t=$t(t,1/n,s),i!==void 0&&(t=$t(t,1/i,s)),t}function oo(t,e=0,n=1,s=.5,i,r=t,a=t){if(_.test(e)&&(e=parseFloat(e),e=S(a.min,a.max,e/100)-a.min),typeof e!="number")return;let o=S(r.min,r.max,s);t===r&&(o-=e),t.min=d1(t.min,e,n,o,i),t.max=d1(t.max,e,n,o,i)}function m1(t,e,[n,s,i],r,a){oo(t,e[n],e[s],e[i],e.scale,r,a)}const lo=["x","scaleX","originX"],co=["y","scaleY","originY"];function p1(t,e,n,s){m1(t.x,e,lo,n?n.x:void 0,s?s.x:void 0),m1(t.y,e,co,n?n.y:void 0,s?s.y:void 0)}function g1(t){return t.translate===0&&t.scale===1}function si(t){return g1(t.x)&&g1(t.y)}function y1(t,e){return t.min===e.min&&t.max===e.max}function uo(t,e){return y1(t.x,e.x)&&y1(t.y,e.y)}function v1(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function ii(t,e){return v1(t.x,e.x)&&v1(t.y,e.y)}function q1(t){return j(t.x)/j(t.y)}function x1(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class ho{constructor(){this.members=[]}add(e){Ye(this.members,e),e.scheduleRender()}remove(e){if(Ze(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const r=this.members[i];if(r.isPresent!==!1){s=r;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function fo(t,e,n){let s="";const i=t.x.translate/e.x,r=t.y.translate/e.y,a=(n==null?void 0:n.z)||0;if((i||r||a)&&(s=`translate3d(${i}px, ${r}px, ${a}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:u,rotate:h,rotateX:f,rotateY:d,skewX:m,skewY:p}=n;u&&(s=`perspective(${u}px) ${s}`),h&&(s+=`rotate(${h}deg) `),f&&(s+=`rotateX(${f}deg) `),d&&(s+=`rotateY(${d}deg) `),m&&(s+=`skewX(${m}deg) `),p&&(s+=`skewY(${p}deg) `)}const o=t.x.scale*e.x,l=t.y.scale*e.y;return(o!==1||l!==1)&&(s+=`scale(${o}, ${l})`),s||"none"}const oe=["","X","Y","Z"],mo={visibility:"hidden"},E1=1e3;let po=0;function le(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function ai(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=rs(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",A,!(i||r))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&ai(s)}function ri({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(a={},o=e==null?void 0:e()){this.id=po++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(vo),this.nodes.forEach(Mo),this.nodes.forEach(Po),this.nodes.forEach(qo)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=o?o.root||o:this,this.path=o?[...o.path,o]:[],this.parent=o,this.depth=o?o.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new eo)}addEventListener(a,o){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new Je),this.eventHandlers.get(a).add(o)}notifyListeners(a,...o){const l=this.eventHandlers.get(a);l&&l.notify(...o)}hasListeners(a){return this.eventHandlers.has(a)}mount(a,o=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Q0(a),this.instance=a;const{layoutId:l,layout:u,visualElement:h}=this.options;if(h&&!h.current&&h.mount(a),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),o&&(u||l)&&(this.isLayoutDirty=!0),t){let f;const d=()=>this.root.updateBlockedByResize=!1;t(a,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=no(d,250),zt.hasAnimatedSinceResize&&(zt.hasAnimatedSinceResize=!1,this.nodes.forEach(M1))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&h&&(l||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:d,hasRelativeLayoutChanged:m,layout:p})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const g=this.options.transition||h.getDefaultTransition()||So,{onLayoutAnimationStart:v,onLayoutAnimationComplete:y}=h.getProps(),x=!this.targetLayout||!ii(this.targetLayout,p),E=!d&&m;if(this.options.layoutRoot||this.resumeFrom||E||d&&(x||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,E);const F={...Xe(g,"layout"),onPlay:v,onComplete:y};(h.shouldReduceMotion||this.options.layoutRoot)&&(F.delay=0,F.type=!1),this.startAnimation(F)}else d||M1(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=p})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Y(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Co),this.animationId++)}getTransformTemplate(){const{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ai(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let h=0;h<this.path.length;h++){const f=this.path[h];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:o,layout:l}=this.options;if(o===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(T1);return}this.isUpdating||this.nodes.forEach(Eo),this.isUpdating=!1,this.nodes.forEach(To),this.nodes.forEach(go),this.nodes.forEach(yo),this.clearAllSnapshots();const o=K.now();B.delta=H(0,1e3/60,o-B.timestamp),B.timestamp=o,B.isProcessing=!0,te.update.process(B),te.preRender.process(B),te.render.process(B),B.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,je.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(xo),this.sharedNodes.forEach(Fo)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,A.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){A.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!j(this.snapshot.measuredBox.x)&&!j(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=w(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:o}=this.options;o&&o.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let o=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(o=!1),o){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,o=this.projectionDelta&&!si(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,h=u!==this.prevTransformTemplateValue;a&&(o||tt(this.latestValues)||h)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){const o=this.measurePageBox();let l=this.removeElementScroll(o);return a&&(l=this.removeTransform(l)),bo(l),{animationId:this.root.animationId,measuredBox:o,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var a;const{visualElement:o}=this.options;if(!o)return w();const l=o.measureViewportBox();if(!(((a=this.scroll)===null||a===void 0?void 0:a.wasRoot)||this.path.some(wo))){const{scroll:h}=this.root;h&&(ct(l.x,h.offset.x),ct(l.y,h.offset.y))}return l}removeElementScroll(a){var o;const l=w();if(z(l,a),!((o=this.scroll)===null||o===void 0)&&o.wasRoot)return l;for(let u=0;u<this.path.length;u++){const h=this.path[u],{scroll:f,options:d}=h;h!==this.root&&f&&d.layoutScroll&&(f.wasRoot&&z(l,a),ct(l.x,f.offset.x),ct(l.y,f.offset.y))}return l}applyTransform(a,o=!1){const l=w();z(l,a);for(let u=0;u<this.path.length;u++){const h=this.path[u];!o&&h.options.layoutScroll&&h.scroll&&h!==h.root&&ut(l,{x:-h.scroll.offset.x,y:-h.scroll.offset.y}),tt(h.latestValues)&&ut(l,h.latestValues)}return tt(this.latestValues)&&ut(l,this.latestValues),l}removeTransform(a){const o=w();z(o,a);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!tt(u.latestValues))continue;Fe(u.latestValues)&&u.updateSnapshot();const h=w(),f=u.measurePageBox();z(h,f),p1(o,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,h)}return tt(this.latestValues)&&p1(o,this.latestValues),o}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:a.crossfade!==void 0?a.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==B.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){var o;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(a||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:f,layoutId:d}=this.options;if(!(!this.layout||!(f||d))){if(this.resolvedRelativeTargetAt=B.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=w(),this.relativeTargetOrigin=w(),Tt(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),z(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=w(),this.targetWithTransforms=w()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),V0(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):z(this.target,this.layout.layoutBox),Xs(this.target,this.targetDelta)):z(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=w(),this.relativeTargetOrigin=w(),Tt(this.relativeTargetOrigin,this.target,m.target),z(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Fe(this.parent.latestValues)||Hs(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var a;const o=this.getLead(),l=!!this.resumingFrom||this!==o;let u=!0;if((this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===B.timestamp&&(u=!1),u)return;const{layout:h,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(h||f))return;z(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,m=this.treeScale.y;B0(this.layoutCorrected,this.treeScale,this.path,l),o.layout&&!o.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(o.target=o.layout.layoutBox,o.targetWithTransforms=w());const{target:p}=o;if(!p){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(f1(this.prevProjectionDelta.x,this.projectionDelta.x),f1(this.prevProjectionDelta.y,this.projectionDelta.y)),Et(this.projectionDelta,this.layoutCorrected,p,this.latestValues),(this.treeScale.x!==d||this.treeScale.y!==m||!x1(this.projectionDelta.x,this.prevProjectionDelta.x)||!x1(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",p))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){var o;if((o=this.options.visualElement)===null||o===void 0||o.scheduleRender(),a){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=lt(),this.projectionDelta=lt(),this.projectionDeltaWithTransform=lt()}setAnimationOrigin(a,o=!1){const l=this.snapshot,u=l?l.latestValues:{},h={...this.latestValues},f=lt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!o;const d=w(),m=l?l.source:void 0,p=this.layout?this.layout.source:void 0,g=m!==p,v=this.getStack(),y=!v||v.members.length<=1,x=!!(g&&!y&&this.options.crossfade===!0&&!this.path.some(Do));this.animationProgress=0;let E;this.mixTargetDelta=F=>{const q=F/1e3;P1(f.x,a.x,q),P1(f.y,a.y,q),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Tt(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Ao(this.relativeTarget,this.relativeTargetOrigin,d,q),E&&uo(this.relativeTarget,E)&&(this.isProjectionDirty=!1),E||(E=w()),z(E,this.relativeTarget)),g&&(this.animationValues=h,io(h,u,this.latestValues,q,x,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=q},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Y(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=A.update(()=>{zt.hasAnimatedSinceResize=!0,this.currentAnimation=J0(0,E1,{...a,onUpdate:o=>{this.mixTargetDelta(o),a.onUpdate&&a.onUpdate(o)},onStop:()=>{},onComplete:()=>{a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(E1),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const a=this.getLead();let{targetWithTransforms:o,target:l,layout:u,latestValues:h}=a;if(!(!o||!l||!u)){if(this!==a&&this.layout&&u&&oi(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||w();const f=j(this.layout.layoutBox.x);l.x.min=a.target.x.min,l.x.max=l.x.min+f;const d=j(this.layout.layoutBox.y);l.y.min=a.target.y.min,l.y.max=l.y.min+d}z(o,l),ut(o,h),Et(this.projectionDeltaWithTransform,this.layoutCorrected,o,h)}}registerSharedNode(a,o){this.sharedNodes.has(a)||this.sharedNodes.set(a,new ho),this.sharedNodes.get(a).add(o);const u=o.options.initialPromotionConfig;o.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(o):void 0})}isLead(){const a=this.getStack();return a?a.lead===this:!0}getLead(){var a;const{layoutId:o}=this.options;return o?((a=this.getStack())===null||a===void 0?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:o}=this.options;return o?(a=this.getStack())===null||a===void 0?void 0:a.prevLead:void 0}getStack(){const{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:o,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),a&&(this.projectionDelta=void 0,this.needsReset=!0),o&&this.setOptions({transition:o})}relegate(){const a=this.getStack();return a?a.relegate(this):!1}resetSkewAndRotation(){const{visualElement:a}=this.options;if(!a)return;let o=!1;const{latestValues:l}=a;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(o=!0),!o)return;const u={};l.z&&le("z",a,u,this.animationValues);for(let h=0;h<oe.length;h++)le(`rotate${oe[h]}`,a,u,this.animationValues),le(`skew${oe[h]}`,a,u,this.animationValues);a.render();for(const h in u)a.setStaticValue(h,u[h]),this.animationValues&&(this.animationValues[h]=u[h]);a.scheduleRender()}getProjectionStyles(a){var o,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return mo;const u={visibility:""},h=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=It(a==null?void 0:a.pointerEvents)||"",u.transform=h?h(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const g={};return this.options.layoutId&&(g.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,g.pointerEvents=It(a==null?void 0:a.pointerEvents)||""),this.hasProjected&&!tt(this.latestValues)&&(g.transform=h?h({},""):"none",this.hasProjected=!1),g}const d=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=fo(this.projectionDeltaWithTransform,this.treeScale,d),h&&(u.transform=h(d,u.transform));const{x:m,y:p}=this.projectionDelta;u.transformOrigin=`${m.origin*100}% ${p.origin*100}% 0`,f.animationValues?u.opacity=f===this?(l=(o=d.opacity)!==null&&o!==void 0?o:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:u.opacity=f===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const g in Pt){if(d[g]===void 0)continue;const{correct:v,applyTo:y,isCSSVariable:x}=Pt[g],E=u.transform==="none"?d[g]:v(d[g],f);if(y){const F=y.length;for(let q=0;q<F;q++)u[y[q]]=E}else x?this.options.visualElement.renderState.vars[g]=E:u[g]=E}return this.options.layoutId&&(u.pointerEvents=f===this?It(a==null?void 0:a.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>{var o;return(o=a.currentAnimation)===null||o===void 0?void 0:o.stop()}),this.root.nodes.forEach(T1),this.root.sharedNodes.clear()}}}function go(t){t.updateLayout()}function yo(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:r}=t.options,a=n.source!==t.layout.source;r==="size"?N(f=>{const d=a?n.measuredBox[f]:n.layoutBox[f],m=j(d);d.min=s[f].min,d.max=d.min+m}):oi(r,n.layoutBox,s)&&N(f=>{const d=a?n.measuredBox[f]:n.layoutBox[f],m=j(s[f]);d.max=d.min+m,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[f].max=t.relativeTarget[f].min+m)});const o=lt();Et(o,s,n.layoutBox);const l=lt();a?Et(l,t.applyTransform(i,!0),n.measuredBox):Et(l,s,n.layoutBox);const u=!si(o);let h=!1;if(!t.resumeFrom){const f=t.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:d,layout:m}=f;if(d&&m){const p=w();Tt(p,n.layoutBox,d.layoutBox);const g=w();Tt(g,s,m.layoutBox),ii(p,g)||(h=!0),f.options.layoutRoot&&(t.relativeTarget=g,t.relativeTargetOrigin=p,t.relativeParent=f)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:l,layoutDelta:o,hasLayoutChanged:u,hasRelativeLayoutChanged:h})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function vo(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function qo(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function xo(t){t.clearSnapshot()}function T1(t){t.clearMeasurements()}function Eo(t){t.isLayoutDirty=!1}function To(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function M1(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Mo(t){t.resolveTargetDelta()}function Po(t){t.calcProjection()}function Co(t){t.resetSkewAndRotation()}function Fo(t){t.removeLeadSnapshot()}function P1(t,e,n){t.translate=S(e.translate,0,n),t.scale=S(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function C1(t,e,n,s){t.min=S(e.min,n.min,s),t.max=S(e.max,n.max,s)}function Ao(t,e,n,s){C1(t.x,e.x,n.x,s),C1(t.y,e.y,n.y,s)}function Do(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const So={duration:.45,ease:[.4,0,.1,1]},F1=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),A1=F1("applewebkit/")&&!F1("chrome/")?Math.round:O;function D1(t){t.min=A1(t.min),t.max=A1(t.max)}function bo(t){D1(t.x),D1(t.y)}function oi(t,e,n){return t==="position"||t==="preserve-aspect"&&!w0(q1(e),q1(n),.2)}function wo(t){var e;return t!==t.root&&((e=t.scroll)===null||e===void 0?void 0:e.wasRoot)}const Vo=ri({attachResizeListener:(t,e)=>St(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ce={current:void 0},li=ri({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ce.current){const t=new Vo({});t.mount(window),t.setOptions({layoutScroll:!0}),ce.current=t}return ce.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Bo={pan:{Feature:H0},drag:{Feature:G0,ProjectionNode:li,MeasureLayout:ti}};function Ro(t,e,n){var s;if(t instanceof EventTarget)return[t];if(typeof t=="string"){let i=document;const r=(s=void 0)!==null&&s!==void 0?s:i.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}function ci(t,e){const n=Ro(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function S1(t){return!(t.pointerType==="touch"||Ks())}function Lo(t,e,n={}){const[s,i,r]=ci(t,n),a=o=>{if(!S1(o))return;const{target:l}=o,u=e(l,o);if(typeof u!="function"||!l)return;const h=f=>{S1(f)&&(u(f),l.removeEventListener("pointerleave",h))};l.addEventListener("pointerleave",h,i)};return s.forEach(o=>{o.addEventListener("pointerenter",a,i)}),r}function b1(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,r=s[i];r&&A.postRender(()=>r(e,Bt(e)))}class ko extends J{mount(){const{current:e}=this.node;e&&(this.unmount=Lo(e,(n,s)=>(b1(this.node,s,"Start"),i=>b1(this.node,i,"End"))))}unmount(){}}class jo extends J{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Vt(St(this.node.current,"focus",()=>this.onFocus()),St(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function w1(t,e){const n=`${e}PointerCapture`;if(t.target instanceof Element&&n in t.target&&t.pointerId!==void 0)try{t.target[n](t.pointerId)}catch{}}const ui=(t,e)=>e?t===e?!0:ui(t,e.parentElement):!1,Io=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Oo(t){return Io.has(t.tagName)||t.tabIndex!==-1}const vt=new WeakSet;function V1(t){return e=>{e.key==="Enter"&&t(e)}}function ue(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const zo=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=V1(()=>{if(vt.has(n))return;ue(n,"down");const i=V1(()=>{ue(n,"up")}),r=()=>ue(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",r,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function B1(t){return hn(t)&&!Ks()}function No(t,e,n={}){const[s,i,r]=ci(t,n),a=o=>{const l=o.currentTarget;if(!l||!B1(o)||vt.has(l))return;vt.add(l),w1(o,"set");const u=e(l,o),h=(m,p)=>{l.removeEventListener("pointerup",f),l.removeEventListener("pointercancel",d),w1(m,"release"),!(!B1(m)||!vt.has(l))&&(vt.delete(l),typeof u=="function"&&u(m,{success:p}))},f=m=>{(m.isTrusted?Uo(m,l instanceof Element?l.getBoundingClientRect():{left:0,top:0,right:window.innerWidth,bottom:window.innerHeight}):!1)?h(m,!1):h(m,!(l instanceof Element)||ui(l,m.target))},d=m=>{h(m,!1)};l.addEventListener("pointerup",f,i),l.addEventListener("pointercancel",d,i),l.addEventListener("lostpointercapture",d,i)};return s.forEach(o=>{o=n.useGlobalTarget?window:o;let l=!1;o instanceof HTMLElement&&(l=!0,!Oo(o)&&o.getAttribute("tabindex")===null&&(o.tabIndex=0)),o.addEventListener("pointerdown",a,i),l&&o.addEventListener("focus",u=>zo(u,i),i)}),r}function Uo(t,e){return t.clientX<e.left||t.clientX>e.right||t.clientY<e.top||t.clientY>e.bottom}function R1(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),r=s[i];r&&A.postRender(()=>r(e,Bt(e)))}class _o extends J{mount(){const{current:e}=this.node;e&&(this.unmount=No(e,(n,s)=>(R1(this.node,s,"Start"),(i,{success:r})=>R1(this.node,i,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Se=new WeakMap,he=new WeakMap,Ko=t=>{const e=Se.get(t.target);e&&e(t)},Wo=t=>{t.forEach(Ko)};function $o({root:t,...e}){const n=t||document;he.has(n)||he.set(n,{});const s=he.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Wo,{root:t,...e})),s[i]}function Go(t,e,n){const s=$o(e);return Se.set(t,n),s.observe(t),()=>{Se.delete(t),s.unobserve(t)}}const Ho={some:0,all:1};class Xo extends J{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:r}=e,a={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Ho[i]},o=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,r&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:h,onViewportLeave:f}=this.node.getProps(),d=u?h:f;d&&d(l)};return Go(this.node.current,a,o)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Yo(e,n))&&this.startObserver()}unmount(){}}function Yo({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Zo={inView:{Feature:Xo},tap:{Feature:_o},focus:{Feature:jo},hover:{Feature:ko}},Jo={layout:{ProjectionNode:li,MeasureLayout:ti}},be={current:null},hi={current:!1};function Qo(){if(hi.current=!0,!!Be)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>be.current=t.matches;t.addListener(e),e()}else be.current=!1}const t2=[...ws,R,Z],e2=t=>t2.find(bs(t)),n2=new WeakMap;function s2(t,e,n){for(const s in e){const i=e[s],r=n[s];if(L(i))t.addValue(s,i);else if(L(r))t.addValue(s,At(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const a=t.getValue(s);a.liveStyle===!0?a.jump(i):a.hasAnimated||a.set(i)}else{const a=t.getStaticValue(s);t.addValue(s,At(a!==void 0?a:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const L1=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class i2{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:r,visualState:a},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=on,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const m=K.now();this.renderScheduledAt<m&&(this.renderScheduledAt=m,A.render(this.render,!1,!0))};const{latestValues:l,renderState:u,onUpdate:h}=a;this.onUpdate=h,this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=Yt(n),this.isVariantNode=_1(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:f,...d}=this.scrapeMotionValuesFromProps(n,{},this);for(const m in d){const p=d[m];l[m]!==void 0&&L(p)&&p.set(l[m],!1)}}mount(e){this.current=e,n2.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),hi.current||Qo(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:be.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Y(this.notifyUpdate),Y(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=it.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",o=>{this.latestValues[e]=o,this.props.onUpdate&&A.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=n.on("renderRequest",this.scheduleRender);let a;window.MotionCheckAppearSync&&(a=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),r(),a&&a(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in ht){const n=ht[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const r=this.features[e];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):w()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<L1.length;s++){const i=L1[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r="on"+i,a=e[r];a&&(this.propEventSubscriptions[i]=this.on(i,a))}this.prevMotionValues=s2(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=At(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){var s;let i=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(s=this.getBaseTargetFromProps(this.props,e))!==null&&s!==void 0?s:this.readValueFromInstance(this.current,e,this.options);return i!=null&&(typeof i=="string"&&(Ds(i)||vs(i))?i=parseFloat(i):!e2(i)&&Z.test(n)&&(i=Cs(e,n)),this.setBaseTarget(e,L(i)?i.get():i)),L(i)?i.get():i}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props;let i;if(typeof s=="string"||typeof s=="object"){const a=Ge(this.props,s,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);a&&(i=a[e])}if(s&&i!==void 0)return i;const r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!L(r)?r:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Je),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class fi extends i2{constructor(){super(...arguments),this.KeyframeResolver=Vs}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function a2(t){return window.getComputedStyle(t)}class r2 extends fi{constructor(){super(...arguments),this.type="html",this.renderInstance=ts}readValueFromInstance(e,n){if(it.has(n))return lr(e,n);{const s=a2(e),i=(Oe(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Ys(e,n)}build(e,n,s){Ue(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return He(e,n,s)}}class o2 extends fi{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=w,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&Q1(this.current,this.renderState)}}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(it.has(n)){const s=Ps(n);return s&&s.default||0}return n=es.has(n)?n:Ie(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return ss(e,n,s)}onBindTransform(){this.current&&!this.renderState.dimensions&&A.postRender(this.updateDimensions)}build(e,n,s){We(e,n,this.isSVGTag,s.transformTemplate)}renderInstance(e,n,s,i){ns(e,n,s,i)}mount(e){this.isSVGTag=$e(e.tagName),super.mount(e)}}const l2=(t,e)=>Ke(t)?new o2(e):new r2(e,{allowProjection:t!==c.Fragment}),c2=Aa({...T0,...Zo,...Bo,...Jo},l2),u2=_i(c2);function x2(){const{token:t}=Mi.useToken(),{t:e}=k1(),n=Pi.useBreakpoint(),[s,i]=c.useState("login"),{pageLayout:r,layoutButtonTrigger:a}=Si(),o=c.useMemo(()=>r==="layout-left",[r]),l=c.useMemo(()=>r==="layout-center",[r]),u=c.useMemo(()=>({formMode:s,setFormMode:i}),[s,i]);return C.jsxs("div",{style:{backgroundColor:t.colorBgContainer},children:[C.jsxs("header",{className:"z-10 absolute flex items-center right-3 top-3 left-3",children:[C.jsxs("div",{className:"text-colorText flex flex-1 items-center",children:[C.jsx("img",{alt:"App Logo",src:yi,className:"mr-2 w-11"}),C.jsx("h1",{className:"m-0 text-xl font-medium",children:"InApp2 管理后台"})]}),C.jsxs("div",{className:"flex items-center",children:[a,C.jsx(vi,{}),C.jsx(qi,{className:"px-2"})]})]}),C.jsx("div",{className:"flex items-center overflow-hidden h-full",children:C.jsxs(Ci,{className:pn("h-screen w-full",{"flex-row-reverse":o}),children:[C.jsx(gn,{xs:0,sm:0,lg:15,style:{backgroundImage:`radial-gradient(${t.colorBgContainer}, ${t.colorPrimaryBg})`},className:pn({hidden:l}),children:C.jsxs("div",{className:"flex flex-col items-center justify-center h-full gap-3",children:[C.jsx(bi,{className:"h-64 motion-safe:animate-bounceInDownOutUp"}),C.jsx("div",{className:"text-xl text-colorTextSecondary mt-6 font-sans lg:text-2xl",children:e("authority.pageTitle")}),C.jsx("div",{className:"text-colorTextTertiary mt-2",children:e("authority.pageDescription")})]})}),C.jsxs(gn,{xs:24,sm:24,lg:l?24:9,className:"relative flex flex-col justify-center px-6 py-10 xl:px-8",style:l||!n.xl&&!n.xxl&&!n.lg?{backgroundImage:`radial-gradient(${t.colorBgContainer}, ${t.colorPrimaryBg})`}:{},children:[C.jsx(xi,{className:"w-full absolute bottom-3 left-1/2 -translate-x-1/2"}),C.jsx("div",{className:"w-full sm:mx-auto md:max-w-md",children:C.jsx(Fi.Provider,{value:u,children:C.jsx(Li,{mode:"wait",initial:!1,children:C.jsx(u2.div,{initial:{x:30,opacity:0},animate:{x:0,opacity:1},exit:{x:0,opacity:0},transition:{duration:.3},children:Ai[s]},s)})})})]})]})})]})}export{x2 as default};
