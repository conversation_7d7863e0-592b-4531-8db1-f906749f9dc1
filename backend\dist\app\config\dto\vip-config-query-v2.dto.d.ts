export declare class VipConfigQueryDto {
    page?: number;
    pageSize?: number;
    vipLevel?: number;
    status?: number;
    levelName?: string;
    buybackEnabled?: boolean;
}
export declare class VipConfigListDto {
    id: number;
    vipLevel: number;
    levelName: string;
    strategicPosition: string;
    requiredExp: number;
    realMoneyRechargeExpRatio: number;
    realMoneyFlowExpRatio: number;
    goldPaymentExpRatio: number;
    goldFlowExpRatio: number;
    dailyTaskExpMin: number;
    dailyTaskExpMax: number;
    dailyGoldReward: number;
    buybackEnabled: boolean;
    buybackRate: number;
    c2cFeeRate: number;
    rakebackRate: number;
    withdrawalFeeRate: number;
    dailyWithdrawalLimit: number;
    withdrawalPriority: number;
    taskUnlockRequired: boolean;
    taskUnlockGamesRequired: number;
    status: number;
    remark: string;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator?: {
        id: number;
        username: string;
    };
    updater?: {
        id: number;
        username: string;
    };
}
export declare class VipExpAddDto {
    expType: 'real_money_recharge' | 'real_money_flow' | 'gold_payment' | 'gold_flow' | 'daily_task';
    amount: number;
    description?: string;
}
