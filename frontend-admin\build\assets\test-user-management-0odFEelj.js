import{j as e}from"./index-DD0gcXtR.js";import{a as r}from"./react-BUTTOX-3.js";import{u as d}from"./index-BpqjFj86.js";import{aq as m,d as g,s as n}from"./antd-FyCAPQKa.js";const h=()=>{const[c,t]=r.useState(!1),[a,l]=r.useState(null),o=async()=>{try{t(!0),console.log("🔍 开始测试API调用...");const s=await d.getUserList({page:1,pageSize:10});console.log("📊 API调用成功，结果:",s),l(s),n.success("API调用成功！")}catch(s){console.error("❌ API调用失败:",s);const i=s instanceof Error?s.message:String(s);n.error(`API调用失败: ${i}`)}finally{t(!1)}};return r.useEffect(()=>{console.log("🚀 测试用户管理组件已加载"),o()},[]),e.jsx("div",{className:"p-6",children:e.jsx(m,{title:"用户管理API测试",className:"mb-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(g,{type:"primary",onClick:o,loading:c,children:"测试API调用"}),a&&e.jsxs("div",{children:[e.jsx("h3",{children:"API返回数据："}),e.jsx("pre",{className:"bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(a,null,2)})]})]})})})};export{h as default};
