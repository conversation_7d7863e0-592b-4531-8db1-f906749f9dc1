"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigModule = void 0;
const common_1 = require("@nestjs/common");
const vip_config_v2_module_1 = require("./vip-config-v2.module");
const membership_card_config_module_1 = require("./membership-card-config.module");
const recharge_config_module_1 = require("./recharge-config.module");
const ad_config_module_1 = require("./ad-config.module");
const app_home_config_module_1 = require("./app-home-config.module");
let ConfigModule = class ConfigModule {
};
exports.ConfigModule = ConfigModule;
exports.ConfigModule = ConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            vip_config_v2_module_1.VipConfigV2Module,
            membership_card_config_module_1.MembershipCardConfigModule,
            recharge_config_module_1.RechargeConfigModule,
            ad_config_module_1.AdConfigModule,
            app_home_config_module_1.AppHomeConfigModule,
        ],
        exports: [
            vip_config_v2_module_1.VipConfigV2Module,
            membership_card_config_module_1.MembershipCardConfigModule,
            recharge_config_module_1.RechargeConfigModule,
            ad_config_module_1.AdConfigModule,
            app_home_config_module_1.AppHomeConfigModule,
        ],
    })
], ConfigModule);
//# sourceMappingURL=config.module.js.map