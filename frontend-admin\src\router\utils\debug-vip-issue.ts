/**
 * 专门调试VIP路由问题的工具
 */
export function debugVipIssue() {
  console.log('🚨 [VIP_DEBUG] ===== VIP路由问题调试开始 =====');
  
  // 1. 检查页面模块
  const pageModules = import.meta.glob([
    "/src/pages/**/*.tsx",
    "!/src/pages/exception/**/*.tsx",
  ]);
  
  const pageModulePaths = Object.keys(pageModules);
  
  // 2. 检查VIP相关路径
  const expectedVipPath = '/src/pages/config/vip-v2/index.tsx';
  const vipPathExists = pageModulePaths.includes(expectedVipPath);
  
  console.log('🔍 [VIP_DEBUG] VIP路径检查:');
  console.log(`  期望路径: ${expectedVipPath}`);
  console.log(`  路径存在: ${vipPathExists ? '✅ 是' : '❌ 否'}`);
  
  // 3. 显示所有config相关路径
  const configPaths = pageModulePaths.filter(path => path.includes('config'));
  console.log('📁 [VIP_DEBUG] 所有config路径:');
  configPaths.forEach(path => {
    console.log(`  ${path}`);
  });
  
  // 4. 模拟路径转换过程
  const testComponentPath = 'config/vip-v2';
  const normalizedPath = testComponentPath.startsWith('/') ? testComponentPath : `/${testComponentPath}`;
  const modulePath = `/src/pages${normalizedPath}/index.tsx`;
  const moduleIndex = pageModulePaths.findIndex(path => path === modulePath);
  
  console.log('🔄 [VIP_DEBUG] 路径转换过程:');
  console.log(`  原始组件路径: ${testComponentPath}`);
  console.log(`  标准化路径: ${normalizedPath}`);
  console.log(`  最终模块路径: ${modulePath}`);
  console.log(`  模块索引: ${moduleIndex}`);
  console.log(`  找到模块: ${moduleIndex !== -1 ? '✅ 是' : '❌ 否'}`);
  
  // 5. 如果找到模块，测试加载
  if (moduleIndex !== -1) {
    console.log('🎯 [VIP_DEBUG] 模块找到，尝试获取模块函数');
    const moduleFunction = pageModules[pageModulePaths[moduleIndex]];
    console.log(`  模块函数类型: ${typeof moduleFunction}`);
    console.log(`  模块函数: `, moduleFunction);
  } else {
    console.log('❌ [VIP_DEBUG] 模块未找到，显示相似路径:');
    const similarPaths = pageModulePaths.filter(path => 
      path.includes('vip') || path.includes('config')
    );
    similarPaths.forEach(path => {
      console.log(`  ${path}`);
    });
  }
  
  console.log('🚨 [VIP_DEBUG] ===== VIP路由问题调试结束 =====');
  
  return {
    vipPathExists,
    expectedVipPath,
    configPaths,
    moduleFound: moduleIndex !== -1,
    modulePath,
    moduleIndex
  };
}
