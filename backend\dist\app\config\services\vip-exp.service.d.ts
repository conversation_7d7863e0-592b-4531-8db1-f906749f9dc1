import { Repository } from 'typeorm';
import { VipConfigV2 } from '../entities/vip-config-v2.entity';
import { AppUser } from '../../entities/app-user.entity';
export declare class VipExpService {
    private readonly vipConfigRepository;
    private readonly appUserRepository;
    constructor(vipConfigRepository: Repository<VipConfigV2>, appUserRepository: Repository<AppUser>);
    calculateUserTotalExp(userId: number): Promise<{
        totalExp: number;
        breakdown: {
            realMoneyRecharge: number;
            realMoneyRechargeExp: number;
            realMoneyFlow: number;
            realMoneyFlowExp: number;
            goldPayment: number;
            goldPaymentExp: number;
            goldFlow: number;
            goldFlowExp: number;
            dailyTaskCount: number;
            dailyTaskExp: number;
        };
    }>;
    addExp(userId: number, expType: 'real_money_recharge' | 'real_money_flow' | 'gold_payment' | 'gold_flow' | 'daily_task', amount: number, description?: string): Promise<number>;
    checkAndUpgradeVipLevel(userId: number): Promise<{
        upgraded: boolean;
        oldLevel: number;
        newLevel: number;
        currentExp: number;
    }>;
    getUserVipBenefits(userId: number): Promise<{
        vipLevel: number;
        levelName: string;
        strategicPosition: string;
        currentExp: number;
        requiredExp: number;
        nextLevelExp: number;
        progressPercent: number;
        benefits: {
            dailyGoldReward: number;
            buybackEnabled: boolean;
            buybackRate: number;
            c2cFeeRate: number;
            rakebackRate: number;
            withdrawalFeeRate: number;
            dailyWithdrawalLimit: number;
            withdrawalPriority: number;
        };
    }>;
    recalculateAllUserVipLevels(): Promise<{
        processed: number;
        upgraded: number;
        downgraded: number;
    }>;
}
