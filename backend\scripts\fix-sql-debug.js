// 修复SQL调试的脚本
const { Client } = require('pg');

async function fixSQLDebug() {
  const client = new Client({
    host: 'db.ytrftwscazjboxbwnrxp.supabase.co',
    port: 5432,
    database: 'postgres',
    user: 'postgres',
    password: 'inapp2backend2024!',
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('🔍 修复SQL调试...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 检查用户角色关联表数据
    console.log('\n📋 检查用户角色关联表数据:');
    const userRoles = await client.query(`
      SELECT ur.user_id, ur.role_id, u.username, r.name as role_name, r.code as role_code
      FROM sys_user_roles ur
      LEFT JOIN sys_users u ON ur.user_id = u.id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE ur.user_id = 2
      ORDER BY ur.role_id;
    `);
    
    console.log(`admin用户的角色关联 (${userRoles.rows.length} 条记录):`);
    userRoles.rows.forEach(ur => {
      console.log(`  用户ID: ${ur.user_id}, 角色ID: ${ur.role_id}, 角色名称: ${ur.role_name}, 角色代码: ${ur.role_code}`);
    });

    // 2. 测试完整的LEFT JOIN查询
    console.log('\n🧪 测试完整的LEFT JOIN查询:');
    const fullQuery = await client.query(`
      SELECT 
        u.id, u.username, u.is_super_admin,
        r.id as role_id, r.name as role_name, r.code as role_code
      FROM sys_users u
      LEFT JOIN sys_user_roles ur ON u.id = ur.user_id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE u.username = $1
      ORDER BY r.id;
    `, ['admin']);
    
    console.log(`完整LEFT JOIN查询结果 (${fullQuery.rows.length} 条记录):`);
    fullQuery.rows.forEach(row => {
      console.log(`  用户: ${row.username} (ID: ${row.id}, 超级管理员: ${row.is_super_admin})`);
      if (row.role_id) {
        console.log(`    角色: ${row.role_name} (ID: ${row.role_id}, 代码: ${row.role_code})`);
      } else {
        console.log(`    ❌ 无角色关联`);
      }
    });

    // 3. 测试TypeORM风格的查询
    console.log('\n🔧 测试TypeORM风格的查询:');
    const typeormQuery = await client.query(`
      SELECT 
        "user"."id" AS "user_id", 
        "user"."username" AS "user_username", 
        "user"."is_super_admin" AS "user_is_super_admin",
        "role"."id" AS "role_id", 
        "role"."name" AS "role_name", 
        "role"."code" AS "role_code"
      FROM "sys_users" "user" 
      LEFT JOIN "sys_user_roles" "user_role" ON "user_role"."user_id"="user"."id" 
      LEFT JOIN "sys_roles" "role" ON "role"."id"="user_role"."role_id" 
      WHERE "user"."username" = $1
      ORDER BY "role"."id";
    `, ['admin']);
    
    console.log(`TypeORM风格查询结果 (${typeormQuery.rows.length} 条记录):`);
    typeormQuery.rows.forEach(row => {
      console.log(`  用户: ${row.user_username} (ID: ${row.user_id}, 超级管理员: ${row.user_is_super_admin})`);
      if (row.role_id) {
        console.log(`    角色: ${row.role_name} (ID: ${row.role_id}, 代码: ${row.role_code})`);
      } else {
        console.log(`    ❌ 无角色关联`);
      }
    });

    // 4. 检查表结构
    console.log('\n🏗️ 检查表结构:');
    
    // 检查sys_users表
    const usersTable = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'sys_users'
      ORDER BY ordinal_position;
    `);
    
    console.log('sys_users表结构:');
    usersTable.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (可空: ${col.is_nullable})`);
    });

    // 检查sys_user_roles表
    const userRolesTable = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'sys_user_roles'
      ORDER BY ordinal_position;
    `);
    
    console.log('\nsys_user_roles表结构:');
    userRolesTable.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (可空: ${col.is_nullable})`);
    });

    // 检查sys_roles表
    const rolesTable = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'sys_roles'
      ORDER BY ordinal_position;
    `);
    
    console.log('\nsys_roles表结构:');
    rolesTable.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (可空: ${col.is_nullable})`);
    });

    await client.end();
    console.log('\n🎉 调试完成！');

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    console.error(error.stack);
    await client.end();
    throw error;
  }
}

// 执行调试
if (require.main === module) {
  fixSQLDebug()
    .then(() => {
      console.log('✅ 调试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 调试失败:', error);
      process.exit(1);
    });
}

module.exports = { fixSQLDebug };
