import{u as w,c as y,a as b,b as C,j as s,d as T}from"./index-DD0gcXtR.js";import{h as k}from"./react-BUTTOX-3.js";import{A as a}from"./constants-BR8N8nwY.js";import{B}from"./index-C1Do55qr.js";import{au as x,aq as j,T as A,d as t}from"./antd-FyCAPQKa.js";const d={[a.admin]:{password:"123456789admin",username:a.admin},[a.common]:{password:"123456789admin",username:a.common}};function S(){const{t:e}=w(),r=k(),{enableFrontendAceess:o,enableBackendAccess:c,setPreferences:m}=y(),{roles:u}=b(),i=C(n=>n.reset),l=C(n=>n.login);function p(n){return u.includes(n)?"primary":"default"}function g(n){if(u.includes(n))return;const h=d[n];i(),h&&l(h).then(()=>{r(0)})}async function f(){var n;if(o&&!c){m({enableFrontendAceess:!1,enableBackendAccess:!0}),i(),l(d.admin).then(()=>{setTimeout(()=>{r(0)},150)});return}if(c&&!o){m({enableFrontendAceess:!0,enableBackendAccess:!1}),i(),l(d.admin).then(()=>{setTimeout(()=>{r(0)},150)});return}(n=window.$message)==null||n.warning(e("access.pageControl.warningMessage"))}return s.jsxs(B,{className:"flex flex-col gap-4",children:[s.jsx(x,{type:"info",message:e("access.pageControl.alertMessage"),description:e("access.pageControl.alertDescription")}),s.jsxs(j,{title:e("access.pageControl.cardTitle"),children:[s.jsx(x,{type:"warning",className:T("mb-4",{hidden:o!==c}),description:e("access.pageControl.warningMessage")}),s.jsxs("div",{className:"flex items-center gap-4",children:[e("access.pageControl.currentPermissionMode"),o?s.jsx(A.Text,{code:!0,children:e("access.pageControl.frontendControl")}):"",c?s.jsx(A.Text,{code:!0,children:e("access.pageControl.backendControl")}):"",c?s.jsx(t,{disabled:o===c,type:"primary",onClick:()=>f(),children:e("access.pageControl.switchToFrontend")}):null,o?s.jsx(t,{disabled:o===c,type:"primary",onClick:()=>f(),children:e("access.pageControl.switchToBackend")}):null]})]}),s.jsx(j,{title:e("access.pageControl.accountSwitching"),children:s.jsxs("div",{className:"flex gap-4",children:[s.jsx(t,{type:p(a.admin),onClick:()=>g(a.admin),children:e("access.pageControl.switchAdmin")}),s.jsx(t,{type:p(a.common),onClick:()=>g(a.common),children:e("access.pageControl.switchCommon")})]})})]})}export{S as default};
