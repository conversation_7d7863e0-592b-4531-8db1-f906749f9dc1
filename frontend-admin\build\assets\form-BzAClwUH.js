import{j as e}from"./index-DD0gcXtR.js";import{l as C,h as T,a as o}from"./react-BUTTOX-3.js";import{a as P,u as O,h as E}from"./index-CmJsfaTQ.js";import{S as _,I as w,C as z,B as R,a as G}from"./types-DmGJpzbp.js";import{av as a,ah as L,aq as c,Q as g,d as h,aj as q,I as t,p as n,q as v,ay as A,s as u}from"./antd-FyCAPQKa.js";const{TextArea:F}=t,{Option:m}=n;function k(){const{id:i}=C(),d=T(),[p]=a.useForm(),[I,x]=o.useState(!1),[b,j]=o.useState(!1),[V,f]=o.useState(null),r=!!i,y=async()=>{if(i){x(!0);try{const l=await P(Number(i));if(l.code===200){const s=l.result;f(s);const S={...s,rateValue:s.rateValue?Number(s.rateValue):void 0,minimumGuarantee:s.minimumGuarantee?Number(s.minimumGuarantee):void 0};p.setFieldsValue(S)}}catch{u.error("获取供应商信息失败"),d("/app/supplier")}finally{x(!1)}}};o.useEffect(()=>{r&&y()},[i]);const N=async l=>{console.log("表单提交的数据:",l),j(!0);try{let s;r&&i?(console.log("更新供应商，ID:",i),s=await O(Number(i),l)):(console.log("创建新供应商"),s=await E(l)),console.log("API响应:",s),s.code===200?(u.success(r?"更新成功":"创建成功"),d("/app/supplier")):u.error(s.message||(r?"更新失败":"创建失败"))}catch(s){console.error("保存失败:",s),u.error(r?"更新失败":"创建失败")}finally{j(!1)}};return r&&I?e.jsx("div",{className:"flex justify-center items-center h-96",children:e.jsx(L,{size:"large"})}):e.jsx("div",{className:"p-6",children:e.jsxs(c,{children:[e.jsx("div",{className:"mb-6",children:e.jsxs(g,{children:[e.jsx(h,{icon:e.jsx(q,{}),onClick:()=>d("/app/supplier"),children:"返回列表"}),e.jsx("h2",{className:"text-xl font-semibold mb-0",children:r?"编辑供应商":"新增供应商"})]})}),e.jsxs(a,{form:p,layout:"vertical",onFinish:N,initialValues:{status:"active",integrationType:"api_integration",billingCurrency:"USD"},children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(c,{title:"基本信息",size:"small",children:[e.jsx(a.Item,{label:"供应商名称",name:"name",rules:[{required:!0,message:"请输入供应商名称"}],children:e.jsx(t,{placeholder:"请输入供应商名称"})}),e.jsx(a.Item,{label:"供应商代码",name:"providerCode",rules:[{required:!0,message:"请输入供应商代码"},{pattern:/^[A-Z0-9_]+$/,message:"代码只能包含大写字母、数字和下划线"}],children:e.jsx(t,{placeholder:"例如: PG_SOFT"})}),e.jsx(a.Item,{label:"合作状态",name:"status",rules:[{required:!0,message:"请选择合作状态"}],children:e.jsx(n,{placeholder:"请选择合作状态",children:_.map(l=>e.jsx(m,{value:l.value,children:l.label},l.value))})}),e.jsx(a.Item,{label:"技术集成方案",name:"integrationType",rules:[{required:!0,message:"请选择技术集成方案"}],children:e.jsx(n,{placeholder:"请选择技术集成方案",children:w.map(l=>e.jsx(m,{value:l.value,children:l.label},l.value))})})]}),e.jsxs(c,{title:"商务条款",size:"small",children:[e.jsx(a.Item,{label:"合作模式",name:"commercialModelType",children:e.jsx(n,{placeholder:"请选择合作模式",allowClear:!0,children:z.map(l=>e.jsx(m,{value:l.value,children:l.label},l.value))})}),e.jsx(a.Item,{label:"费率/金额 (%)",name:"rateValue",rules:[{type:"number",min:0,max:100,message:"费率必须在0-100之间"}],children:e.jsx(v,{placeholder:"请输入费率",min:0,max:100,precision:4,style:{width:"100%"}})}),e.jsx(a.Item,{label:"结算周期",name:"billingCycle",children:e.jsx(n,{placeholder:"请选择结算周期",allowClear:!0,children:R.map(l=>e.jsx(m,{value:l.value,children:l.label},l.value))})}),e.jsx(a.Item,{label:"结算币种",name:"billingCurrency",children:e.jsx(n,{placeholder:"请选择结算币种",allowClear:!0,children:G.map(l=>e.jsx(m,{value:l.value,children:l.label},l.value))})}),e.jsx(a.Item,{label:"最低保证金额",name:"minimumGuarantee",rules:[{type:"number",min:0,message:"金额不能为负数"}],children:e.jsx(v,{placeholder:"请输入最低保证金额",min:0,precision:2,style:{width:"100%"}})})]}),e.jsxs(c,{title:"联系人信息",size:"small",children:[e.jsx(a.Item,{label:"商务经理",name:"amName",children:e.jsx(t,{placeholder:"请输入商务经理姓名"})}),e.jsx(a.Item,{label:"商务邮箱",name:"amEmail",rules:[{type:"email",message:"请输入正确的邮箱格式"}],children:e.jsx(t,{placeholder:"请输入商务经理邮箱"})}),e.jsx(a.Item,{label:"技术支持邮箱",name:"techSupportEmail",rules:[{type:"email",message:"请输入正确的邮箱格式"}],children:e.jsx(t,{placeholder:"请输入技术支持邮箱"})}),e.jsx(a.Item,{label:"财务联系邮箱",name:"financeEmail",rules:[{type:"email",message:"请输入正确的邮箱格式"}],children:e.jsx(t,{placeholder:"请输入财务联系邮箱"})})]}),e.jsx(c,{title:"备注",size:"small",children:e.jsx(a.Item,{label:"内部备注",name:"notes",children:e.jsx(F,{rows:6,placeholder:"请输入内部备注"})})})]}),e.jsx("div",{className:"mt-8 text-center",children:e.jsxs(g,{size:"large",children:[e.jsx(h,{size:"large",onClick:()=>d("/app/supplier"),children:"取消"}),e.jsx(h,{type:"primary",htmlType:"submit",loading:b,icon:e.jsx(A,{}),size:"large",children:r?"更新":"创建"})]})})]})]})})}export{k as default};
