"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../../system/auth/guards/jwt-auth.guard");
const vip_config_v2_service_1 = require("../services/vip-config-v2.service");
const create_vip_config_v2_dto_1 = require("../dto/create-vip-config-v2.dto");
const update_vip_config_v2_dto_1 = require("../dto/update-vip-config-v2.dto");
const vip_config_query_v2_dto_1 = require("../dto/vip-config-query-v2.dto");
let VipConfigController = class VipConfigController {
    vipConfigService;
    constructor(vipConfigService) {
        this.vipConfigService = vipConfigService;
    }
    async create(createVipConfigDto, req) {
        const result = await this.vipConfigService.create(createVipConfigDto, req.user.id);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAll(query) {
        const result = await this.vipConfigService.findAll(query);
        return {
            code: 200,
            message: '查询成功',
            result,
        };
    }
    async getVipLevelComparison() {
        const result = await this.vipConfigService.getVipLevelComparison();
        return {
            code: 200,
            message: '查询成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.vipConfigService.findOne(id);
        return {
            code: 200,
            message: '查询成功',
            result,
        };
    }
    async findByLevel(vipLevel) {
        const result = await this.vipConfigService.findByLevel(vipLevel);
        return {
            code: 200,
            message: '查询成功',
            result,
        };
    }
    async update(id, updateVipConfigDto, req) {
        const result = await this.vipConfigService.update(id, updateVipConfigDto, req.user.id);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id) {
        const result = await this.vipConfigService.remove(id);
        return {
            code: 200,
            message: '删除成功',
            result,
        };
    }
    async getUserVipBenefits(userId) {
        const result = await this.vipConfigService.getUserVipBenefits(userId);
        return {
            code: 200,
            message: '查询成功',
            result,
        };
    }
    async addUserExp(userId, addExpDto) {
        const expGained = await this.vipConfigService.addUserExp(userId, addExpDto.expType, addExpDto.amount, addExpDto.description);
        return {
            code: 200,
            message: 'EXP添加成功',
            result: {
                expGained,
                expType: addExpDto.expType,
                amount: addExpDto.amount,
            },
        };
    }
    async recalculateAllUserVipLevels() {
        const result = await this.vipConfigService.recalculateAllUserVipLevels();
        return {
            code: 200,
            message: '重新计算完成',
            result,
        };
    }
    async getVipStatsOverview() {
        return {
            code: 200,
            message: '查询成功',
            result: {
                totalConfigs: 0,
                totalUsers: 0,
                levelDistribution: [],
                expDistribution: [],
            },
        };
    }
    async getVipLevelDistribution() {
        return {
            code: 200,
            message: '查询成功',
            result: [],
        };
    }
    async checkUserBuybackPermission(userId) {
        const benefits = await this.vipConfigService.getUserVipBenefits(userId);
        return {
            code: 200,
            message: '查询成功',
            result: {
                hasPermission: benefits.benefits.buybackEnabled,
                buybackRate: benefits.benefits.buybackRate,
                vipLevel: benefits.vipLevel,
            },
        };
    }
    async checkUserWithdrawalPermission(userId) {
        const benefits = await this.vipConfigService.getUserVipBenefits(userId);
        return {
            code: 200,
            message: '查询成功',
            result: {
                feeRate: benefits.benefits.withdrawalFeeRate,
                dailyLimit: benefits.benefits.dailyWithdrawalLimit,
                priority: benefits.benefits.withdrawalPriority,
                vipLevel: benefits.vipLevel,
            },
        };
    }
    async checkUserC2CPermission(userId) {
        const benefits = await this.vipConfigService.getUserVipBenefits(userId);
        return {
            code: 200,
            message: '查询成功',
            result: {
                feeRate: benefits.benefits.c2cFeeRate,
                vipLevel: benefits.vipLevel,
            },
        };
    }
};
exports.VipConfigController = VipConfigController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建VIP配置' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'VIP等级已存在' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_vip_config_v2_dto_1.CreateVipConfigDto, Object]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '分页查询VIP配置列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [vip_config_query_v2_dto_1.VipConfigQueryDto]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('comparison'),
    (0, swagger_1.ApiOperation)({ summary: '获取VIP等级权益对比表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "getVipLevelComparison", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID查询VIP配置详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('level/:vipLevel'),
    (0, swagger_1.ApiOperation)({ summary: '根据VIP等级查询配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP配置不存在' }),
    __param(0, (0, common_1.Param)('vipLevel', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "findByLevel", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新VIP配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP配置不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'VIP等级已存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_vip_config_v2_dto_1.UpdateVipConfigDto, Object]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除VIP配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP配置不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '无法删除，有用户正在使用此等级' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('user/:userId/benefits'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户VIP权益详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "getUserVipBenefits", null);
__decorate([
    (0, common_1.Post)('user/:userId/add-exp'),
    (0, swagger_1.ApiOperation)({ summary: '为用户添加EXP经验值' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '添加成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, vip_config_query_v2_dto_1.VipExpAddDto]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "addUserExp", null);
__decorate([
    (0, common_1.Post)('recalculate-all'),
    (0, swagger_1.ApiOperation)({ summary: '重新计算所有用户的VIP等级' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '重新计算完成' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "recalculateAllUserVipLevels", null);
__decorate([
    (0, common_1.Get)('stats/overview'),
    (0, swagger_1.ApiOperation)({ summary: '获取VIP系统统计概览' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "getVipStatsOverview", null);
__decorate([
    (0, common_1.Get)('stats/level-distribution'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户VIP等级分布统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "getVipLevelDistribution", null);
__decorate([
    (0, common_1.Get)('user/:userId/check-buyback'),
    (0, swagger_1.ApiOperation)({ summary: '检查用户回购权限' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "checkUserBuybackPermission", null);
__decorate([
    (0, common_1.Get)('user/:userId/check-withdrawal'),
    (0, swagger_1.ApiOperation)({ summary: '检查用户提现权限' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "checkUserWithdrawalPermission", null);
__decorate([
    (0, common_1.Get)('user/:userId/check-c2c'),
    (0, swagger_1.ApiOperation)({ summary: '检查用户C2C交易权限' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "checkUserC2CPermission", null);
exports.VipConfigController = VipConfigController = __decorate([
    (0, swagger_1.ApiTags)('VIP经济权益配置管理 V12.0'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('config/vip-v2'),
    __metadata("design:paramtypes", [vip_config_v2_service_1.VipConfigV2Service])
], VipConfigController);
//# sourceMappingURL=vip-config-v2.controller.js.map