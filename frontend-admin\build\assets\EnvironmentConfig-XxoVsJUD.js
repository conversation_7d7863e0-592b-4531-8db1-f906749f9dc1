import{j as e}from"./index-DD0gcXtR.js";import{a}from"./react-BUTTOX-3.js";import{b as O,c as F,d as M,e as R,t as W,f as z}from"./index-CmJsfaTQ.js";import{E as B}from"./types-DmGJpzbp.js";import{av as r,d as i,u as D,az as L,M as U,p as k,S as V,I as o,D as _,Q as I,s as n,an as g,x as J,aC as $,aD as q,aE as Q}from"./antd-FyCAPQKa.js";const{TextArea:v}=o,{Option:Y}=k;function te({providerId:h}){const[c]=r.useForm(),[S,p]=a.useState(!1),[C,u]=a.useState(!1),[E,w]=a.useState([]),[b,f]=a.useState(!1),[l,y]=a.useState(null),d=async()=>{p(!0);try{const t=await O(h);t.code===200&&w(t.result)}catch{n.error("获取环境配置失败")}finally{p(!1)}};a.useEffect(()=>{d()},[h]);const j=t=>{var s;y(t||null),f(!0),t?c.setFieldsValue({...t,serverIpWhitelist:((s=t.serverIpWhitelist)==null?void 0:s.join(`
`))||"",extraConfig:t.extraConfig?JSON.stringify(t.extraConfig,null,2):""}):c.resetFields()},m=()=>{f(!1),y(null),c.resetFields()},T=async t=>{u(!0);try{const s={...t,serverIpWhitelist:t.serverIpWhitelist?t.serverIpWhitelist.split(`
`).filter(K=>K.trim()):[],extraConfig:t.extraConfig?JSON.parse(t.extraConfig):null};let x;l?x=await F(l.id,s):x=await M(h,s),x.code===200&&(n.success(l?"更新成功":"创建成功"),m(),d())}catch{n.error("保存失败")}finally{u(!1)}},P=async t=>{try{(await z(t)).code===200&&(n.success("删除成功"),d())}catch{n.error("删除失败")}},A=async t=>{try{(await W(t)).code===200&&(n.success("状态切换成功"),d())}catch{n.error("状态切换失败")}},N=[{title:"环境类型",dataIndex:"environmentType",key:"environmentType",width:120,render:t=>e.jsx(g,{color:t==="production"?"red":"blue",children:R(t)})},{title:"状态",dataIndex:"isActive",key:"isActive",width:100,render:t=>e.jsx(g,{color:t?"green":"red",children:t?"激活":"停用"})},{title:"API地址",dataIndex:"apiBaseUrl",key:"apiBaseUrl",ellipsis:!0},{title:"API Key",dataIndex:"apiKey",key:"apiKey",width:150,render:t=>t?"***"+t.slice(-4):"-"},{title:"运营商令牌",dataIndex:"operatorToken",key:"operatorToken",width:150,render:t=>t||"-"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180,render:t=>new Date(t).toLocaleString()},{title:"操作",key:"action",width:200,fixed:"right",render:(t,s)=>e.jsxs(I,{size:"small",children:[e.jsx(i,{type:"link",size:"small",icon:e.jsx(J,{}),onClick:()=>A(s.id),children:s.isActive?"停用":"激活"}),e.jsx(i,{type:"link",size:"small",icon:e.jsx($,{}),onClick:()=>j(s),children:"编辑"}),e.jsx(q,{title:"确定要删除这个环境配置吗？",onConfirm:()=>P(s.id),okText:"确定",cancelText:"取消",children:e.jsx(i,{type:"link",size:"small",danger:!0,icon:e.jsx(Q,{}),children:"删除"})})]})}];return e.jsxs("div",{children:[e.jsx("div",{className:"mb-4",children:e.jsx(i,{type:"primary",icon:e.jsx(D,{}),onClick:()=>j(),children:"新增环境配置"})}),e.jsx(L,{columns:N,dataSource:E,rowKey:"id",loading:S,pagination:!1,scroll:{x:1e3}}),e.jsx(U,{title:l?"编辑环境配置":"新增环境配置",open:b,onCancel:m,footer:null,width:800,destroyOnClose:!0,children:e.jsxs(r,{form:c,layout:"vertical",onFinish:T,children:[e.jsx(r.Item,{label:"环境类型",name:"environmentType",rules:[{required:!0,message:"请选择环境类型"}],children:e.jsx(k,{placeholder:"请选择环境类型",disabled:!!l,children:B.map(t=>e.jsx(Y,{value:t.value,children:t.label},t.value))})}),e.jsx(r.Item,{label:"激活状态",name:"isActive",valuePropName:"checked",children:e.jsx(V,{checkedChildren:"激活",unCheckedChildren:"停用"})}),e.jsx(r.Item,{label:"API根地址",name:"apiBaseUrl",rules:[{required:!0,message:"请输入API根地址"},{type:"url",message:"请输入正确的URL格式"}],children:e.jsx(o,{placeholder:"https://api.example.com"})}),e.jsx(r.Item,{label:"API Key",name:"apiKey",children:e.jsx(o,{placeholder:"请输入API Key"})}),e.jsx(r.Item,{label:"Secret Key",name:"secretKey",children:e.jsx(o.Password,{placeholder:"请输入Secret Key"})}),e.jsx(r.Item,{label:"运营商令牌",name:"operatorToken",children:e.jsx(o,{placeholder:"请输入运营商令牌"})}),e.jsx(r.Item,{label:"服务器IP白名单",name:"serverIpWhitelist",help:"每行一个IP地址",children:e.jsx(v,{rows:4,placeholder:`***********
***********`})}),e.jsx(r.Item,{label:"额外配置",name:"extraConfig",help:"JSON格式的额外配置参数",children:e.jsx(v,{rows:6,placeholder:'{"timeout": 30, "retry_count": 3}'})}),e.jsx(_,{}),e.jsx("div",{className:"text-right",children:e.jsxs(I,{children:[e.jsx(i,{onClick:m,children:"取消"}),e.jsx(i,{type:"primary",htmlType:"submit",loading:C,children:"保存"})]})})]})})]})}export{te as default};
