import{j as e}from"./index-DD0gcXtR.js";import{a as o}from"./react-BUTTOX-3.js";import{B as Z}from"./index-C1Do55qr.js";import{f as X,a as ee,b as se,c as te,d as ae}from"./index-B0R7VueJ.js";import{u as le}from"./supabase-upload-BnuJd1N_.js";import{av as i,aq as U,az as re,Q as v,d as x,u as ne,ak as oe,aP as ie,M as ce,I as u,p as C,aA as B,aE as E,ad as de,aQ as me,q as ge,s as r,an as b,S as he,n as F,aC as ue,aD as pe}from"./antd-FyCAPQKa.js";const{Option:Q}=C,{TextArea:xe}=u,je=[{value:1,label:"轮播"},{value:2,label:"弹窗广告"},{value:3,label:"浮点弹窗"},{value:4,label:"嵌入广告"},{value:5,label:"Banner"},{value:6,label:"插屏广告"},{value:7,label:"首页4宫格"}],Ae=[{value:1,label:"内部路由"},{value:2,label:"iframe页面"}],Ce=()=>{const[D,M]=o.useState([]),[R,T]=o.useState(!1),[q,p]=o.useState(!1),[y,I]=o.useState(null),[n]=i.useForm(),[j,O]=o.useState({page:1,pageSize:10}),[P,V]=o.useState(0),[w,k]=o.useState(!1),[fe,d]=o.useState([]),[m,A]=o.useState(null),[g,h]=o.useState([]),c=async t=>{T(!0);try{const s=await X(t||j);s.code===200?(M(s.result.list||[]),V(s.result.total||0)):r.error(s.message||"获取广告配置失败")}catch(s){r.error("获取广告配置失败"),console.error("获取广告配置失败:",s)}finally{T(!1)}},L=async t=>{try{const s=await ae(t);s.code===200?(r.success("创建成功"),p(!1),n.resetFields(),c()):r.error(s.message||"创建失败")}catch(s){r.error("创建失败"),console.error("创建广告配置失败:",s)}},Y=async(t,s)=>{try{const a=await te(t,s);a.code===200?(r.success("更新成功"),p(!1),c()):r.error(a.message||"更新失败")}catch(a){r.error("更新失败"),console.error("更新广告配置失败:",a)}},G=async t=>{try{const s=await se(t);s.code===200?(r.success("删除成功"),c()):r.error(s.message||"删除失败")}catch(s){r.error("删除失败"),console.error("删除广告配置失败:",s)}},N=async t=>{try{const s=await ee(t);s.code===200?(r.success(s.result.message),c()):r.error(s.message||"状态切换失败")}catch(s){r.error("状态切换失败"),console.error("状态切换失败:",s)}},H=async t=>{k(!0);try{if(!t.type.startsWith("image/"))throw new Error("只能上传图片文件");if(t.size>5*1024*1024)throw new Error("图片大小不能超过5MB");const s=await le(t,"ads",{maxWidth:1920,maxHeight:1080,quality:.8}),a={imageUrl:s,jumpType:1,jumpTarget:"",title:"",description:""},l=[...g,a];if(n.getFieldValue("adType")===7&&l.length>4)throw new Error("首页4宫格最多只能上传4张图片");return h(l),d(l.map(W=>W.imageUrl)),n.setFieldsValue({imageItems:l}),r.success("图片上传成功"),s}catch(s){const a=s instanceof Error?s.message:"图片上传失败";throw r.error(a),console.error("图片上传失败:",s),s}finally{k(!1)}},J=t=>{const s=g.filter((a,l)=>l!==t);h(s),d(s.map(a=>a.imageUrl)),n.setFieldsValue({imageItems:s})},f=(t,s,a)=>{const l=[...g];l[t]={...l[t],[s]:a},h(l),n.setFieldsValue({imageItems:l})},S=t=>{switch(t){case 1:return{min:2,max:10,text:"轮播广告至少需要2张图片"};case 7:return{min:4,max:4,text:"首页4宫格必须上传4张图片"};default:return{min:1,max:5,text:"至少需要1张图片"}}};o.useEffect(()=>{c()},[]);const $=[{title:"广告标识",dataIndex:"adIdentifier",key:"adIdentifier",width:150},{title:"广告类型",dataIndex:"adTypeName",key:"adTypeName",width:100,render:(t,s)=>e.jsx(b,{color:"blue",children:t})},{title:"标题",dataIndex:"title",key:"title",width:200},{title:"图片",dataIndex:"imageItems",key:"imageItems",width:120,render:(t,s)=>e.jsx("div",{style:{display:"flex",gap:4,flexWrap:"wrap"},children:t&&t.length>0?e.jsxs(e.Fragment,{children:[e.jsx(B,{width:40,height:30,src:t[0].imageUrl,style:{objectFit:"cover",borderRadius:4},fallback:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"}),t.length>1&&e.jsxs("span",{style:{fontSize:"12px",color:"#666",alignSelf:"center",background:"#f0f0f0",padding:"2px 6px",borderRadius:"4px"},children:["+",t.length-1]})]}):e.jsx("span",{style:{color:"#ccc",fontSize:"12px"},children:"无图片"})})},{title:"跳转配置",dataIndex:"imageItems",key:"jumpConfig",width:200,render:t=>{if(!t||t.length===0)return e.jsx("span",{style:{color:"#ccc"},children:"无配置"});const s=t[0];return t.every(l=>l.jumpType===s.jumpType&&l.jumpTarget===s.jumpTarget)?e.jsxs("div",{children:[e.jsx(b,{color:"green",children:s.jumpType===1?"内部路由":"外部链接"}),e.jsx("div",{style:{fontSize:"12px",color:"#666",marginTop:2},children:s.jumpTarget})]}):e.jsxs("div",{children:[e.jsx(b,{color:"blue",children:"多跳转配置"}),e.jsxs("div",{style:{fontSize:"12px",color:"#666",marginTop:2},children:[t.length," 个不同配置"]})]})}},{title:"排序",dataIndex:"sortOrder",key:"sortOrder",width:80,sorter:(t,s)=>t.sortOrder-s.sortOrder},{title:"状态",dataIndex:"status",key:"status",width:100,render:(t,s)=>e.jsx(he,{checked:t===1,onChange:()=>N(s.id),checkedChildren:"启用",unCheckedChildren:"禁用"})},{title:"操作",key:"action",width:150,render:(t,s)=>e.jsxs(v,{children:[e.jsx(F,{title:"编辑",children:e.jsx(x,{type:"primary",size:"small",icon:e.jsx(ue,{}),onClick:()=>{I(s);const a=s.imageItems||[],l=a.map(z=>z.imageUrl);h(a),d(l),A(s.adType),n.setFieldsValue({...s,imageItems:a}),p(!0)}})}),e.jsx(pe,{title:"确定要删除这个广告配置吗？",onConfirm:()=>G(s.id),okText:"确定",cancelText:"取消",children:e.jsx(F,{title:"删除",children:e.jsx(x,{danger:!0,size:"small",icon:e.jsx(E,{})})})})]})}],_=async t=>{y?await Y(y.id,t):await L(t)},K=()=>{p(!1),I(null),d([]),h([]),A(null),n.resetFields()};return e.jsxs(Z,{children:[e.jsx(U,{title:e.jsxs(v,{children:[e.jsx(ie,{}),"广告配置管理"]}),extra:e.jsxs(v,{children:[e.jsx(x,{type:"primary",icon:e.jsx(ne,{}),onClick:()=>{I(null),d([]),h([]),A(null),n.resetFields(),p(!0)},children:"新增广告"}),e.jsx(x,{icon:e.jsx(oe,{}),onClick:()=>c(),children:"刷新"})]}),children:e.jsx(re,{columns:$,dataSource:D,rowKey:"id",loading:R,pagination:{current:j.page,pageSize:j.pageSize,total:P,showSizeChanger:!0,showQuickJumper:!0,showTotal:(t,s)=>`第 ${s[0]}-${s[1]} 条/共 ${t} 条`,onChange:(t,s)=>{const a={...j,page:t,pageSize:s};O(a),c(a)}},scroll:{x:1200}})}),e.jsx(ce,{title:y?"编辑广告配置":"新增广告配置",open:q,onCancel:K,onOk:()=>n.submit(),width:800,confirmLoading:w,children:e.jsxs(i,{form:n,layout:"vertical",onFinish:_,children:[e.jsx(i.Item,{label:"广告标识",name:"adIdentifier",rules:[{required:!0,message:"请输入广告标识"},{pattern:/^[a-zA-Z0-9_]+$/,message:"广告标识只能包含字母、数字和下划线"}],children:e.jsx(u,{placeholder:"例如: banner_home_1"})}),e.jsx(i.Item,{label:"广告类型",name:"adType",rules:[{required:!0,message:"请选择广告类型"}],children:e.jsx(C,{placeholder:"请选择广告类型",onChange:t=>{A(t),d([]),n.setFieldsValue({images:[]})},children:je.map(t=>e.jsx(Q,{value:t.value,children:t.label},t.value))})}),e.jsx(i.Item,{label:"广告标题",name:"title",rules:[{required:!0,message:"请输入广告标题"}],children:e.jsx(u,{placeholder:"请输入广告标题"})}),e.jsx(i.Item,{label:"广告图片配置",name:"imageItems",rules:[{required:!0,message:"请上传广告图片"}],children:e.jsxs("div",{children:[m&&e.jsx("div",{style:{marginBottom:8,color:"#666",fontSize:"12px"},children:S(m).text}),g.map((t,s)=>e.jsx(U,{size:"small",style:{marginBottom:16},title:`图片 ${s+1}`,extra:e.jsx(x,{type:"text",danger:!0,size:"small",icon:e.jsx(E,{}),onClick:()=>J(s),children:"删除"}),children:e.jsxs("div",{style:{display:"flex",gap:16},children:[e.jsx("div",{children:e.jsx(B,{width:80,height:80,src:t.imageUrl,style:{objectFit:"cover",borderRadius:4}})}),e.jsxs("div",{style:{flex:1},children:[e.jsxs("div",{style:{display:"flex",gap:8,marginBottom:8},children:[e.jsxs("div",{style:{flex:1},children:[e.jsx("label",{style:{fontSize:"12px",color:"#666"},children:"标题"}),e.jsx(u,{size:"small",placeholder:"图片标题",value:t.title,onChange:a=>f(s,"title",a.target.value)})]}),e.jsxs("div",{style:{flex:1},children:[e.jsx("label",{style:{fontSize:"12px",color:"#666"},children:"跳转类型"}),e.jsx(C,{size:"small",value:t.jumpType,onChange:a=>f(s,"jumpType",a),style:{width:"100%"},children:Ae.map(a=>e.jsx(Q,{value:a.value,children:a.label},a.value))})]})]}),e.jsxs("div",{style:{marginBottom:8},children:[e.jsx("label",{style:{fontSize:"12px",color:"#666"},children:"跳转目标"}),e.jsx(u,{size:"small",placeholder:"内部路由: /games/hot 或 外部链接: https://example.com",value:t.jumpTarget,onChange:a=>f(s,"jumpTarget",a.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{style:{fontSize:"12px",color:"#666"},children:"描述"}),e.jsx(u,{size:"small",placeholder:"图片描述（可选）",value:t.description,onChange:a=>f(s,"description",a.target.value)})]})]})]})},s)),(!m||g.length<S(m).max)&&e.jsx(de,{name:"image",listType:"picture-card",className:"avatar-uploader",showUploadList:!1,beforeUpload:t=>(H(t),!1),accept:"image/*",disabled:w,children:e.jsxs("div",{style:{width:100,height:100,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[e.jsx(me,{}),e.jsx("div",{style:{marginTop:8,fontSize:"12px"},children:w?"上传中...":"上传图片"})]})}),m&&e.jsxs("div",{style:{marginTop:8,fontSize:"12px",color:"#999"},children:["已配置 ",g.length," / ",S(m).max," 张图片"]})]})}),e.jsx(i.Item,{label:"排序",name:"sortOrder",initialValue:0,children:e.jsx(ge,{min:0,max:9999,placeholder:"数字越小越靠前",style:{width:"100%"}})}),e.jsx(i.Item,{label:"备注",name:"remark",children:e.jsx(xe,{rows:3,placeholder:"请输入备注信息"})})]})})]})};export{Ce as default};
