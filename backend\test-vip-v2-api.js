// 使用Node.js内置模块进行HTTP请求
const https = require('https');
const http = require('http');
const { URL } = require('url');

const BASE_URL = 'http://localhost:3000/api';

// HTTP请求工具函数
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }

    req.end();
  });
}

// 模拟登录获取token
async function getAuthToken() {
  try {
    const response = await makeRequest(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: { username: 'admin', password: 'admin123' }
    });

    if (response.data.code === 200) {
      return response.data.result.token;
    } else {
      console.error('登录失败:', response.data.message);
      return null;
    }
  } catch (error) {
    console.error('登录请求失败:', error.message);
    return null;
  }
}

// 测试VIP V2 API
async function testVipV2APIs() {
  console.log('🧪 VIP V2 API测试开始');
  console.log('='.repeat(50));
  
  // 获取认证token
  console.log('1. 获取认证token...');
  const token = await getAuthToken();
  if (!token) {
    console.log('❌ 无法获取认证token，跳过API测试');
    return;
  }
  console.log('✅ 认证token获取成功');
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
  
  try {
    // 测试1: 获取VIP配置列表
    console.log('\n2. 测试获取VIP配置列表...');
    const listResponse = await makeRequest(`${BASE_URL}/config/vip-v2?page=1&pageSize=10`, {
      method: 'GET',
      headers
    });
    
    if (listResponse.data.code === 200) {
      console.log('✅ VIP配置列表获取成功');
      console.log(`📊 配置数量: ${listResponse.data.result.list.length}`);
      console.log(`📊 总数: ${listResponse.data.result.total}`);

      // 显示前3个配置的基本信息
      const configs = listResponse.data.result.list.slice(0, 3);
      console.log('📋 前3个VIP配置:');
      configs.forEach(config => {
        console.log(`   VIP${config.vipLevel}: ${config.levelName} (需要EXP: ${config.requiredExp})`);
      });
    } else {
      console.log('❌ VIP配置列表获取失败:', listResponse.data.message);
    }

    // 测试2: 获取特定VIP等级配置
    console.log('\n3. 测试获取VIP4配置...');
    const vip4Response = await makeRequest(`${BASE_URL}/config/vip-v2/level/4`, {
      method: 'GET',
      headers
    });
    
    if (vip4Response.data.code === 200) {
      const vip4 = vip4Response.data.result;
      console.log('✅ VIP4配置获取成功');
      console.log(`📋 VIP4详情:`);
      console.log(`   等级名称: ${vip4.levelName}`);
      console.log(`   战略定位: ${vip4.strategicPosition}`);
      console.log(`   需要EXP: ${vip4.requiredExp}`);
      console.log(`   回购权益: ${vip4.buybackEnabled ? '已开启' : '未开启'}`);
      if (vip4.buybackEnabled) {
        console.log(`   回购价格: ${vip4.buybackRate}:1`);
      }
      console.log(`   C2C费率: ${vip4.c2cFeeRate}%`);
      console.log(`   周返水: ${vip4.rakebackRate}%`);
    } else {
      console.log('❌ VIP4配置获取失败:', vip4Response.data.message);
    }
    
    // 测试3: 获取权益对比数据
    console.log('\n4. 测试获取权益对比数据...');
    const comparisonResponse = await makeRequest(`${BASE_URL}/config/vip-v2/comparison`, {
      method: 'GET',
      headers
    });

    if (comparisonResponse.data.code === 200) {
      console.log('✅ 权益对比数据获取成功');
      console.log(`📊 对比数据条数: ${comparisonResponse.data.result.length}`);
    } else {
      console.log('❌ 权益对比数据获取失败:', comparisonResponse.data.message);
    }

    
  } catch (error) {
    console.error('❌ API测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('   响应状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
  }
  
  console.log('\n🎉 VIP V2 API测试完成！');
  console.log('='.repeat(50));
}

// 运行测试
if (require.main === module) {
  testVipV2APIs().catch(console.error);
}

module.exports = { testVipV2APIs };
