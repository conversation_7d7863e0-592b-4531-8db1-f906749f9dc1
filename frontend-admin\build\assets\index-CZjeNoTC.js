import{j as a}from"./index-DD0gcXtR.js";import{h as k,a as l}from"./react-BUTTOX-3.js";import{i as O,j as E,k as P,l as R,m as z}from"./index-CmJsfaTQ.js";import{S as M,I as N,C as $}from"./types-DmGJpzbp.js";import{aq as L,ao as b,ap as n,I as D,p as o,Q as y,d as r,u as A,ak as _,az as B,s as u,an as Q,x as q,aC as F,aD as U,aE as V}from"./antd-FyCAPQKa.js";const{Search:G}=D,{Option:x}=o;function X(){const c=k(),[g,m]=l.useState(!1),[j,S]=l.useState([]),[w,C]=l.useState(0),[s,i]=l.useState({page:1,pageSize:10,sortBy:"createdAt",sortOrder:"DESC"}),d=async()=>{m(!0);try{const e=await P(s);e.code===200&&(S(e.result.list),C(e.result.total))}catch{u.error("获取数据失败")}finally{m(!1)}};l.useEffect(()=>{d()},[s]);const f=e=>{i(t=>({...t,name:e,page:1}))},p=(e,t)=>{i(h=>({...h,[e]:t,page:1}))},I=()=>{i({page:1,pageSize:10,sortBy:"createdAt",sortOrder:"DESC"})},v=async e=>{try{(await z(e)).code===200&&(u.success("删除成功"),d())}catch{u.error("删除失败")}},T=[{title:"供应商名称",dataIndex:"name",key:"name",width:200,ellipsis:!0},{title:"供应商代码",dataIndex:"providerCode",key:"providerCode",width:150},{title:"合作状态",dataIndex:"status",key:"status",width:120,render:e=>{const t={active:"green",inactive:"red",testing:"orange",suspended:"volcano"};return a.jsx(Q,{color:t[e]||"default",children:R(e)})}},{title:"集成类型",dataIndex:"integrationType",key:"integrationType",width:120,render:e=>O(e)},{title:"商务模式",dataIndex:"commercialModelType",key:"commercialModelType",width:120,render:e=>e?E(e):"-"},{title:"费率",dataIndex:"rateValue",key:"rateValue",width:100,render:e=>e?`${e}%`:"-"},{title:"商务经理",dataIndex:"amName",key:"amName",width:120,render:e=>e||"-"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180,render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",width:200,fixed:"right",render:(e,t)=>a.jsxs(y,{size:"small",children:[a.jsx(r,{type:"link",size:"small",icon:a.jsx(q,{}),onClick:()=>c(`/app/supplier/${t.id}`),children:"配置"}),a.jsx(r,{type:"link",size:"small",icon:a.jsx(F,{}),onClick:()=>c(`/app/supplier/${t.id}/edit`),children:"编辑"}),a.jsx(U,{title:"确定要删除这个供应商吗？",description:"删除后将无法恢复，请谨慎操作。",onConfirm:()=>v(t.id),okText:"确定",cancelText:"取消",children:a.jsx(r,{type:"link",size:"small",danger:!0,icon:a.jsx(V,{}),children:"删除"})})]})}];return a.jsx("div",{className:"p-6",children:a.jsxs(L,{children:[a.jsx("div",{className:"mb-4",children:a.jsxs(b,{gutter:[16,16],children:[a.jsx(n,{span:6,children:a.jsx(G,{placeholder:"搜索供应商名称",allowClear:!0,onSearch:f,style:{width:"100%"}})}),a.jsx(n,{span:4,children:a.jsx(o,{placeholder:"合作状态",allowClear:!0,style:{width:"100%"},onChange:e=>p("status",e),value:s.status,children:M.map(e=>a.jsx(x,{value:e.value,children:e.label},e.value))})}),a.jsx(n,{span:4,children:a.jsx(o,{placeholder:"集成类型",allowClear:!0,style:{width:"100%"},onChange:e=>p("integrationType",e),value:s.integrationType,children:N.map(e=>a.jsx(x,{value:e.value,children:e.label},e.value))})}),a.jsx(n,{span:4,children:a.jsx(o,{placeholder:"商务模式",allowClear:!0,style:{width:"100%"},onChange:e=>p("commercialModelType",e),value:s.commercialModelType,children:$.map(e=>a.jsx(x,{value:e.value,children:e.label},e.value))})}),a.jsx(n,{span:6,children:a.jsxs(y,{children:[a.jsx(r,{type:"primary",icon:a.jsx(A,{}),onClick:()=>c("/app/supplier/create"),children:"新增供应商"}),a.jsx(r,{icon:a.jsx(_,{}),onClick:d,children:"刷新"}),a.jsx(r,{onClick:I,children:"重置"})]})})]})}),a.jsx(B,{columns:T,dataSource:j,rowKey:"id",loading:g,scroll:{x:1400},pagination:{current:s.page,pageSize:s.pageSize,total:w,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`,onChange:(e,t)=>{i(h=>({...h,page:e,pageSize:t}))}}})]})})}export{X as default};
