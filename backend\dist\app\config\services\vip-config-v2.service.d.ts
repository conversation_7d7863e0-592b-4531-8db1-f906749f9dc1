import { Repository } from 'typeorm';
import { VipConfigV2 } from '../entities/vip-config-v2.entity';
import { AppUser } from '../../entities/app-user.entity';
import { CreateVipConfigDto } from '../dto/create-vip-config-v2.dto';
import { UpdateVipConfigDto } from '../dto/update-vip-config-v2.dto';
import { VipConfigQueryDto } from '../dto/vip-config-query-v2.dto';
import { VipExpService } from './vip-exp.service';
export declare class VipConfigV2Service {
    private readonly vipConfigRepository;
    private readonly appUserRepository;
    private readonly vipExpService;
    constructor(vipConfigRepository: Repository<VipConfigV2>, appUserRepository: Repository<AppUser>, vipExpService: VipExpService);
    create(createVipConfigDto: CreateVipConfigDto, userId: number): Promise<VipConfigV2>;
    findAll(query: VipConfigQueryDto): Promise<{
        list: VipConfigV2[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    findOne(id: number): Promise<VipConfigV2>;
    findByLevel(vipLevel: number): Promise<VipConfigV2>;
    update(id: number, updateVipConfigDto: UpdateVipConfigDto, userId: number): Promise<VipConfigV2>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getUserVipBenefits(userId: number): Promise<{
        vipLevel: number;
        levelName: string;
        strategicPosition: string;
        currentExp: number;
        requiredExp: number;
        nextLevelExp: number;
        progressPercent: number;
        benefits: {
            dailyGoldReward: number;
            buybackEnabled: boolean;
            buybackRate: number;
            c2cFeeRate: number;
            rakebackRate: number;
            withdrawalFeeRate: number;
            dailyWithdrawalLimit: number;
            withdrawalPriority: number;
        };
    }>;
    addUserExp(userId: number, expType: string, amount: number, description?: string): Promise<number>;
    recalculateAllUserVipLevels(): Promise<{
        processed: number;
        upgraded: number;
        downgraded: number;
    }>;
    getVipLevelComparison(): Promise<{
        vipLevel: number;
        levelName: string;
        strategicPosition: string;
        requiredExp: number;
        dailyGoldReward: number;
        buybackRateDisplay: string;
        c2cFeeRate: string;
        rakebackRate: string;
        withdrawalFeeRate: string;
        withdrawalLimitDisplay: string;
        withdrawalPriorityDisplay: string;
    }[]>;
    private validateExpIncrement;
    private validateBusinessRules;
}
