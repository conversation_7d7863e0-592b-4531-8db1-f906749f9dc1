import { Repository } from 'typeorm';
import { VipConfig } from '../entities/vip-config-v2.entity';
import { AppUser } from '../../entities/app-user.entity';
import { CreateVipConfigDto } from '../dto/create-vip-config-v2.dto';
import { UpdateVipConfigDto } from '../dto/update-vip-config-v2.dto';
import { VipConfigQueryDto } from '../dto/vip-config-query-v2.dto';
import { VipExpService } from './vip-exp.service';
export declare class VipConfigService {
    private readonly vipConfigRepository;
    private readonly appUserRepository;
    private readonly vipExpService;
    constructor(vipConfigRepository: Repository<VipConfig>, appUserRepository: Repository<AppUser>, vipExpService: VipExpService);
    create(createVipConfigDto: CreateVipConfigDto, userId: number): Promise<VipConfig>;
    findAll(query: VipConfigQueryDto): Promise<{
        list: VipConfig[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    findOne(id: number): Promise<VipConfig>;
    findByLevel(vipLevel: number): Promise<VipConfig>;
    update(id: number, updateVipConfigDto: UpdateVipConfigDto, userId: number): Promise<VipConfig>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getUserVipBenefits(userId: number): Promise<{
        vipLevel: number;
        levelName: string;
        strategicPosition: string;
        currentExp: number;
        requiredExp: number;
        nextLevelExp: number;
        progressPercent: number;
        benefits: {
            dailyGoldReward: number;
            buybackEnabled: boolean;
            buybackRate: number;
            c2cFeeRate: number;
            rakebackRate: number;
            withdrawalFeeRate: number;
            dailyWithdrawalLimit: number;
            withdrawalPriority: number;
        };
    }>;
    addUserExp(userId: number, expType: string, amount: number, description?: string): Promise<number>;
    recalculateAllUserVipLevels(): Promise<{
        processed: number;
        upgraded: number;
        downgraded: number;
    }>;
    getVipLevelComparison(): Promise<{
        vipLevel: number;
        levelName: string;
        strategicPosition: string;
        requiredExp: number;
        dailyGoldReward: number;
        buybackRateDisplay: string;
        c2cFeeRate: string;
        rakebackRate: string;
        withdrawalFeeRate: string;
        withdrawalLimitDisplay: string;
        withdrawalPriorityDisplay: string;
    }[]>;
    private validateExpIncrement;
    private validateBusinessRules;
}
