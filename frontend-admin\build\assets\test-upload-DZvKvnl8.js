import{j as s}from"./index-DD0gcXtR.js";import{a as f}from"./react-BUTTOX-3.js";import{a as z,u as U,b as I,d as C}from"./supabase-upload-BnuJd1N_.js";import{Q as k,aq as i,ad as m,d,aQ as x,by as E,aA as F,aE as T,s as t}from"./antd-FyCAPQKa.js";const O=()=>{const[p,r]=f.useState(!1),[g,n]=f.useState([]),[j,u]=f.useState(0),y=async a=>{r(!0);try{const e=await z(a);n(l=>[...l,e]),t.success("图片上传成功！"),console.log("上传成功:",e)}catch(e){t.error(`上传失败: ${e instanceof Error?e.message:"未知错误"}`),console.error("上传失败:",e)}finally{r(!1)}},b=async a=>{r(!0);try{const e=await U(a,"ads",{maxWidth:1280,maxHeight:720,quality:.8});n(l=>[...l,e]),t.success("压缩图片上传成功！"),console.log("压缩上传成功:",e)}catch(e){t.error(`压缩上传失败: ${e instanceof Error?e.message:"未知错误"}`),console.error("压缩上传失败:",e)}finally{r(!1)}},w=async a=>{r(!0),u(0);try{const e=await I(a,"ads",(o,$,v)=>{u(o),console.log(`上传进度: ${o.toFixed(1)}% (${$}/${v})`)}),l=e.filter(o=>o.success).map(o=>o.url).filter(Boolean),c=e.filter(o=>!o.success).length;n(o=>[...o,...l]),c>0?t.warning(`批量上传完成，${l.length}个成功，${c}个失败`):t.success(`批量上传成功，共${l.length}个文件`),console.log("批量上传结果:",e)}catch(e){t.error(`批量上传失败: ${e instanceof Error?e.message:"未知错误"}`),console.error("批量上传失败:",e)}finally{r(!1),u(0)}},S=async a=>{try{await C(a)?(n(l=>l.filter(c=>c!==a)),t.success("图片删除成功！")):t.error("图片删除失败")}catch(e){t.error(`删除失败: ${e instanceof Error?e.message:"未知错误"}`),console.error("删除失败:",e)}},h={beforeUpload:a=>!1,showUploadList:!1};return s.jsxs("div",{style:{padding:"24px"},children:[s.jsx("h1",{children:"Supabase 上传功能测试"}),s.jsxs(k,{direction:"vertical",size:"large",style:{width:"100%"},children:[s.jsx(i,{title:"单个图片上传测试",size:"small",children:s.jsx(m,{...h,onChange:a=>{var l;const e=(l=a.fileList[a.fileList.length-1])==null?void 0:l.originFileObj;e&&y(e)},children:s.jsx(d,{icon:s.jsx(x,{}),loading:p,children:"选择图片上传"})})}),s.jsx(i,{title:"压缩图片上传测试",size:"small",children:s.jsx(m,{...h,onChange:a=>{var l;const e=(l=a.fileList[a.fileList.length-1])==null?void 0:l.originFileObj;e&&b(e)},children:s.jsx(d,{icon:s.jsx(x,{}),loading:p,children:"选择图片压缩上传 (1280x720, 80%质量)"})})}),s.jsxs(i,{title:"批量图片上传测试",size:"small",children:[s.jsx(m,{...h,multiple:!0,onChange:a=>{const e=a.fileList.map(l=>l.originFileObj).filter(Boolean);e.length>0&&w(e)},children:s.jsx(d,{icon:s.jsx(x,{}),loading:p,children:"选择多个图片批量上传"})}),j>0&&s.jsx("div",{style:{marginTop:"12px"},children:s.jsx(E,{percent:Math.round(j)})})]}),g.length>0&&s.jsx(i,{title:`已上传图片 (${g.length})`,size:"small",children:s.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(200px, 1fr))",gap:"16px"},children:g.map((a,e)=>s.jsxs("div",{style:{position:"relative"},children:[s.jsx(F,{src:a,alt:`上传图片 ${e+1}`,style:{width:"100%",height:"150px",objectFit:"cover"},preview:{mask:"预览"}}),s.jsx(d,{type:"primary",danger:!0,size:"small",icon:s.jsx(T,{}),style:{position:"absolute",top:"8px",right:"8px",opacity:.8},onClick:()=>S(a),children:"删除"}),s.jsx("div",{style:{position:"absolute",bottom:"0",left:"0",right:"0",background:"rgba(0,0,0,0.7)",color:"white",padding:"4px 8px",fontSize:"12px",wordBreak:"break-all"},children:a.split("/").pop()})]},e))})}),s.jsx(i,{title:"当前配置信息",size:"small",children:s.jsx("pre",{style:{background:"#f5f5f5",padding:"12px",borderRadius:"4px",fontSize:"12px"},children:JSON.stringify({environment:"production",supabaseUrl:"https://ytrftwscazjboxbwnrxp.supabase.co",bucket:"inda",folder:"ads",maxFileSize:"10485760",allowedTypes:"image/jpeg,image/png,image/gif,image/svg+xml,image/webp"},null,2)})})]})]})};export{O as default};
