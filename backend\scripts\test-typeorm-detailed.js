// 详细测试TypeORM的脚本
const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testTypeORMDetailed() {
  try {
    console.log('🚀 启动NestJS应用...');
    const app = await NestFactory.createApplicationContext(AppModule);
    console.log('✅ 应用启动成功');

    const { DataSource } = require('typeorm');
    const { SysUser } = require('../dist/system/entities/sys-user.entity');
    const dataSource = app.get(DataSource);
    const userRepository = dataSource.getRepository(SysUser);

    console.log('\n🔍 详细测试TypeORM查询...');

    // 1. 测试基本查询（不包含关系）
    console.log('\n📋 测试1: 基本查询（不包含关系）');
    const basicUser = await userRepository.findOne({
      where: { username: 'admin' },
      select: ['id', 'username', 'email', 'status', 'isSuperAdmin']
    });
    
    if (basicUser) {
      console.log(`  用户: ${basicUser.username} (ID: ${basicUser.id})`);
      console.log(`  超级管理员: ${basicUser.isSuperAdmin}`);
      console.log(`  roles属性存在: ${basicUser.hasOwnProperty('roles')}`);
      console.log(`  roles值: ${basicUser.roles}`);
    }

    // 2. 测试包含关系的查询
    console.log('\n📋 测试2: 包含关系的查询');
    const userWithRoles = await userRepository.findOne({
      where: { username: 'admin' },
      relations: ['roles']
    });
    
    if (userWithRoles) {
      console.log(`  用户: ${userWithRoles.username} (ID: ${userWithRoles.id})`);
      console.log(`  超级管理员: ${userWithRoles.isSuperAdmin}`);
      console.log(`  roles属性存在: ${userWithRoles.hasOwnProperty('roles')}`);
      console.log(`  roles类型: ${typeof userWithRoles.roles}`);
      console.log(`  roles是数组: ${Array.isArray(userWithRoles.roles)}`);
      console.log(`  roles长度: ${userWithRoles.roles ? userWithRoles.roles.length : 'undefined'}`);
      console.log(`  roles内容: ${JSON.stringify(userWithRoles.roles)}`);
      
      if (userWithRoles.roles && userWithRoles.roles.length > 0) {
        userWithRoles.roles.forEach((role, index) => {
          console.log(`    角色${index + 1}: ${role.name} (${role.code})`);
        });
      }
    }

    // 3. 测试QueryBuilder查询
    console.log('\n📋 测试3: QueryBuilder查询');
    const qbUser = await userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where('user.username = :username', { username: 'admin' })
      .getOne();
    
    if (qbUser) {
      console.log(`  用户: ${qbUser.username} (ID: ${qbUser.id})`);
      console.log(`  超级管理员: ${qbUser.isSuperAdmin}`);
      console.log(`  roles属性存在: ${qbUser.hasOwnProperty('roles')}`);
      console.log(`  roles类型: ${typeof qbUser.roles}`);
      console.log(`  roles是数组: ${Array.isArray(qbUser.roles)}`);
      console.log(`  roles长度: ${qbUser.roles ? qbUser.roles.length : 'undefined'}`);
      console.log(`  roles内容: ${JSON.stringify(qbUser.roles)}`);
      
      if (qbUser.roles && qbUser.roles.length > 0) {
        qbUser.roles.forEach((role, index) => {
          console.log(`    角色${index + 1}: ${role.name} (${role.code})`);
        });
      }
    }

    // 4. 测试原生查询并手动映射
    console.log('\n📋 测试4: 原生查询并手动映射');
    const rawResults = await dataSource.query(`
      SELECT 
        u.id, u.username, u.email, u.is_super_admin,
        r.id as role_id, r.name as role_name, r.code as role_code
      FROM sys_users u
      LEFT JOIN sys_user_roles ur ON u.id = ur.user_id
      LEFT JOIN sys_roles r ON ur.role_id = r.id
      WHERE u.username = $1
      ORDER BY r.id
    `, ['admin']);
    
    console.log(`  原生查询结果数量: ${rawResults.length}`);
    
    // 手动映射结果
    const userMap = new Map();
    rawResults.forEach(row => {
      if (!userMap.has(row.id)) {
        userMap.set(row.id, {
          id: row.id,
          username: row.username,
          email: row.email,
          isSuperAdmin: row.is_super_admin,
          roles: []
        });
      }
      
      if (row.role_id) {
        userMap.get(row.id).roles.push({
          id: row.role_id,
          name: row.role_name,
          code: row.role_code
        });
      }
    });
    
    const mappedUser = userMap.get(2); // admin用户ID是2
    if (mappedUser) {
      console.log(`  映射用户: ${mappedUser.username} (ID: ${mappedUser.id})`);
      console.log(`  超级管理员: ${mappedUser.isSuperAdmin}`);
      console.log(`  角色数量: ${mappedUser.roles.length}`);
      mappedUser.roles.forEach((role, index) => {
        console.log(`    角色${index + 1}: ${role.name} (${role.code})`);
      });
    }

    // 5. 测试关系元数据
    console.log('\n📋 测试5: 检查关系元数据');
    const userMetadata = dataSource.getMetadata(SysUser);
    const rolesRelation = userMetadata.findRelationWithPropertyPath('roles');
    
    if (rolesRelation) {
      console.log(`  关系名称: ${rolesRelation.propertyName}`);
      console.log(`  关系类型: ${rolesRelation.relationType}`);
      console.log(`  目标实体: ${rolesRelation.type.name}`);
      console.log(`  连接表: ${rolesRelation.joinTableName}`);
      console.log(`  是否延迟加载: ${rolesRelation.isLazy}`);
      console.log(`  是否急切加载: ${rolesRelation.isEager}`);
    } else {
      console.log('  ❌ 未找到roles关系');
    }

    await app.close();
    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 执行测试
if (require.main === module) {
  testTypeORMDetailed()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testTypeORMDetailed };
