import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { SysUser } from '../../../system/entities/sys-user.entity';

/**
 * VIP配置实体 - V12.0 经济权益系统
 * 支持完整的VIP经济权益配置，包括EXP系统、回购权益、C2C权益、真金系统权益等
 */
@Entity('vip_configs')
export class VipConfig {
  @PrimaryGeneratedColumn()
  id: number;

  // ==================== 基础等级信息 ====================
  @Column({ name: 'vip_level', unique: true, comment: 'VIP等级，从0开始，唯一' })
  vipLevel: number;

  @Column({ name: 'level_name', length: 50, comment: '等级名称显示' })
  levelName: string;

  @Column({ name: 'strategic_position', length: 200, nullable: true, comment: '核心战略定位描述' })
  strategicPosition: string;

  // ==================== EXP经验值系统 ====================
  @Column({ name: 'required_exp', type: 'bigint', comment: '达到该等级所需的累计EXP经验值' })
  requiredExp: number;

  // EXP获取规则配置
  @Column({ 
    name: 'real_money_recharge_exp_ratio', 
    type: 'decimal', 
    precision: 10, 
    scale: 4, 
    default: 10.0000,
    comment: '真金充值EXP比例 (1 INR = 10 EXP)'
  })
  realMoneyRechargeExpRatio: number;

  @Column({
    name: 'real_money_flow_exp_ratio',
    type: 'decimal',
    precision: 10,
    scale: 4,
    default: 1.0000,
    comment: '真金流水EXP比例 (1 INR = 1 EXP)'
  })
  realMoneyFlowExpRatio: number;

  @Column({
    name: 'gold_payment_exp_ratio',
    type: 'decimal',
    precision: 10,
    scale: 4,
    default: 5.0000,
    comment: '金币付费EXP比例 (1 INR = 5 EXP)'
  })
  goldPaymentExpRatio: number;

  @Column({
    name: 'gold_flow_exp_ratio',
    type: 'decimal',
    precision: 10,
    scale: 6,
    default: 0.001000,
    comment: '金币流水EXP比例 (1000金币 = 1 EXP)'
  })
  goldFlowExpRatio: number;

  @Column({ name: 'daily_task_exp_min', default: 10, comment: '活跃任务最小EXP' })
  dailyTaskExpMin: number;

  @Column({ name: 'daily_task_exp_max', default: 50, comment: '活跃任务最大EXP' })
  dailyTaskExpMax: number;

  // ==================== 每日奖励系统 ====================
  @Column({ name: 'daily_gold_reward', type: 'bigint', default: 0, comment: '每日可领取金币数量' })
  dailyGoldReward: number;

  // ==================== 官方回购系统权益 ====================
  @Column({ name: 'buyback_enabled', default: false, comment: '是否开启官方回购资格' })
  buybackEnabled: boolean;

  @Column({ 
    name: 'buyback_rate', 
    nullable: true, 
    comment: '官方回购价格 (金币:1 INR)，如140表示140:1' 
  })
  buybackRate: number;

  // ==================== C2C交易系统权益 ====================
  @Column({ 
    name: 'c2c_fee_rate', 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    default: 15.00, 
    comment: 'C2C手续费率(%)' 
  })
  c2cFeeRate: number;

  // ==================== 真金系统权益 ====================
  @Column({ 
    name: 'rakeback_rate', 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    default: 0.00, 
    comment: '周返水比例(%)' 
  })
  rakebackRate: number;

  @Column({ 
    name: 'withdrawal_fee_rate', 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    default: 5.00, 
    comment: '提现手续费率(%)' 
  })
  withdrawalFeeRate: number;

  @Column({ 
    name: 'daily_withdrawal_limit', 
    type: 'bigint', 
    nullable: true, 
    comment: '每日提现额度上限(INR)，NULL表示无上限' 
  })
  dailyWithdrawalLimit: number;

  @Column({ 
    name: 'withdrawal_priority', 
    default: 1, 
    comment: '提现优先级 1-普通 2-优先 3-VIP专享' 
  })
  withdrawalPriority: number;

  // ==================== 解锁机制配置 ====================
  @Column({ name: 'task_unlock_required', default: false, comment: '活跃任务是否需要游戏局数解锁' })
  taskUnlockRequired: boolean;

  @Column({ name: 'task_unlock_games_required', default: 0, comment: '解锁所需游戏局数' })
  taskUnlockGamesRequired: number;

  // ==================== 状态和管理字段 ====================
  @Column({ default: 1, comment: '状态：1-启用，0-禁用' })
  status: number;

  @Column({ type: 'text', nullable: true, comment: '备注说明' })
  remark: string;

  // ==================== 审计字段 ====================
  @Column({ name: 'created_by', nullable: true, comment: '创建人ID' })
  createdBy: number;

  @Column({ name: 'updated_by', nullable: true, comment: '最后更新人ID' })
  updatedBy: number;

  @CreateDateColumn({ name: 'create_time' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time' })
  updateTime: Date;

  // ==================== 关联关系 ====================
  @ManyToOne(() => SysUser, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator: SysUser;

  @ManyToOne(() => SysUser, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: SysUser;

  // ==================== 业务方法 ====================
  
  /**
   * 计算用户EXP经验值
   * @param realMoneyRecharge 真金充值金额 (INR)
   * @param realMoneyFlow 真金流水金额 (INR)
   * @param goldPayment 金币付费金额 (INR)
   * @param goldFlow 金币流水数量
   * @param dailyTaskCount 完成的活跃任务数量
   * @returns 计算得出的EXP经验值
   */
  calculateUserExp(
    realMoneyRecharge: number = 0,
    realMoneyFlow: number = 0,
    goldPayment: number = 0,
    goldFlow: number = 0,
    dailyTaskCount: number = 0
  ): number {
    const rechargeExp = realMoneyRecharge * this.realMoneyRechargeExpRatio;
    const flowExp = realMoneyFlow * this.realMoneyFlowExpRatio;
    const paymentExp = goldPayment * this.goldPaymentExpRatio;
    const goldFlowExp = goldFlow * this.goldFlowExpRatio;
    
    // 活跃任务EXP计算（取中间值）
    const avgTaskExp = (this.dailyTaskExpMin + this.dailyTaskExpMax) / 2;
    const taskExp = dailyTaskCount * avgTaskExp;

    return Math.floor(rechargeExp + flowExp + paymentExp + goldFlowExp + taskExp);
  }

  /**
   * 检查用户是否达到此VIP等级
   * @param userExp 用户当前EXP经验值
   * @returns 是否达到等级要求
   */
  isUserQualified(userExp: number): boolean {
    return userExp >= this.requiredExp;
  }

  /**
   * 获取回购价格显示文本
   * @returns 回购价格显示文本
   */
  getBuybackRateDisplay(): string {
    if (!this.buybackEnabled || !this.buybackRate) {
      return '无资格';
    }
    return `${this.buybackRate}:1`;
  }

  /**
   * 获取提现额度显示文本
   * @returns 提现额度显示文本
   */
  getWithdrawalLimitDisplay(): string {
    if (!this.dailyWithdrawalLimit) {
      return '无上限';
    }
    return `₹${this.dailyWithdrawalLimit.toLocaleString()}`;
  }

  /**
   * 获取提现优先级显示文本
   * @returns 提现优先级显示文本
   */
  getWithdrawalPriorityDisplay(): string {
    const priorityMap = {
      1: '普通',
      2: '优先',
      3: 'VIP专享'
    };
    return priorityMap[this.withdrawalPriority] || '普通';
  }
}
