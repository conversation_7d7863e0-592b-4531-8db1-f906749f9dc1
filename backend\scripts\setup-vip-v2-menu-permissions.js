const { Client } = require('pg');

// 数据库连接配置 - 使用Supabase
const client = new Client({
  host: 'db.ytrftwscazjboxbwnrxp.supabase.co',
  port: 5432,
  database: 'postgres',
  user: 'postgres',
  password: 'inapp2backend2024!',
  ssl: {
    rejectUnauthorized: false
  }
});

async function setupVipV2MenuPermissions() {
  try {
    console.log('🚀 开始配置VIP V2菜单和权限...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 检查并创建配置管理父菜单
    console.log('📋 检查配置管理父菜单...');
    let configParentResult = await client.query(`
      SELECT id FROM sys_menus WHERE title = '配置管理' AND parent_id IS NULL;
    `);

    let configParentId;
    if (configParentResult.rows.length === 0) {
      // 创建配置管理父菜单
      const insertResult = await client.query(`
        INSERT INTO sys_menus (title, path, icon, parent_id, "order", type, status, component, meta, permission_code, menu_type)
        VALUES ('配置管理', '/config', 'SettingOutlined', NULL, 4, 1, 1, NULL,
                '{"icon": "SettingOutlined", "title": "配置管理", "hideInMenu": false}',
                'config', 0)
        RETURNING id;
      `);
      configParentId = insertResult.rows[0].id;
      console.log('✅ 配置管理父菜单创建成功，ID:', configParentId);
    } else {
      configParentId = configParentResult.rows[0].id;
      console.log('✅ 配置管理父菜单已存在，ID:', configParentId);
    }

    // 2. 删除旧的VIP配置菜单（如果存在）
    console.log('🗑️ 删除旧的VIP配置菜单...');
    await client.query(`
      DELETE FROM sys_menus WHERE permission_code = 'config:vip' OR path = '/config/vip';
    `);
    console.log('✅ 旧VIP配置菜单已删除');

    // 3. 创建VIP V2经济权益配置菜单
    console.log('📋 创建VIP V2经济权益配置菜单...');
    const vipV2MenuResult = await client.query(`
      INSERT INTO sys_menus (title, path, icon, parent_id, "order", type, status, component, meta, permission_code, menu_type, button_permissions)
      VALUES ('VIP经济权益配置', '/config/vip', 'CrownOutlined', $1, 1, 1, 1, '/config/vip',
              '{"icon": "CrownOutlined", "title": "VIP经济权益配置", "hideInMenu": false}',
              'config:vip-v2', 1,
              '["config:vip-v2:query", "config:vip-v2:add", "config:vip-v2:edit", "config:vip-v2:delete", "config:vip-v2:calculate", "config:vip-v2:recalculate"]')
      RETURNING id;
    `, [configParentId]);
    
    const vipV2MenuId = vipV2MenuResult.rows[0].id;
    console.log('✅ VIP V2经济权益配置菜单创建成功，ID:', vipV2MenuId);

    // 4. 删除旧的VIP权限
    console.log('🗑️ 删除旧的VIP权限...');
    await client.query(`
      DELETE FROM sys_permissions WHERE code LIKE 'config:vip:%' AND code NOT LIKE 'config:vip-v2:%';
    `);
    console.log('✅ 旧VIP权限已删除');

    // 5. 创建VIP V2相关权限
    console.log('🔐 创建VIP V2权限...');
    const permissions = [
      // VIP V2配置权限
      { name: 'VIP V2配置查询', code: 'config:vip-v2:query', description: '查看VIP V2经济权益配置列表和详情', type: 'button' },
      { name: 'VIP V2配置新增', code: 'config:vip-v2:add', description: '创建新的VIP V2等级配置', type: 'button' },
      { name: 'VIP V2配置编辑', code: 'config:vip-v2:edit', description: '修改VIP V2等级配置信息', type: 'button' },
      { name: 'VIP V2配置删除', code: 'config:vip-v2:delete', description: '删除VIP V2等级配置', type: 'button' },
      { name: 'VIP V2 EXP计算', code: 'config:vip-v2:calculate', description: '计算用户VIP V2 EXP经验值', type: 'button' },
      { name: 'VIP V2等级重算', code: 'config:vip-v2:recalculate', description: '重新计算所有用户VIP V2等级', type: 'button' },
      
      // 配置管理菜单权限
      { name: '配置管理查看', code: 'config', description: '查看配置管理菜单', type: 'menu' },
    ];

    for (const perm of permissions) {
      await client.query(`
        INSERT INTO sys_permissions (name, code, description, type, status)
        VALUES ($1, $2, $3, $4, 1)
        ON CONFLICT (code) DO UPDATE SET
          name = EXCLUDED.name,
          description = EXCLUDED.description,
          type = EXCLUDED.type;
      `, [perm.name, perm.code, perm.description, perm.type]);
    }
    console.log('✅ VIP V2权限创建/更新成功');

    // 6. 为超级管理员角色分配权限
    console.log('👑 为超级管理员分配VIP V2权限...');
    
    // 获取超级管理员角色ID
    const superAdminRole = await client.query(`
      SELECT id FROM sys_roles WHERE code = 'super_admin' OR name = 'super_admin';
    `);

    if (superAdminRole.rows.length > 0) {
      const roleId = superAdminRole.rows[0].id;
      
      // 获取所有VIP V2权限ID
      const vipV2Permissions = await client.query(`
        SELECT id FROM sys_permissions WHERE code LIKE 'config:vip-v2:%' OR code = 'config';
      `);

      for (const perm of vipV2Permissions.rows) {
        await client.query(`
          INSERT INTO sys_role_permissions (role_id, permission_id)
          VALUES ($1, $2)
          ON CONFLICT (role_id, permission_id) DO NOTHING;
        `, [roleId, perm.id]);
      }
      console.log('✅ 超级管理员VIP V2权限分配成功');
    } else {
      console.log('⚠️ 未找到超级管理员角色');
    }

    // 7. 验证配置结果
    console.log('\n📊 验证配置结果...');
    
    // 检查菜单
    const menuCheck = await client.query(`
      SELECT id, title, path, permission_code, parent_id
      FROM sys_menus 
      WHERE title = 'VIP经济权益配置' OR title = '配置管理'
      ORDER BY parent_id NULLS FIRST;
    `);
    
    console.log('菜单配置:');
    menuCheck.rows.forEach(menu => {
      const level = menu.parent_id ? '  └─' : '├─';
      console.log(`${level} ${menu.title} (${menu.permission_code})`);
    });

    // 检查权限
    const permCheck = await client.query(`
      SELECT code, name, type
      FROM sys_permissions 
      WHERE code LIKE 'config:vip-v2:%' OR code = 'config'
      ORDER BY code;
    `);
    
    console.log('\n权限配置:');
    permCheck.rows.forEach(perm => {
      console.log(`- ${perm.code}: ${perm.name} (${perm.type})`);
    });

    console.log('\n🎉 VIP V2菜单和权限配置完成！');

  } catch (error) {
    console.error('❌ 配置过程中发生错误:', error);
    throw error;
  } finally {
    await client.end();
    console.log('📝 数据库连接已关闭');
  }
}

// 执行配置
if (require.main === module) {
  setupVipV2MenuPermissions()
    .then(() => {
      console.log('✅ 配置完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 配置失败:', error);
      process.exit(1);
    });
}

module.exports = { setupVipV2MenuPermissions };
