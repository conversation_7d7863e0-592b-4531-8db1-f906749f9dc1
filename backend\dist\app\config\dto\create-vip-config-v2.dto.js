"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVipConfigDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateVipConfigDto {
    vipLevel;
    levelName;
    strategicPosition;
    requiredExp;
    realMoneyRechargeExpRatio;
    realMoneyFlowExpRatio;
    goldPaymentExpRatio;
    goldFlowExpRatio;
    dailyTaskExpMin;
    dailyTaskExpMax;
    dailyGoldReward;
    buybackEnabled;
    buybackRate;
    c2cFeeRate;
    rakebackRate;
    withdrawalFeeRate;
    dailyWithdrawalLimit;
    withdrawalPriority;
    taskUnlockRequired;
    taskUnlockGamesRequired;
    status;
    remark;
}
exports.CreateVipConfigDto = CreateVipConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP等级，从0开始', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: 'VIP等级不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: 'VIP等级必须是数字' }),
    (0, class_validator_1.Min)(0, { message: 'VIP等级不能小于0' }),
    (0, class_validator_1.Max)(99, { message: 'VIP等级不能大于99' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "vipLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级名称', example: '活跃认证' }),
    (0, class_validator_1.IsNotEmpty)({ message: '等级名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '等级名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 50, { message: '等级名称长度必须在1-50个字符之间' }),
    __metadata("design:type", String)
], CreateVipConfigDto.prototype, "levelName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '核心战略定位描述', example: '活跃认证用户', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '战略定位必须是字符串' }),
    (0, class_validator_1.Length)(0, 200, { message: '战略定位长度不能超过200个字符' }),
    __metadata("design:type", String)
], CreateVipConfigDto.prototype, "strategicPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '达到该等级所需的累计EXP经验值', example: 500 }),
    (0, class_validator_1.IsNotEmpty)({ message: '所需EXP不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '所需EXP必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '所需EXP不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "requiredExp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '真金充值EXP比例 (1 INR = X EXP)', example: 10.0000, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '真金充值EXP比例必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '真金充值EXP比例不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "realMoneyRechargeExpRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '真金流水EXP比例 (1 INR = X EXP)', example: 1.0000, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '真金流水EXP比例必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '真金流水EXP比例不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "realMoneyFlowExpRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币付费EXP比例 (1 INR = X EXP)', example: 5.0000, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '金币付费EXP比例必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '金币付费EXP比例不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "goldPaymentExpRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币流水EXP比例 (X金币 = 1 EXP)', example: 0.001000, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '金币流水EXP比例必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '金币流水EXP比例不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "goldFlowExpRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活跃任务最小EXP', example: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '活跃任务最小EXP必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '活跃任务最小EXP不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "dailyTaskExpMin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活跃任务最大EXP', example: 50, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '活跃任务最大EXP必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '活跃任务最大EXP不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "dailyTaskExpMax", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日可领取金币数量', example: 50, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '每日金币奖励必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '每日金币奖励不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "dailyGoldReward", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否开启官方回购资格', example: false, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '回购资格必须是布尔值' }),
    __metadata("design:type", Boolean)
], CreateVipConfigDto.prototype, "buybackEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '官方回购价格 (金币:1 INR)，如140表示140:1', example: 140, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '回购价格必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '回购价格不能小于1' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "buybackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'C2C手续费率(%)', example: 15.00, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'C2C手续费率必须是数字' }),
    (0, class_validator_1.Min)(0, { message: 'C2C手续费率不能小于0' }),
    (0, class_validator_1.Max)(100, { message: 'C2C手续费率不能大于100' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "c2cFeeRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '周返水比例(%)', example: 0.50, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '返水比例必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '返水比例不能小于0' }),
    (0, class_validator_1.Max)(100, { message: '返水比例不能大于100' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "rakebackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '提现手续费率(%)', example: 5.00, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '提现手续费率必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '提现手续费率不能小于0' }),
    (0, class_validator_1.Max)(100, { message: '提现手续费率不能大于100' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "withdrawalFeeRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日提现额度上限(INR)，null表示无上限', example: 2000, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '提现额度必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '提现额度不能小于1' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "dailyWithdrawalLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '提现优先级 1-普通 2-优先 3-VIP专享', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '提现优先级必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '提现优先级不能小于1' }),
    (0, class_validator_1.Max)(3, { message: '提现优先级不能大于3' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "withdrawalPriority", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活跃任务是否需要游戏局数解锁', example: false, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '任务解锁要求必须是布尔值' }),
    __metadata("design:type", Boolean)
], CreateVipConfigDto.prototype, "taskUnlockRequired", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '解锁所需游戏局数', example: 0, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '解锁游戏局数必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '解锁游戏局数不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "taskUnlockGamesRequired", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注说明', example: '活跃认证用户配置', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注必须是字符串' }),
    (0, class_validator_1.Length)(0, 1000, { message: '备注长度不能超过1000个字符' }),
    __metadata("design:type", String)
], CreateVipConfigDto.prototype, "remark", void 0);
//# sourceMappingURL=create-vip-config-v2.dto.js.map