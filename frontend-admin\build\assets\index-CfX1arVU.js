import{j as t}from"./index-DD0gcXtR.js";import{a}from"./react-BUTTOX-3.js";import{C as O,h as $,b as D,c as L,i as N}from"./index-_Cd-UTps.js";import{ChannelForm as B}from"./channel-form-9bCkcdTV.js";import{aq as M,ao as P,ap as d,I as Q,p as S,Q as m,d as n,u as V,ak as _,az as q,s as h,an as H,aC as J,aO as K}from"./antd-FyCAPQKa.js";const{Search:U}=Q,{Option:G}=S;function te(){const[g,u]=a.useState(!1),[f,j]=a.useState([]),[C,y]=a.useState(0),[r,l]=a.useState({page:1,pageSize:10,sortBy:"createdAt",sortOrder:"DESC"}),[w,i]=a.useState(!1),[k,p]=a.useState("create"),[I,x]=a.useState(null),o=async()=>{u(!0);try{const e=await $(r);e.code===200&&(j(e.result.list),y(e.result.total))}catch{h.error("获取数据失败")}finally{u(!1)}};a.useEffect(()=>{o()},[r]);const z=e=>{l(s=>({...s,search:e,page:1}))},R=(e,s)=>{l(c=>({...c,[e]:s,page:1}))},b=()=>{l({page:1,pageSize:10,sortBy:"createdAt",sortOrder:"DESC"})},E=async e=>{try{(await N(e)).code===200&&(h.success("状态切换成功"),o())}catch(s){h.error(s.message||"状态切换失败")}},T=()=>{p("create"),x(null),i(!0)},v=e=>{p("edit"),x(e),i(!0)},A=()=>{i(!1),o()},F=[{title:"渠道名称",dataIndex:"name",key:"name",width:200,ellipsis:!0},{title:"渠道标识",dataIndex:"identifier",key:"identifier",width:150},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>t.jsx(H,{color:L(e),children:D(e)})},{title:"描述",dataIndex:"description",key:"description",width:200,ellipsis:!0,render:e=>e||"-"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180,render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",width:200,fixed:"right",render:(e,s)=>t.jsxs(m,{size:"small",children:[t.jsx(n,{type:"link",size:"small",icon:t.jsx(J,{}),onClick:()=>v(s),children:"编辑"}),t.jsx(n,{type:"link",size:"small",icon:t.jsx(K,{}),onClick:()=>E(s.id),children:s.status===1?"禁用":"启用"})]})}];return t.jsxs("div",{className:"p-6",children:[t.jsxs(M,{children:[t.jsx("div",{className:"mb-4",children:t.jsxs(P,{gutter:[16,16],children:[t.jsx(d,{span:6,children:t.jsx(U,{placeholder:"搜索渠道名称",allowClear:!0,onSearch:z,style:{width:"100%"}})}),t.jsx(d,{span:4,children:t.jsx(S,{placeholder:"状态",allowClear:!0,style:{width:"100%"},onChange:e=>R("status",e),value:r.status,children:O.map(e=>t.jsx(G,{value:e.value,children:e.label},e.value))})}),t.jsx(d,{span:14,children:t.jsxs(m,{children:[t.jsx(n,{type:"primary",icon:t.jsx(V,{}),onClick:T,children:"新增渠道"}),t.jsx(n,{icon:t.jsx(_,{}),onClick:o,children:"刷新"}),t.jsx(n,{onClick:b,children:"重置"})]})})]})}),t.jsx(q,{columns:F,dataSource:f,rowKey:"id",loading:g,scroll:{x:1e3},pagination:{current:r.page,pageSize:r.pageSize,total:C,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>`第 ${s[0]}-${s[1]} 条/共 ${e} 条`,onChange:(e,s)=>{l(c=>({...c,page:e,pageSize:s}))}}})]}),t.jsx(B,{visible:w,mode:k,initialValues:I,onCancel:()=>i(!1),onSuccess:A})]})}export{te as default};
