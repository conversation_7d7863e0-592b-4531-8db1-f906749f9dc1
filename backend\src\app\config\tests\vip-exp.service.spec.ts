import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { VipExpService } from '../services/vip-exp.service';
import { VipConfig } from '../entities/vip-config-v2.entity';
import { AppUser } from '../../entities/app-user.entity';

describe('VipExpService', () => {
  let service: VipExpService;
  let vipConfigRepository: Repository<VipConfig>;
  let appUserRepository: Repository<AppUser>;

  const mockVipConfigRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
  };

  const mockAppUserRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VipExpService,
        {
          provide: getRepositoryToken(VipConfig),
          useValue: mockVipConfigRepository,
        },
        {
          provide: getRepositoryToken(AppUser),
          useValue: mockAppUserRepository,
        },
      ],
    }).compile();

    service = module.get<VipExpService>(VipExpService);
    vipConfigRepository = module.get<Repository<VipConfig>>(getRepositoryToken(VipConfig));
    appUserRepository = module.get<Repository<AppUser>>(getRepositoryToken(AppUser));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('calculateExpFromActivity', () => {
    const mockConfig = {
      realMoneyRechargeExpRatio: 10.0,
      realMoneyFlowExpRatio: 1.0,
      goldPaymentExpRatio: 5.0,
      goldFlowExpRatio: 0.001,
      dailyTaskExpMin: 10,
      dailyTaskExpMax: 50,
      calculateUserExp: jest.fn(),
    } as any;

    beforeEach(() => {
      mockVipConfigRepository.findOne.mockResolvedValue(mockConfig);
    });

    it('应该正确计算真金充值EXP', async () => {
      const amount = 100; // 100 INR
      const expectedExp = 1000; // 100 * 10

      mockConfig.calculateUserExp.mockReturnValue(expectedExp);

      const result = await service.calculateExpFromActivity('real_money_recharge', amount);

      expect(mockConfig.calculateUserExp).toHaveBeenCalledWith(100, 0, 0, 0, 0);
      expect(result).toBe(expectedExp);
    });

    it('应该正确计算真金流水EXP', async () => {
      const amount = 100; // 100 INR
      const expectedExp = 100; // 100 * 1

      mockConfig.calculateUserExp.mockReturnValue(expectedExp);

      const result = await service.calculateExpFromActivity('real_money_flow', amount);

      expect(mockConfig.calculateUserExp).toHaveBeenCalledWith(0, 100, 0, 0, 0);
      expect(result).toBe(expectedExp);
    });

    it('应该正确计算金币付费EXP', async () => {
      const amount = 100; // 100 INR
      const expectedExp = 500; // 100 * 5

      mockConfig.calculateUserExp.mockReturnValue(expectedExp);

      const result = await service.calculateExpFromActivity('gold_payment', amount);

      expect(mockConfig.calculateUserExp).toHaveBeenCalledWith(0, 0, 100, 0, 0);
      expect(result).toBe(expectedExp);
    });

    it('应该正确计算金币流水EXP', async () => {
      const amount = 1000; // 1000 金币
      const expectedExp = 1; // 1000 * 0.001

      mockConfig.calculateUserExp.mockReturnValue(expectedExp);

      const result = await service.calculateExpFromActivity('gold_flow', amount);

      expect(mockConfig.calculateUserExp).toHaveBeenCalledWith(0, 0, 0, 1000, 0);
      expect(result).toBe(expectedExp);
    });

    it('应该正确计算活跃任务EXP', async () => {
      const amount = 1; // 1次任务
      const expectedExp = 30; // 随机值在10-50之间

      mockConfig.calculateUserExp.mockReturnValue(expectedExp);

      const result = await service.calculateExpFromActivity('daily_task', amount);

      expect(mockConfig.calculateUserExp).toHaveBeenCalledWith(0, 0, 0, 0, 1);
      expect(result).toBe(expectedExp);
    });

    it('当VIP配置不存在时应该抛出异常', async () => {
      mockVipConfigRepository.findOne.mockResolvedValue(null);

      await expect(service.calculateExpFromActivity('real_money_recharge', 100))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('addExp', () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      vipLevel: 1,
      totalExp: 500,
    };

    const mockConfig = {
      calculateUserExp: jest.fn().mockReturnValue(100),
    } as any;

    beforeEach(() => {
      mockAppUserRepository.findOne.mockResolvedValue(mockUser);
      mockVipConfigRepository.findOne.mockResolvedValue(mockConfig);
    });

    it('应该成功为用户添加EXP', async () => {
      const userId = 1;
      const expType = 'real_money_recharge';
      const amount = 100;
      const description = '充值获得EXP';
      const expGained = 1000;

      // Mock calculateExpFromActivity
      jest.spyOn(service, 'calculateExpFromActivity').mockResolvedValue(expGained);
      
      // Mock calculateVipLevelFromExp
      jest.spyOn(service, 'calculateVipLevelFromExp').mockResolvedValue(2);

      const updatedUser = {
        ...mockUser,
        totalExp: mockUser.totalExp + expGained,
        vipLevel: 2,
        lastExpUpdate: expect.any(Date),
      };

      mockAppUserRepository.save.mockResolvedValue(updatedUser);

      const result = await service.addExp(userId, expType, amount, description);

      expect(service.calculateExpFromActivity).toHaveBeenCalledWith(expType, amount);
      expect(service.calculateVipLevelFromExp).toHaveBeenCalledWith(mockUser.totalExp + expGained);
      expect(mockAppUserRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          totalExp: mockUser.totalExp + expGained,
          vipLevel: 2,
        })
      );
      expect(result).toBe(expGained);
    });

    it('当用户不存在时应该抛出异常', async () => {
      const userId = 999;
      mockAppUserRepository.findOne.mockResolvedValue(null);

      await expect(service.addExp(userId, 'real_money_recharge', 100))
        .rejects.toThrow(NotFoundException);
    });

    it('当金额为负数时应该抛出异常', async () => {
      const userId = 1;
      const amount = -100;

      await expect(service.addExp(userId, 'real_money_recharge', amount))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('calculateVipLevelFromExp', () => {
    const mockConfigs = [
      { vipLevel: 0, requiredExp: 0 },
      { vipLevel: 1, requiredExp: 500 },
      { vipLevel: 2, requiredExp: 2000 },
      { vipLevel: 3, requiredExp: 10000 },
    ];

    beforeEach(() => {
      mockVipConfigRepository.find.mockResolvedValue(mockConfigs);
    });

    it('应该返回正确的VIP等级 - 等级0', async () => {
      const totalExp = 100;
      const result = await service.calculateVipLevelFromExp(totalExp);
      expect(result).toBe(0);
    });

    it('应该返回正确的VIP等级 - 等级1', async () => {
      const totalExp = 1000;
      const result = await service.calculateVipLevelFromExp(totalExp);
      expect(result).toBe(1);
    });

    it('应该返回正确的VIP等级 - 等级2', async () => {
      const totalExp = 5000;
      const result = await service.calculateVipLevelFromExp(totalExp);
      expect(result).toBe(2);
    });

    it('应该返回最高等级', async () => {
      const totalExp = 50000;
      const result = await service.calculateVipLevelFromExp(totalExp);
      expect(result).toBe(3);
    });

    it('当EXP为0时应该返回等级0', async () => {
      const totalExp = 0;
      const result = await service.calculateVipLevelFromExp(totalExp);
      expect(result).toBe(0);
    });
  });

  describe('getUserVipBenefits', () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      vipLevel: 1,
      totalExp: 1000,
    };

    const mockConfig = {
      vipLevel: 1,
      levelName: '活跃认证',
      requiredExp: 500,
      dailyGoldReward: 50,
      buybackEnabled: false,
      buybackRate: null,
      c2cFeeRate: 15.00,
      rakebackRate: 0.00,
      withdrawalFeeRate: 5.00,
      dailyWithdrawalLimit: 2000,
      withdrawalPriority: 1,
    };

    beforeEach(() => {
      mockAppUserRepository.findOne.mockResolvedValue(mockUser);
      mockVipConfigRepository.findOne.mockResolvedValue(mockConfig);
    });

    it('应该返回用户VIP权益详情', async () => {
      const userId = 1;
      const result = await service.getUserVipBenefits(userId);

      expect(result).toEqual({
        userId: mockUser.id,
        username: mockUser.username,
        vipLevel: mockUser.vipLevel,
        totalExp: mockUser.totalExp,
        levelName: mockConfig.levelName,
        requiredExp: mockConfig.requiredExp,
        benefits: {
          dailyGoldReward: mockConfig.dailyGoldReward,
          buybackEnabled: mockConfig.buybackEnabled,
          buybackRate: mockConfig.buybackRate,
          c2cFeeRate: mockConfig.c2cFeeRate,
          rakebackRate: mockConfig.rakebackRate,
          withdrawalFeeRate: mockConfig.withdrawalFeeRate,
          dailyWithdrawalLimit: mockConfig.dailyWithdrawalLimit,
          withdrawalPriority: mockConfig.withdrawalPriority,
        },
      });
    });

    it('当用户不存在时应该抛出异常', async () => {
      const userId = 999;
      mockAppUserRepository.findOne.mockResolvedValue(null);

      await expect(service.getUserVipBenefits(userId))
        .rejects.toThrow(NotFoundException);
    });

    it('当VIP配置不存在时应该抛出异常', async () => {
      const userId = 1;
      mockVipConfigRepository.findOne.mockResolvedValue(null);

      await expect(service.getUserVipBenefits(userId))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('recalculateAllUserVipLevels', () => {
    const mockUsers = [
      { id: 1, totalExp: 100, vipLevel: 0 },
      { id: 2, totalExp: 1000, vipLevel: 0 },
      { id: 3, totalExp: 5000, vipLevel: 1 },
    ];

    beforeEach(() => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockUsers, mockUsers.length]),
      };
      mockAppUserRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      
      // Mock calculateVipLevelFromExp
      jest.spyOn(service, 'calculateVipLevelFromExp')
        .mockResolvedValueOnce(0) // user 1: 100 exp -> level 0
        .mockResolvedValueOnce(1) // user 2: 1000 exp -> level 1  
        .mockResolvedValueOnce(2); // user 3: 5000 exp -> level 2
    });

    it('应该成功重新计算所有用户VIP等级', async () => {
      mockAppUserRepository.save
        .mockResolvedValueOnce({ ...mockUsers[0], vipLevel: 0 })
        .mockResolvedValueOnce({ ...mockUsers[1], vipLevel: 1 })
        .mockResolvedValueOnce({ ...mockUsers[2], vipLevel: 2 });

      const result = await service.recalculateAllUserVipLevels();

      expect(result).toEqual({
        processed: 3,
        upgraded: 2, // user 2 and user 3 upgraded
        downgraded: 0,
        errors: 0,
      });

      expect(mockAppUserRepository.save).toHaveBeenCalledTimes(2); // Only users with level changes
    });

    it('应该处理降级情况', async () => {
      // Mock users with higher levels than their EXP should allow
      const usersWithHighLevels = [
        { id: 1, totalExp: 100, vipLevel: 2 }, // Should be level 0
        { id: 2, totalExp: 1000, vipLevel: 3 }, // Should be level 1
      ];

      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([usersWithHighLevels, usersWithHighLevels.length]),
      };
      mockAppUserRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      jest.spyOn(service, 'calculateVipLevelFromExp')
        .mockResolvedValueOnce(0) // user 1: should be level 0
        .mockResolvedValueOnce(1); // user 2: should be level 1

      mockAppUserRepository.save
        .mockResolvedValueOnce({ ...usersWithHighLevels[0], vipLevel: 0 })
        .mockResolvedValueOnce({ ...usersWithHighLevels[1], vipLevel: 1 });

      const result = await service.recalculateAllUserVipLevels();

      expect(result).toEqual({
        processed: 2,
        upgraded: 0,
        downgraded: 2, // Both users downgraded
        errors: 0,
      });
    });
  });
});
