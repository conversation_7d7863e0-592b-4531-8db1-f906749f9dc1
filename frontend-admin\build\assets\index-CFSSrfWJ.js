import{j as t}from"./index-DD0gcXtR.js";import{a as s}from"./react-BUTTOX-3.js";import{C as $,A as F,g as B,a as M,b as Q,c as V,d as q,e as J,t as H}from"./index-_Cd-UTps.js";import{AdForm as K}from"./ad-form-CWRYX0wk.js";import{aq as U,ao as Y,ap as l,I as G,p as c,Q as f,d as n,u as W,ak as X,az as Z,s as p,an as j,aC as ee,aO as te}from"./antd-FyCAPQKa.js";const{Search:ae}=G,{Option:x}=c;function ie(){const[w,g]=s.useState(!1),[C,I]=s.useState([]),[k,A]=s.useState(0),[S,D]=s.useState([]),[r,o]=s.useState({page:1,pageSize:10,sortBy:"createdAt",sortOrder:"DESC"}),[T,i]=s.useState(!1),[v,m]=s.useState("create"),[L,y]=s.useState(null),b=async()=>{try{const e=await B();e.code===200&&D(e.result)}catch(e){console.error("获取渠道列表失败:",e)}},d=async()=>{g(!0);try{console.log("[AD_LIST] 发送查询参数:",JSON.stringify(r,null,2));const e=await M(r);console.log("[AD_LIST] API响应:",e),e.code===200&&(I(e.result.list),A(e.result.total))}catch(e){console.error("[AD_LIST] API调用失败:",e),p.error("获取数据失败")}finally{g(!1)}};s.useEffect(()=>{b()},[]),s.useEffect(()=>{d()},[r]);const E=e=>{o(a=>({...a,search:e,page:1}))},h=(e,a)=>{o(u=>({...u,[e]:a,page:1}))},z=()=>{o({page:1,pageSize:10,sortBy:"createdAt",sortOrder:"DESC"})},O=async e=>{try{(await H(e)).code===200&&(p.success("状态切换成功"),d())}catch(a){p.error(a.message||"状态切换失败")}},R=()=>{m("create"),y(null),i(!0)},_=e=>{m("edit"),y(e),i(!0)},N=()=>{i(!1),d()},P=[{title:"广告名称",dataIndex:"name",key:"name",width:200,ellipsis:!0},{title:"广告标识",dataIndex:"identifier",key:"identifier",width:150},{title:"所属渠道",dataIndex:"channel",key:"channel",width:150,render:e=>(e==null?void 0:e.name)||"-"},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>t.jsx(j,{color:V(e),children:Q(e)})},{title:"广告类型",dataIndex:"adType",key:"adType",width:120,render:e=>t.jsx(j,{color:J(e),children:q(e)})},{title:"推荐人ID",dataIndex:"referrerId",key:"referrerId",width:100,render:e=>e||"-"},{title:"预算",dataIndex:"budget",key:"budget",width:120,render:e=>e?`¥${e.toLocaleString()}`:"-"},{title:"开始日期",dataIndex:"startDate",key:"startDate",width:120,render:e=>e?new Date(e).toLocaleDateString():"-"},{title:"结束日期",dataIndex:"endDate",key:"endDate",width:120,render:e=>e?new Date(e).toLocaleDateString():"-"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:180,render:e=>new Date(e).toLocaleString()},{title:"操作",key:"action",width:200,fixed:"right",render:(e,a)=>t.jsxs(f,{size:"small",children:[t.jsx(n,{type:"link",size:"small",icon:t.jsx(ee,{}),onClick:()=>_(a),children:"编辑"}),t.jsx(n,{type:"link",size:"small",icon:t.jsx(te,{}),onClick:()=>O(a.id),children:a.status===1?"禁用":"启用"})]})}];return t.jsxs("div",{className:"p-6",children:[t.jsxs(U,{children:[t.jsx("div",{className:"mb-4",children:t.jsxs(Y,{gutter:[16,16],children:[t.jsx(l,{span:5,children:t.jsx(ae,{placeholder:"搜索广告名称",allowClear:!0,onSearch:E,style:{width:"100%"}})}),t.jsx(l,{span:4,children:t.jsx(c,{placeholder:"所属渠道",allowClear:!0,style:{width:"100%"},onChange:e=>h("channelId",e),value:r.channelId,children:S.map(e=>t.jsx(x,{value:e.id,children:e.name},e.id))})}),t.jsx(l,{span:3,children:t.jsx(c,{placeholder:"状态",allowClear:!0,style:{width:"100%"},onChange:e=>h("status",e),value:r.status,children:$.map(e=>t.jsx(x,{value:e.value,children:e.label},e.value))})}),t.jsx(l,{span:3,children:t.jsx(c,{placeholder:"广告类型",allowClear:!0,style:{width:"100%"},onChange:e=>h("adType",e),value:r.adType,children:F.map(e=>t.jsx(x,{value:e.value,children:e.label},e.value))})}),t.jsx(l,{span:9,children:t.jsxs(f,{children:[t.jsx(n,{type:"primary",icon:t.jsx(W,{}),onClick:R,children:"新增广告"}),t.jsx(n,{icon:t.jsx(X,{}),onClick:d,children:"刷新"}),t.jsx(n,{onClick:z,children:"重置"})]})})]})}),t.jsx(Z,{columns:P,dataSource:C,rowKey:"id",loading:w,scroll:{x:1400},pagination:{current:r.page,pageSize:r.pageSize,total:k,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,a)=>`第 ${a[0]}-${a[1]} 条/共 ${e} 条`,onChange:(e,a)=>{o(u=>({...u,page:e,pageSize:a}))}}})]}),t.jsx(K,{visible:T,mode:v,initialValues:L,channels:S,onCancel:()=>i(!1),onSuccess:N})]})}export{ie as default};
