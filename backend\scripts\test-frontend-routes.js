// 测试前端路由API响应的脚本
const https = require('https');
const http = require('http');

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    if (options.data) {
      const postData = JSON.stringify(options.data);
      requestOptions.headers['Content-Type'] = 'application/json';
      requestOptions.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (options.data) {
      req.write(JSON.stringify(options.data));
    }

    req.end();
  });
}

async function testFrontendRoutes() {
  try {
    console.log('🔍 测试前端路由API...');

    // 首先登录获取token
    console.log('\n1. 登录获取token...');

    // 尝试不同的密码
    const passwords = ['123456', 'admin123', 'admin', 'password'];
    let loginResponse = null;

    for (const password of passwords) {
      console.log(`尝试密码: ${password}`);
      try {
        loginResponse = await makeRequest('http://localhost:3000/api/auth/login', {
          method: 'POST',
          data: {
            username: 'admin',
            password: password
          }
        });

        if (loginResponse.data.code === 200) {
          console.log(`✅ 密码正确: ${password}`);
          console.log('登录响应详情:', JSON.stringify(loginResponse.data, null, 2));
          break;
        } else {
          console.log(`❌ 密码错误: ${password} - ${loginResponse.data.message}`);
          loginResponse = null;
        }
      } catch (error) {
        console.log(`❌ 登录请求失败: ${password} - ${error.message}`);
        loginResponse = null;
      }
    }

    if (!loginResponse || loginResponse.data.code !== 200) {
      throw new Error('所有密码都尝试失败，无法登录');
    }

    const token = loginResponse.data.result.token;
    console.log('✅ 登录成功，token:', token ? token.substring(0, 20) + '...' : 'N/A');

    // 测试路由API
    console.log('\n2. 请求路由API...');
    const routesResponse = await makeRequest('http://localhost:3000/api/routes', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('路由API响应状态:', routesResponse.status);
    console.log('路由API响应数据:');
    console.log(JSON.stringify(routesResponse.data, null, 2));
    
    // 检查VIP路由
    console.log('\n3. 检查VIP相关路由...');
    const routes = routesResponse.data.result || [];
    
    function findVipRoutes(routes, path = '') {
      const vipRoutes = [];
      
      routes.forEach(route => {
        const fullPath = path + route.path;
        
        if (route.path.includes('vip') || route.handle?.title?.includes('VIP')) {
          vipRoutes.push({
            path: fullPath,
            component: route.component,
            title: route.handle?.title,
            permissions: route.handle?.permissions
          });
        }
        
        if (route.children && route.children.length > 0) {
          vipRoutes.push(...findVipRoutes(route.children, fullPath));
        }
      });
      
      return vipRoutes;
    }
    
    const vipRoutes = findVipRoutes(routes);
    
    if (vipRoutes.length > 0) {
      console.log('✅ 找到VIP路由:');
      vipRoutes.forEach(route => {
        console.log(`  路径: ${route.path}`);
        console.log(`  组件: ${route.component || 'N/A'}`);
        console.log(`  标题: ${route.title || 'N/A'}`);
        console.log(`  权限: ${route.permissions ? route.permissions.join(', ') : 'N/A'}`);
        console.log('');
      });
    } else {
      console.log('❌ 未找到VIP路由');
    }
    
    // 测试用户权限
    console.log('\n4. 检查用户权限...');
    const permissionsResponse = await makeRequest('http://localhost:3000/api/routes/permissions', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('用户权限:', permissionsResponse.data.result);
    
    // 检查是否有VIP相关权限
    const permissions = permissionsResponse.data.result || [];
    const vipPermissions = permissions.filter(p => p.includes('vip'));
    
    if (vipPermissions.length > 0) {
      console.log('✅ 找到VIP权限:', vipPermissions);
    } else {
      console.log('❌ 未找到VIP权限');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 执行测试
if (require.main === module) {
  testFrontendRoutes()
    .then(() => {
      console.log('\n✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testFrontendRoutes };
