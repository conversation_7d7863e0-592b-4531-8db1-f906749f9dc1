import{j as e}from"./index-DD0gcXtR.js";import{a as x}from"./react-BUTTOX-3.js";import{C as f,j,k as g}from"./index-_Cd-UTps.js";import{av as t,M as F,I as l,p as i,s as c}from"./antd-FyCAPQKa.js";const{TextArea:C}=l,{Option:I}=i;function O({visible:n,mode:o,initialValues:r,onCancel:u,onSuccess:d}){const[a]=t.useForm();x.useEffect(()=>{n&&(o==="edit"&&r?a.setFieldsValue({name:r.name,identifier:r.identifier,status:r.status,description:r.description}):(a.resetFields(),a.setFieldsValue({status:1})))},[n,o,r,a]);const p=async()=>{try{const s=await a.validateFields();if(o==="create")(await j(s)).code===200&&(c.success("创建成功"),d());else{const m=s;(await g(r.id,m)).code===200&&(c.success("更新成功"),d())}}catch(s){if(s.errorFields)return;c.error(s.message||`${o==="create"?"创建":"更新"}失败`)}};return e.jsx(F,{title:o==="create"?"新增渠道":"编辑渠道",open:n,onCancel:u,onOk:p,width:600,destroyOnClose:!0,children:e.jsxs(t,{form:a,layout:"vertical",preserve:!1,children:[e.jsx(t.Item,{name:"name",label:"渠道名称",rules:[{required:!0,message:"请输入渠道名称"},{max:100,message:"渠道名称不能超过100个字符"}],children:e.jsx(l,{placeholder:"请输入渠道名称，如：Facebook Ads"})}),e.jsx(t.Item,{name:"identifier",label:"渠道标识",rules:[{required:!0,message:"请输入渠道标识"},{max:50,message:"渠道标识不能超过50个字符"},{pattern:/^[a-zA-Z0-9_-]+$/,message:"渠道标识只能包含字母、数字、下划线和连字符"}],children:e.jsx(l,{placeholder:"请输入渠道标识，如：facebook"})}),e.jsx(t.Item,{name:"status",label:"状态",rules:[{required:!0,message:"请选择状态"}],children:e.jsx(i,{placeholder:"请选择状态",children:f.map(s=>e.jsx(I,{value:s.value,children:s.label},s.value))})}),e.jsx(t.Item,{name:"description",label:"描述",rules:[{max:500,message:"描述不能超过500个字符"}],children:e.jsx(C,{rows:4,placeholder:"请输入渠道描述（可选）",showCount:!0,maxLength:500})})]})})}export{O as ChannelForm};
