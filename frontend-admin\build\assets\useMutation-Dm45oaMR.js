var E=e=>{throw TypeError(e)};var C=(e,t,s)=>t.has(e)||E("Cannot "+s);var i=(e,t,s)=>(C(e,t,"read from private field"),s?s.call(e):t.get(e)),b=(e,t,s)=>t.has(e)?E("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),p=(e,t,s,r)=>(C(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s),f=(e,t,s)=>(C(e,t,"access private method"),s);import{a as m}from"./react-BUTTOX-3.js";import{S as w,U as k,V as K,W as q,X as U,Y as L}from"./index-DD0gcXtR.js";var a,l,n,o,u,g,M,R,j=(R=class extends w{constructor(t,s){super();b(this,u);b(this,a);b(this,l);b(this,n);b(this,o);p(this,a,t),this.setOptions(s),this.bindMethods(),f(this,u,g).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var r;const s=this.options;this.options=i(this,a).defaultMutationOptions(t),k(this.options,s)||i(this,a).getMutationCache().notify({type:"observerOptionsUpdated",mutation:i(this,n),observer:this}),s!=null&&s.mutationKey&&this.options.mutationKey&&K(s.mutationKey)!==K(this.options.mutationKey)?this.reset():((r=i(this,n))==null?void 0:r.state.status)==="pending"&&i(this,n).setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(t=i(this,n))==null||t.removeObserver(this)}onMutationUpdate(t){f(this,u,g).call(this),f(this,u,M).call(this,t)}getCurrentResult(){return i(this,l)}reset(){var t;(t=i(this,n))==null||t.removeObserver(this),p(this,n,void 0),f(this,u,g).call(this),f(this,u,M).call(this)}mutate(t,s){var r;return p(this,o,s),(r=i(this,n))==null||r.removeObserver(this),p(this,n,i(this,a).getMutationCache().build(i(this,a),this.options)),i(this,n).addObserver(this),i(this,n).execute(t)}},a=new WeakMap,l=new WeakMap,n=new WeakMap,o=new WeakMap,u=new WeakSet,g=function(){var s;const t=((s=i(this,n))==null?void 0:s.state)??q();p(this,l,{...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset})},M=function(t){U.batch(()=>{var s,r,h,v,c,y,S,x;if(i(this,o)&&this.hasListeners()){const d=i(this,l).variables,O=i(this,l).context;(t==null?void 0:t.type)==="success"?((r=(s=i(this,o)).onSuccess)==null||r.call(s,t.data,d,O),(v=(h=i(this,o)).onSettled)==null||v.call(h,t.data,null,d,O)):(t==null?void 0:t.type)==="error"&&((y=(c=i(this,o)).onError)==null||y.call(c,t.error,d,O),(x=(S=i(this,o)).onSettled)==null||x.call(S,void 0,t.error,d,O))}this.listeners.forEach(d=>{d(i(this,l))})})},R);function A(e,t){return typeof e=="function"?e(...t):!!e}function D(){}function T(e,t){const s=L(),[r]=m.useState(()=>new j(s,e));m.useEffect(()=>{r.setOptions(e)},[r,e]);const h=m.useSyncExternalStore(m.useCallback(c=>r.subscribe(U.batchCalls(c)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),v=m.useCallback((c,y)=>{r.mutate(c,y).catch(D)},[r]);if(h.error&&A(r.options.throwOnError,[h.error]))throw h.error;return{...h,mutate:v,mutateAsync:h.mutate}}export{D as n,A as s,T as u};
