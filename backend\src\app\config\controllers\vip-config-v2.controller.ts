import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SystemJwtAuthGuard } from '../../../system/auth/guards/jwt-auth.guard';
import { VipConfigV2Service } from '../services/vip-config-v2.service';
import { CreateVipConfigDto } from '../dto/create-vip-config-v2.dto';
import { UpdateVipConfigDto } from '../dto/update-vip-config-v2.dto';
import { VipConfigQueryDto, VipExpAddDto } from '../dto/vip-config-query-v2.dto';

@ApiTags('VIP经济权益配置管理 V12.0')
@ApiBearerAuth()
@UseGuards(SystemJwtAuthGuard)
@Controller('config/vip-v2')
export class VipConfigV2Controller {
  constructor(private readonly vipConfigService: VipConfigV2Service) {}

  @Post()
  @ApiOperation({ summary: '创建VIP配置' })
  @ApiResponse({ status: 201, description: '创建成功' })
  @ApiResponse({ status: 409, description: 'VIP等级已存在' })
  async create(@Body() createVipConfigDto: CreateVipConfigDto, @Request() req) {
    const result = await this.vipConfigService.create(createVipConfigDto, req.user.id);
    return {
      code: 200,
      message: '创建成功',
      result,
    };
  }

  @Get()
  @ApiOperation({ summary: '分页查询VIP配置列表' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async findAll(@Query() query: VipConfigQueryDto) {
    const result = await this.vipConfigService.findAll(query);
    return {
      code: 200,
      message: '查询成功',
      result,
    };
  }

  @Get('comparison')
  @ApiOperation({ summary: '获取VIP等级权益对比表' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getVipLevelComparison() {
    const result = await this.vipConfigService.getVipLevelComparison();
    return {
      code: 200,
      message: '查询成功',
      result,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询VIP配置详情' })
  @ApiResponse({ status: 200, description: '查询成功' })
  @ApiResponse({ status: 404, description: 'VIP配置不存在' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const result = await this.vipConfigService.findOne(id);
    return {
      code: 200,
      message: '查询成功',
      result,
    };
  }

  @Get('level/:vipLevel')
  @ApiOperation({ summary: '根据VIP等级查询配置' })
  @ApiResponse({ status: 200, description: '查询成功' })
  @ApiResponse({ status: 404, description: 'VIP配置不存在' })
  async findByLevel(@Param('vipLevel', ParseIntPipe) vipLevel: number) {
    const result = await this.vipConfigService.findByLevel(vipLevel);
    return {
      code: 200,
      message: '查询成功',
      result,
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新VIP配置' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: 'VIP配置不存在' })
  @ApiResponse({ status: 409, description: 'VIP等级已存在' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateVipConfigDto: UpdateVipConfigDto,
    @Request() req,
  ) {
    const result = await this.vipConfigService.update(id, updateVipConfigDto, req.user.id);
    return {
      code: 200,
      message: '更新成功',
      result,
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除VIP配置' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: 'VIP配置不存在' })
  @ApiResponse({ status: 400, description: '无法删除，有用户正在使用此等级' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    const result = await this.vipConfigService.remove(id);
    return {
      code: 200,
      message: '删除成功',
      result,
    };
  }

  // ==================== 用户VIP相关接口 ====================

  @Get('user/:userId/benefits')
  @ApiOperation({ summary: '获取用户VIP权益详情' })
  @ApiResponse({ status: 200, description: '查询成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getUserVipBenefits(@Param('userId', ParseIntPipe) userId: number) {
    const result = await this.vipConfigService.getUserVipBenefits(userId);
    return {
      code: 200,
      message: '查询成功',
      result,
    };
  }

  @Post('user/:userId/add-exp')
  @ApiOperation({ summary: '为用户添加EXP经验值' })
  @ApiResponse({ status: 200, description: '添加成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async addUserExp(
    @Param('userId', ParseIntPipe) userId: number,
    @Body() addExpDto: VipExpAddDto,
  ) {
    const expGained = await this.vipConfigService.addUserExp(
      userId,
      addExpDto.expType,
      addExpDto.amount,
      addExpDto.description,
    );
    return {
      code: 200,
      message: 'EXP添加成功',
      result: {
        expGained,
        expType: addExpDto.expType,
        amount: addExpDto.amount,
      },
    };
  }

  @Post('recalculate-all')
  @ApiOperation({ summary: '重新计算所有用户的VIP等级' })
  @ApiResponse({ status: 200, description: '重新计算完成' })
  async recalculateAllUserVipLevels() {
    const result = await this.vipConfigService.recalculateAllUserVipLevels();
    return {
      code: 200,
      message: '重新计算完成',
      result,
    };
  }

  // ==================== 数据统计接口 ====================

  @Get('stats/overview')
  @ApiOperation({ summary: '获取VIP系统统计概览' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getVipStatsOverview() {
    // TODO: 实现VIP系统统计功能
    // 包括各等级用户数量、EXP分布、权益使用情况等
    return {
      code: 200,
      message: '查询成功',
      result: {
        totalConfigs: 0,
        totalUsers: 0,
        levelDistribution: [],
        expDistribution: [],
      },
    };
  }

  @Get('stats/level-distribution')
  @ApiOperation({ summary: '获取用户VIP等级分布统计' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getVipLevelDistribution() {
    // TODO: 实现等级分布统计
    return {
      code: 200,
      message: '查询成功',
      result: [],
    };
  }

  // ==================== 权益验证接口 ====================

  @Get('user/:userId/check-buyback')
  @ApiOperation({ summary: '检查用户回购权限' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async checkUserBuybackPermission(@Param('userId', ParseIntPipe) userId: number) {
    const benefits = await this.vipConfigService.getUserVipBenefits(userId);
    return {
      code: 200,
      message: '查询成功',
      result: {
        hasPermission: benefits.benefits.buybackEnabled,
        buybackRate: benefits.benefits.buybackRate,
        vipLevel: benefits.vipLevel,
      },
    };
  }

  @Get('user/:userId/check-withdrawal')
  @ApiOperation({ summary: '检查用户提现权限' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async checkUserWithdrawalPermission(@Param('userId', ParseIntPipe) userId: number) {
    const benefits = await this.vipConfigService.getUserVipBenefits(userId);
    return {
      code: 200,
      message: '查询成功',
      result: {
        feeRate: benefits.benefits.withdrawalFeeRate,
        dailyLimit: benefits.benefits.dailyWithdrawalLimit,
        priority: benefits.benefits.withdrawalPriority,
        vipLevel: benefits.vipLevel,
      },
    };
  }

  @Get('user/:userId/check-c2c')
  @ApiOperation({ summary: '检查用户C2C交易权限' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async checkUserC2CPermission(@Param('userId', ParseIntPipe) userId: number) {
    const benefits = await this.vipConfigService.getUserVipBenefits(userId);
    return {
      code: 200,
      message: '查询成功',
      result: {
        feeRate: benefits.benefits.c2cFeeRate,
        vipLevel: benefits.vipLevel,
      },
    };
  }
}
