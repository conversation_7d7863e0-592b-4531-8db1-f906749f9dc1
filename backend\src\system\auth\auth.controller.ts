import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  Get,
  Patch,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SystemAuthService } from './auth.service';
import { SystemLocalAuthGuard } from './guards/local-auth.guard';
import { SystemJwtAuthGuard } from './guards/jwt-auth.guard';
import { SystemLoginDto } from './dto/login.dto';
import { SystemRefreshTokenDto } from './dto/refresh-token.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

@ApiTags('系统认证')
@Controller('system-auth')
export class SystemAuthController {
  constructor(private authService: SystemAuthService) {}

  @UseGuards(SystemLocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '管理员登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 401, description: '用户名或密码错误' })
  async login(@Request() req, @Body() loginDto: SystemLoginDto) {
    const startTime = Date.now();
    console.log(`[AUTH_CONTROLLER] 收到登录请求 - 用户名: ${loginDto.username}`);

    try {
      const result = await this.authService.login(req.user);
      const endTime = Date.now();
      console.log(`[AUTH_CONTROLLER] 登录成功 - 用户名: ${loginDto.username}, 耗时: ${endTime - startTime}ms`);

      return {
        code: 200,
        message: '登录成功',
        result,
      };
    } catch (error) {
      const endTime = Date.now();
      console.error(`[AUTH_CONTROLLER] 登录失败 - 用户名: ${loginDto.username}, 耗时: ${endTime - startTime}ms, 错误:`, error);
      throw error;
    }
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '管理员登出' })
  @ApiResponse({ status: 200, description: '登出成功' })
  async logout() {
    return {
      code: 200,
      message: '登出成功',
      result: {},
    };
  }

  @Post('refresh-token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '刷新令牌' })
  @ApiResponse({ status: 200, description: '刷新成功' })
  @ApiResponse({ status: 401, description: '刷新令牌无效' })
  async refreshToken(@Body() refreshTokenDto: SystemRefreshTokenDto) {
    const result = await this.authService.refreshToken(
      refreshTokenDto.refreshToken,
    );
    return {
      code: 200,
      message: '刷新成功',
      result,
    };
  }

  @UseGuards(SystemJwtAuthGuard)
  @Get('profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前管理员信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getProfile(@Request() req) {
    const result = await this.authService.getUserInfo(req.user.userId);
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @UseGuards(SystemJwtAuthGuard)
  @Patch('profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新当前管理员信息' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateProfile(@Request() req, @Body() updateData: any) {
    const startTime = Date.now();
    console.log(`[AUTH] 开始更新用户信息 - 用户ID: ${req.user.userId}, 数据:`, updateData);

    try {
      const result = await this.authService.updateUserInfo(req.user.userId, updateData);
      const endTime = Date.now();
      console.log(`[AUTH] 更新用户信息成功 - 耗时: ${endTime - startTime}ms`);

      return {
        code: 200,
        message: '更新成功',
        result,
      };
    } catch (error) {
      const endTime = Date.now();
      console.error(`[AUTH] 更新用户信息失败 - 耗时: ${endTime - startTime}ms, 错误:`, error);
      throw error;
    }
  }

  @UseGuards(SystemJwtAuthGuard)
  @Post('change-password')
  @ApiBearerAuth()
  @ApiOperation({ summary: '修改密码' })
  @ApiResponse({ status: 200, description: '密码修改成功' })
  @ApiResponse({ status: 401, description: '当前密码错误' })
  @ApiResponse({ status: 400, description: '密码格式不正确' })
  async changePassword(@Request() req, @Body() changePasswordDto: ChangePasswordDto) {
    const { currentPassword, newPassword, confirmPassword } = changePasswordDto;

    // 验证新密码和确认密码是否一致
    if (newPassword !== confirmPassword) {
      return {
        code: 400,
        message: '新密码和确认密码不一致',
      };
    }

    try {
      const result = await this.authService.changePassword(
        req.user.userId,
        currentPassword,
        newPassword,
      );

      return {
        code: 200,
        message: '密码修改成功',
        result,
      };
    } catch (error) {
      console.error(`[AUTH] 修改密码失败 - 用户ID: ${req.user.userId}, 错误:`, error);
      throw error;
    }
  }

  // 临时的密码重置接口，仅用于开发调试
  @Post('reset-admin-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '重置admin密码（仅开发环境）' })
  async resetAdminPassword(@Body() body: { newPassword: string }) {
    console.log(`[AUTH_CONTROLLER] 重置admin密码请求`);
    const result = await this.authService.resetAdminPassword(body.newPassword);
    return {
      code: 200,
      message: result.message,
    };
  }
}
