// 为admin用户设置密码
const { DataSource } = require('typeorm');
const bcrypt = require('bcryptjs');
const { SysUser } = require('../dist/system/entities/sys-user.entity');
const { SysRole } = require('../dist/system/entities/sys-role.entity');
const { SysPermission } = require('../dist/system/entities/sys-permission.entity');
const { SysMenu } = require('../dist/system/entities/sys-menu.entity');

async function setAdminPassword() {
  const dataSource = new DataSource({
    type: 'postgres',
    host: '**************',
    port: 5435,
    username: 'user_jJSpPW',
    password: 'password_DmrhYX',
    database: 'inapp2',
    entities: [SysUser, SysRole, SysPermission, SysMenu],
    synchronize: false,
    logging: false,
  });

  try {
    await dataSource.initialize();
    console.log('🔗 数据库连接成功');

    const userRepository = dataSource.getRepository(SysUser);
    
    // 查找admin用户
    const adminUser = await userRepository.findOne({
      where: { username: 'admin' }
    });
    
    if (!adminUser) {
      console.log('❌ 没有找到admin用户');
      return;
    }

    console.log('✅ 找到admin用户');
    console.log(`  当前密码哈希: ${adminUser.password || '无密码'}`);

    // 生成新密码哈希
    const newPassword = 'admin123';
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    console.log(`\n🔐 生成新密码哈希:`);
    console.log(`  明文密码: ${newPassword}`);
    console.log(`  哈希密码: ${hashedPassword}`);

    // 更新用户密码
    await userRepository.update(adminUser.id, {
      password: hashedPassword
    });

    console.log('\n✅ 密码更新成功');

    // 验证更新
    const updatedUser = await userRepository.findOne({
      where: { username: 'admin' }
    });
    
    console.log('\n📋 验证更新结果:');
    console.log(`  用户名: ${updatedUser.username}`);
    if (updatedUser.password) {
      console.log(`  新密码哈希: ${updatedUser.password.substring(0, 20)}...`);

      // 验证密码是否正确
      const isPasswordValid = await bcrypt.compare(newPassword, updatedUser.password);
      console.log(`  密码验证: ${isPasswordValid ? '✅ 正确' : '❌ 错误'}`);
    } else {
      console.log(`  新密码哈希: 无密码`);
    }

    await dataSource.destroy();
    console.log('\n🎉 admin用户密码设置完成！');
    console.log(`   用户名: admin`);
    console.log(`   密码: ${newPassword}`);

  } catch (error) {
    console.error('❌ 设置密码失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 执行设置
if (require.main === module) {
  setAdminPassword()
    .then(() => {
      console.log('✅ 设置完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 设置失败:', error);
      process.exit(1);
    });
}

module.exports = { setAdminPassword };
