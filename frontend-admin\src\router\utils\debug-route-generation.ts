import type { AppRouteRecordRaw } from "#src/router/types";

/**
 * 调试路由生成过程
 */
export function debugRouteGeneration(backendRoutes: Array<AppRouteRecordRaw>) {
  console.log('🔍 [DEBUG] 后端路由数据:', JSON.stringify(backendRoutes, null, 2));
  
  // 查找VIP相关路由
  const findVipRoute = (routes: Array<AppRouteRecordRaw>, level = 0): any => {
    const indent = '  '.repeat(level);
    
    for (const route of routes) {
      console.log(`${indent}路由: ${route.path}`);
      console.log(`${indent}  标题: ${route.handle?.title}`);
      console.log(`${indent}  组件: ${route.component || '无'}`);
      
      if (route.path === '/config/vip' || route.handle?.title === 'VIP配置') {
        console.log(`🎯 [DEBUG] 找到VIP路由:`, {
          path: route.path,
          title: route.handle?.title,
          component: route.component,
          handle: route.handle
        });
        return route;
      }
      
      if (route.children?.length) {
        const found = findVipRoute(route.children, level + 1);
        if (found) return found;
      }
    }
    return null;
  };
  
  const vipRoute = findVipRoute(backendRoutes);
  
  if (vipRoute) {
    console.log('✅ [DEBUG] VIP路由找到');
  } else {
    console.log('❌ [DEBUG] VIP路由未找到');
  }
  
  return vipRoute;
}
