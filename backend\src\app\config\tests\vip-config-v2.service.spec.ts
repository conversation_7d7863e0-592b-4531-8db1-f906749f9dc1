import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { VipConfigService } from '../services/vip-config-v2.service';
import { VipExpService } from '../services/vip-exp.service';
import { VipConfig } from '../entities/vip-config-v2.entity';
import { AppUser } from '../../entities/app-user.entity';

describe('VipConfigService V2', () => {
  let service: VipConfigService;
  let vipConfigRepository: Repository<VipConfig>;
  let appUserRepository: Repository<AppUser>;
  let vipExpService: VipExpService;

  const mockVipConfigRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    count: jest.fn(),
  };

  const mockAppUserRepository = {
    count: jest.fn(),
  };

  const mockVipExpService = {
    getUserVipBenefits: jest.fn(),
    addExp: jest.fn(),
    recalculateAllUserVipLevels: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VipConfigService,
        {
          provide: getRepositoryToken(VipConfig),
          useValue: mockVipConfigRepository,
        },
        {
          provide: getRepositoryToken(AppUser),
          useValue: mockAppUserRepository,
        },
        {
          provide: VipExpService,
          useValue: mockVipExpService,
        },
      ],
    }).compile();

    service = module.get<VipConfigService>(VipConfigService);
    vipConfigRepository = module.get<Repository<VipConfig>>(getRepositoryToken(VipConfig));
    appUserRepository = module.get<Repository<AppUser>>(getRepositoryToken(AppUser));
    vipExpService = module.get<VipExpService>(VipExpService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createDto = {
      vipLevel: 1,
      levelName: '活跃认证',
      strategicPosition: '活跃认证用户',
      requiredExp: 500,
      dailyGoldReward: 50,
      buybackEnabled: false,
      c2cFeeRate: 15.00,
      rakebackRate: 0.00,
      withdrawalFeeRate: 5.00,
      dailyWithdrawalLimit: 2000,
      withdrawalPriority: 1,
      taskUnlockRequired: false,
      taskUnlockGamesRequired: 0,
      status: 1,
    };

    it('应该成功创建VIP配置', async () => {
      const userId = 1;
      const savedConfig = { id: 1, ...createDto, createdBy: userId, updatedBy: userId };

      mockVipConfigRepository.findOne.mockResolvedValue(null); // 不存在冲突
      mockVipConfigRepository.create.mockReturnValue(savedConfig);
      mockVipConfigRepository.save.mockResolvedValue(savedConfig);

      const result = await service.create(createDto, userId);

      expect(mockVipConfigRepository.findOne).toHaveBeenCalledWith({
        where: { vipLevel: createDto.vipLevel },
      });
      expect(mockVipConfigRepository.create).toHaveBeenCalledWith({
        ...createDto,
        createdBy: userId,
        updatedBy: userId,
      });
      expect(mockVipConfigRepository.save).toHaveBeenCalledWith(savedConfig);
      expect(result).toEqual(savedConfig);
    });

    it('当VIP等级已存在时应该抛出冲突异常', async () => {
      const userId = 1;
      const existingConfig = { id: 1, vipLevel: 1 };

      mockVipConfigRepository.findOne.mockResolvedValue(existingConfig);

      await expect(service.create(createDto, userId)).rejects.toThrow(ConflictException);
      expect(mockVipConfigRepository.findOne).toHaveBeenCalledWith({
        where: { vipLevel: createDto.vipLevel },
      });
    });

    it('当业务规则验证失败时应该抛出异常', async () => {
      const userId = 1;
      const invalidDto = {
        ...createDto,
        buybackEnabled: true,
        buybackRate: null, // 开启回购但未设置价格
      };

      mockVipConfigRepository.findOne.mockResolvedValue(null);

      await expect(service.create(invalidDto, userId)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    it('应该返回分页的VIP配置列表', async () => {
      const query = { page: 1, pageSize: 10, vipLevel: 1 };
      const mockConfigs = [
        { id: 1, vipLevel: 1, levelName: '活跃认证' },
        { id: 2, vipLevel: 2, levelName: '信任用户' },
      ];
      const total = 2;

      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockConfigs, total]),
      };

      mockVipConfigRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.findAll(query);

      expect(result).toEqual({
        list: mockConfigs,
        total,
        page: 1,
        pageSize: 10,
        totalPages: 1,
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('vip.vipLevel = :vipLevel', { vipLevel: 1 });
    });
  });

  describe('findOne', () => {
    it('应该返回指定ID的VIP配置', async () => {
      const id = 1;
      const mockConfig = { id, vipLevel: 1, levelName: '活跃认证' };

      mockVipConfigRepository.findOne.mockResolvedValue(mockConfig);

      const result = await service.findOne(id);

      expect(mockVipConfigRepository.findOne).toHaveBeenCalledWith({
        where: { id },
        relations: ['creator', 'updater'],
      });
      expect(result).toEqual(mockConfig);
    });

    it('当VIP配置不存在时应该抛出异常', async () => {
      const id = 999;

      mockVipConfigRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(id)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findByLevel', () => {
    it('应该返回指定等级的VIP配置', async () => {
      const vipLevel = 1;
      const mockConfig = { id: 1, vipLevel, levelName: '活跃认证', status: 1 };

      mockVipConfigRepository.findOne.mockResolvedValue(mockConfig);

      const result = await service.findByLevel(vipLevel);

      expect(mockVipConfigRepository.findOne).toHaveBeenCalledWith({
        where: { vipLevel, status: 1 },
      });
      expect(result).toEqual(mockConfig);
    });

    it('当指定等级配置不存在时应该抛出异常', async () => {
      const vipLevel = 999;

      mockVipConfigRepository.findOne.mockResolvedValue(null);

      await expect(service.findByLevel(vipLevel)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    const updateDto = {
      levelName: '更新的活跃认证',
      requiredExp: 600,
    };

    it('应该成功更新VIP配置', async () => {
      const id = 1;
      const userId = 1;
      const existingConfig = { id, vipLevel: 1, levelName: '活跃认证', requiredExp: 500 };
      const updatedConfig = { ...existingConfig, ...updateDto, updatedBy: userId };

      mockVipConfigRepository.findOne
        .mockResolvedValueOnce(existingConfig) // findOne call
        .mockResolvedValueOnce(updatedConfig); // findOne call after update
      mockVipConfigRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.update(id, updateDto, userId);

      expect(mockVipConfigRepository.update).toHaveBeenCalledWith(id, {
        ...updateDto,
        updatedBy: userId,
      });
      expect(result).toEqual(updatedConfig);
    });

    it('当更新不存在的配置时应该抛出异常', async () => {
      const id = 999;
      const userId = 1;

      mockVipConfigRepository.findOne.mockResolvedValue(null);

      await expect(service.update(id, updateDto, userId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('应该成功删除VIP配置', async () => {
      const id = 1;
      const mockConfig = { id, vipLevel: 1, levelName: '活跃认证' };

      mockVipConfigRepository.findOne.mockResolvedValue(mockConfig);
      mockAppUserRepository.count.mockResolvedValue(0); // 没有用户使用此等级
      mockVipConfigRepository.remove.mockResolvedValue(mockConfig);

      const result = await service.remove(id);

      expect(mockAppUserRepository.count).toHaveBeenCalledWith({
        where: { vipLevel: mockConfig.vipLevel },
      });
      expect(mockVipConfigRepository.remove).toHaveBeenCalledWith(mockConfig);
      expect(result).toEqual({ message: '删除成功' });
    });

    it('当有用户使用此等级时应该抛出异常', async () => {
      const id = 1;
      const mockConfig = { id, vipLevel: 1, levelName: '活跃认证' };

      mockVipConfigRepository.findOne.mockResolvedValue(mockConfig);
      mockAppUserRepository.count.mockResolvedValue(5); // 有5个用户使用此等级

      await expect(service.remove(id)).rejects.toThrow(BadRequestException);
      expect(mockVipConfigRepository.remove).not.toHaveBeenCalled();
    });
  });

  describe('getUserVipBenefits', () => {
    it('应该返回用户VIP权益详情', async () => {
      const userId = 1;
      const mockBenefits = {
        vipLevel: 1,
        levelName: '活跃认证',
        totalExp: 600,
        benefits: {
          dailyGoldReward: 50,
          buybackEnabled: false,
          c2cFeeRate: 15.00,
        },
      };

      mockVipExpService.getUserVipBenefits.mockResolvedValue(mockBenefits);

      const result = await service.getUserVipBenefits(userId);

      expect(mockVipExpService.getUserVipBenefits).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockBenefits);
    });
  });

  describe('addUserExp', () => {
    it('应该成功为用户添加EXP', async () => {
      const userId = 1;
      const expType = 'real_money_recharge';
      const amount = 100;
      const description = '充值获得EXP';
      const expGained = 1000;

      mockVipExpService.addExp.mockResolvedValue(expGained);

      const result = await service.addUserExp(userId, expType, amount, description);

      expect(mockVipExpService.addExp).toHaveBeenCalledWith(userId, expType, amount, description);
      expect(result).toEqual(expGained);
    });
  });

  describe('recalculateAllUserVipLevels', () => {
    it('应该成功重新计算所有用户VIP等级', async () => {
      const mockResult = { processed: 100, upgraded: 15, downgraded: 3 };

      mockVipExpService.recalculateAllUserVipLevels.mockResolvedValue(mockResult);

      const result = await service.recalculateAllUserVipLevels();

      expect(mockVipExpService.recalculateAllUserVipLevels).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });
  });

  describe('getVipLevelComparison', () => {
    it('应该返回VIP等级权益对比数据', async () => {
      const mockConfigs = [
        {
          vipLevel: 0,
          levelName: '体验用户',
          strategicPosition: '体验用户/流量基础',
          requiredExp: 0,
          dailyGoldReward: 0,
          buybackEnabled: false,
          buybackRate: null,
          c2cFeeRate: 15.00,
          rakebackRate: 0.00,
          withdrawalFeeRate: null,
          dailyWithdrawalLimit: null,
          withdrawalPriority: 1,
          getBuybackRateDisplay: jest.fn().mockReturnValue('无资格'),
          getWithdrawalLimitDisplay: jest.fn().mockReturnValue('无资格'),
          getWithdrawalPriorityDisplay: jest.fn().mockReturnValue('普通'),
        },
      ];

      mockVipConfigRepository.find.mockResolvedValue(mockConfigs);

      const result = await service.getVipLevelComparison();

      expect(mockVipConfigRepository.find).toHaveBeenCalledWith({
        where: { status: 1 },
        order: { vipLevel: 'ASC' },
      });
      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        vipLevel: 0,
        levelName: '体验用户',
        strategicPosition: '体验用户/流量基础',
        requiredExp: 0,
        dailyGoldReward: 0,
        c2cFeeRate: '15%',
        rakebackRate: '0%',
      });
    });
  });
});
