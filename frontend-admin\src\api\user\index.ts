import type { PasswordLoginFormType } from "#src/pages/login/components/password-login";
import type { AppRouteRecordRaw } from "#src/router/types";
import type { AuthType, UserInfoType } from "./types";
import { request } from "#src/utils";

export * from "./types";

export function fetchLogin(data: PasswordLoginFormType) {
	return request
		.post("system-auth/login", {
			json: data,
			retry: { limit: 0 }, // 禁用重试，避免登录失败时重复请求
			timeout: 30000 // 增加超时时间到30秒，因为数据库查询可能较慢
		})
		.json<ApiResponse<AuthType>>();
}

export function fetchLogout() {
	return request.post("system-auth/logout").json();
}

export function fetchAsyncRoutes() {
	return request.get("routes").json<ApiResponse<AppRouteRecordRaw[]>>();
}

export function fetchUserInfo() {
	return request.get("system-auth/profile").json<ApiResponse<UserInfoType>>();
}

export function fetchUpdateUserInfo(data: Partial<UserInfoType>) {
	return request.patch("system-auth/profile", { json: data }).json<ApiResponse<UserInfoType>>();
}

export interface ChangePasswordFormType {
	currentPassword: string
	newPassword: string
	confirmPassword: string
}

export function fetchChangePassword(data: ChangePasswordFormType) {
	return request.post("system-auth/change-password", {
		json: data,
		retry: { limit: 0 } // 完全禁用重试，避免密码错误时重复请求
	}).json<ApiResponse<{ message: string }>>();
}

export interface RefreshTokenResult {
	token: string
	refreshToken: string
}

export const refreshTokenPath = "system-auth/refresh-token";
export function fetchRefreshToken(data: { readonly refreshToken: string }) {
	return request.post(refreshTokenPath, { json: data }).json<ApiResponse<RefreshTokenResult>>();
}
