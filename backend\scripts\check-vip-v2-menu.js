const { Client } = require('pg');

async function checkVipV2Menu() {
  const client = new Client({
    host: 'aws-0-ap-southeast-1.pooler.supabase.com',
    port: 6543,
    database: 'postgres',
    user: 'postgres.ytrftwscazjboxbwnrxp',
    password: 'DmrhYX_user_jJSpPW',
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    await client.connect();
    console.log('🔗 数据库连接成功');

    // 查询所有菜单
    console.log('\n📋 查询所有菜单:');
    const menusResult = await client.query(`
      SELECT id, name, path, parent_id, type, sort_order, is_enabled 
      FROM sys_menu 
      ORDER BY parent_id NULLS FIRST, sort_order
    `);
    
    console.log('菜单总数:', menusResult.rows.length);
    menusResult.rows.forEach(menu => {
      const indent = menu.parent_id ? '  ' : '';
      console.log(`${indent}${menu.id}: ${menu.name} (${menu.path}) - ${menu.type} - enabled: ${menu.is_enabled}`);
    });

    // 查询VIP V2相关权限
    console.log('\n🔑 查询VIP V2相关权限:');
    const permissionsResult = await client.query(`
      SELECT id, name, code, type 
      FROM sys_permission 
      WHERE code LIKE '%vip-v2%'
      ORDER BY code
    `);
    
    console.log('VIP V2权限数量:', permissionsResult.rows.length);
    permissionsResult.rows.forEach(perm => {
      console.log(`  ${perm.id}: ${perm.name} (${perm.code}) - ${perm.type}`);
    });

    // 查询角色权限关联
    console.log('\n👥 查询超级管理员角色的VIP V2权限:');
    const rolePermissionsResult = await client.query(`
      SELECT r.name as role_name, p.name as permission_name, p.code as permission_code
      FROM sys_role r
      JOIN sys_role_permission rp ON r.id = rp.role_id
      JOIN sys_permission p ON rp.permission_id = p.id
      WHERE r.name = '系统管理员' AND p.code LIKE '%vip-v2%'
      ORDER BY p.code
    `);
    
    console.log('超级管理员VIP V2权限数量:', rolePermissionsResult.rows.length);
    rolePermissionsResult.rows.forEach(rp => {
      console.log(`  ${rp.role_name}: ${rp.permission_name} (${rp.permission_code})`);
    });

  } catch (error) {
    console.error('❌ 数据库操作失败:', error.message);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行检查
if (require.main === module) {
  checkVipV2Menu()
    .then(() => {
      console.log('✅ 检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 检查失败:', error);
      process.exit(1);
    });
}

module.exports = { checkVipV2Menu };
